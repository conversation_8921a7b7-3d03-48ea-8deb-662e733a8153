2025/08/21 00:23:22.112 ; Info ; 87 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:23:22.126 ; <PERSON> ; 87 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:23:22.150 ; Trace ; 87 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:23:22.155 ; Log ; 87 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:23:22.157 ; Log ; 87 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:23:22.157 ; Log ; 87 ;  ; 0000: ; 21/8/2025 00:23:22 @Http Req#0
2025/08/21 00:23:22.158 ; Log ; 87 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:23:22.158 ; Log ; 87 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:23:22.158 ; Log ; 87 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:23:22.166 ; Log ; 87 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:23:22.170 ; Log ; 87 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:23:22.233 ; Log ; 87 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:23:22.233 ; Log ; 87 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:23:22.277 ; Info ; 87 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:23:22.302 ; Log ; 87 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:23:22.304 ; Log ; 87 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:23:22.309 ; Trace ; 87 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:23:22.324 ; Trace ; 87 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:23:22.324 ; Trace ; 87 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:23:22.325 ; Trace ; 87 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:23:22.327 ; Trace ; 87 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:23:22.332 ; Info ; 87 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:23:22.384 ; Log ; 87 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:23:22.387 ; Log ; 87 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:23:22.434 ; Log ; 87 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:23:22.434 ; Log ; 87 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:23:22.435 ; Log ; 87 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:23:22.438 ; Log ; 87 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:23:22.491 ; Trace ; 87 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:23:22.493 ; Trace ; 87 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:23:22.596 ; Log ; 87 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:23:22.596 ; Log ; 87 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:23:22.596 ; Log ; 87 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:23:22.597 ; Log ; 87 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:23:22.597 ; Log ; 87 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:23:22.597 ; Log ; 87 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:23:22.598 ; Log ; 87 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:23:27.599 ; Log ; 96 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#0
2025/08/21 00:23:27.599 ; Log ; 95 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#0
2025/08/21 00:23:27.606 ; Log ; 95 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#0
2025/08/21 00:23:29.479 ; Trace ; 92 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:23:29.487 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:23:29.487 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:23:29.505 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:23:29.540 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:23:29.541 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:23:29.544 ; Log ; 92 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:23:29.560 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:23:29.705 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:23:29.838 ; Trace ; 92 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:23:30.336 ; Trace ; 92 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:23:30.347 ; Info ; 108 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.366 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.367 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.371 ; Trace ; 108 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.385 ; Trace ; 108 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.385 ; Trace ; 108 ; ::1 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.385 ; Trace ; 108 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.386 ; Trace ; 108 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.388 ; Trace ; 108 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.390 ; Log ; 108 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.414 ; Log ; 108 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.414 ; Trace ; 108 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.424 ; Trace ; 108 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.427 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.427 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.430 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.430 ; Info ; 108 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:37.829 ; Trace ; 101 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#5 @Req#4 0s
2025/08/21 00:23:37.833 ; Log ; 101 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#5 @Req#4 0s
2025/08/21 00:23:37.833 ; Trace ; 101 ; ::1 ; 9900:admin ; Redirecting user to: /app/?fi-act&cmd=init @Http Req#5 @Req#4 0s
2025/08/21 00:23:37.842 ; Info ; 94 ; ::1 ; 9900:admin ;  ;  ; /app/?fi-act&cmd=init ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:23:37.898 ; Info ; 106 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:23:41.694 ; Info ; 106 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:23:41.907 ; Trace ; 106 ; ::1 ; 9900:admin ; UserClientSideCache: Add key=acc_data_list @Http Req#8 @Req#7 0s
2025/08/21 00:23:46.797 ; Dev ; 37 ; ::1 ; 9900:admin ; >> Form is cached: hr-wd-def @Http Req#9 @Req#8 0s
2025/08/21 00:23:46.798 ; Info ; 37 ; ::1 ; 9900:admin ; hr-wd-def ;  ; /app/fms/?fm=hr-wd-def&cmd=init ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:23:52.120 ; Log ; 109 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-users ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-users&cmd=view&id=admin @Req#9 0s ; #at:UserError
2025/08/21 00:24:19.936 ; Info ; 90 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt&sind=y ; ::1 @Req#10 0s
2025/08/21 00:24:21.657 ; Info ; 90 ; ::1 ; 9900:admin ; sys-cfg ; es-sys-fi-gl ; /app/fms/?fm=sys-cfg&id=es-sys-fi-gl ; ::1 @Req#11 0s
2025/08/21 00:24:24.045 ; Info ; 110 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#12 0s
2025/08/21 00:24:27.632 ; Dev ; 95 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:24:32.998 ; Dev ; 108 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Req#13 0s
2025/08/21 00:24:32.998 ; Info ; 108 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#13 0s
2025/08/21 00:25:18.057 ; Trace ; 91 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#16 0s
2025/08/21 00:25:18.061 ; Log ; 91 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#16 0s
2025/08/21 00:25:18.061 ; Trace ; 91 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#16 0s
2025/08/21 00:25:18.071 ; Info ; 101 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#17 0s
2025/08/21 00:25:18.129 ; Info ; 6 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#18 0s
2025/08/21 00:25:27.635 ; Dev ; 95 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:26:27.665 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:26:27.679 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:26:27.799 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:26:27.804 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:26:27 @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:26:27.807 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:26:27.809 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 00:26:27.812 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:26:27.814 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:26:27.923 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:26:27.926 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:26:27.984 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:26:28.011 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:26:28.016 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:26:28.027 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:26:28.045 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:26:28.045 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:26:28.046 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:26:28.052 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:26:28.059 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:26:28.119 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:26:28.138 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:26:28.227 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:26:28.235 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:26:28.248 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:26:28.271 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:26:28.379 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:26:28.379 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:26:28.379 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:26:28.381 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:26:28.381 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:26:28.387 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:26:28.391 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:26:28.596 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:26:28.596 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:26:28.597 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:26:28.597 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:26:28.597 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:26:28.598 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:26:28.599 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:26:28.630 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:26:28.635 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:26:28.635 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:26:28.678 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:26:28.711 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:26:28.712 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:26:28.716 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:26:28.725 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:26:28.960 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:26:29.238 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:26:30.076 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:26:30.116 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.132 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.133 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.139 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.159 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.161 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.162 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.165 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.170 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.175 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.209 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.212 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.234 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.237 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.237 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.244 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.244 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:33.600 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#4
2025/08/21 00:26:33.600 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#4
2025/08/21 00:26:33.603 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#4
2025/08/21 00:26:40.114 ; Log ; 18 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Http Req#5 @Req#4 0s ; #at:UserError
2025/08/21 00:26:45.113 ; Log ; 10 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=list @Http Req#6 @Req#5 0s ; #at:UserError
2025/08/21 00:26:55.883 ; Trace ; 22 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#7 @Req#6 0s
2025/08/21 00:26:55.889 ; Log ; 22 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#7 @Req#6 0s
2025/08/21 00:26:55.889 ; Trace ; 22 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#7 @Req#6 0s
2025/08/21 00:26:55.901 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:26:55.949 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:26:58.662 ; Dev ; 14 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Req#9 0s
2025/08/21 00:26:58.662 ; Info ; 14 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=list ; ::1 @Req#9 0s
2025/08/21 00:27:23.534 ; Log ; 11 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hr-e ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hr-e @Req#10 0s ; #at:UserError
2025/08/21 00:27:33.626 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:28:06.218 ; Dev ; 24 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#11 0s
2025/08/21 00:28:06.218 ; Info ; 24 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=list ; ::1 @Req#11 0s
2025/08/21 00:28:11.618 ; Dev ; 9 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#12 0s
2025/08/21 00:28:11.618 ; Info ; 9 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#12 0s
2025/08/21 00:28:33.632 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:28:36.406 ; Dev ; 18 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#13 0s
2025/08/21 00:28:36.406 ; Info ; 18 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#13 0s
2025/08/21 00:28:36.418 ; Error ; 18 ; ::1 ; 9900:admin ;  حقل ( تاريخ التوظيف ) مطلوب <br/>قيمة حقل ( الأجر ) غير مناسبة <br/> @Req#13 0s ; #at:UserError
2025/08/21 00:28:42.034 ; Dev ; 18 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#14 0s
2025/08/21 00:28:42.034 ; Info ; 18 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#14 0s
2025/08/21 00:28:42.037 ; Error ; 18 ; ::1 ; 9900:admin ; قيمة حقل ( الأجر ) غير مناسبة <br/> @Req#14 0s ; #at:UserError
2025/08/21 00:28:49.978 ; Dev ; 16 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#15 0s
2025/08/21 00:28:49.978 ; Info ; 16 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#15 0s
2025/08/21 00:28:50.063 ; Trace ; 16 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =acc_data_list @Req#15 0s
2025/08/21 00:28:50.075 ; Dev ; 17 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#16 0s
2025/08/21 00:28:50.075 ; Info ; 17 ; ::1 ; 9900:admin ; hr-emp ; 8003 ; /app/fms/?fm=hr-emp&cmd=view&id=8003 ; ::1 @Req#16 0s
2025/08/21 00:29:08.503 ; Log ; 38 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#17 0s ; #at:UserError
2025/08/21 00:29:17.229 ; Log ; 17 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#18 0s ; #at:UserError
2025/08/21 00:29:23.752 ; Log ; 18 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#19 0s ; #at:UserError
2025/08/21 00:29:30.051 ; Log ; 27 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#20 0s ; #at:UserError
2025/08/21 00:29:33.633 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:29:36.180 ; Log ; 34 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#21 0s ; #at:UserError
2025/08/21 00:29:42.538 ; Log ; 27 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=list @Req#22 0s ; #at:UserError
2025/08/21 00:29:48.260 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#23 0s
2025/08/21 00:30:27.823 ; Trace ; 37 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#26 0s
2025/08/21 00:30:27.828 ; Log ; 37 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#26 0s
2025/08/21 00:30:27.828 ; Trace ; 37 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#26 0s
2025/08/21 00:30:27.838 ; Info ; 31 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#27 0s
2025/08/21 00:30:27.894 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#28 0s
2025/08/21 00:30:33.635 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:31:19.936 ; Info ; 28 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#29 0s
2025/08/21 00:31:26.220 ; Info ; 15 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=list-tables&sind=y ; ::1 @Req#30 0s
2025/08/21 00:31:33.636 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:31:53.692 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:31:53.711 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:31:53.826 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:31:53.832 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:31:53.834 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:31:53.834 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:31:53 @Http Req#0
2025/08/21 00:31:53.835 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:31:53.836 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:31:53.836 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:31:53.837 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 00:31:53.841 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:31:53.842 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:31:53.948 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:31:53.950 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:31:54.016 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:31:54.048 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:31:54.055 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:31:54.068 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:31:54.088 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:31:54.088 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:31:54.089 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:31:54.095 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:31:54.103 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:31:54.180 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:31:54.201 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:31:54.317 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:31:54.324 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:31:54.337 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:31:54.354 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:31:54.478 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:31:54.479 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:31:54.479 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:31:54.480 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:31:54.481 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:31:54.486 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:31:54.490 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:31:54.721 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:31:54.721 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:31:54.722 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:31:54.722 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:31:54.722 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:31:54.723 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:31:54.724 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:31:54.796 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:31:54.804 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:31:54.804 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:31:54.830 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:31:54.875 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:31:54.875 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:31:54.880 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:31:54.881 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:31:55.035 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:31:55.161 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:31:55.583 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:31:55.592 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.605 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.606 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.608 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.620 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.620 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.620 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.622 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.623 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.625 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.648 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.648 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.659 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.662 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.662 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.665 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.665 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:59.725 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 00:31:59.727 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 00:31:59.730 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 00:32:01.467 ; Trace ; 11 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:01.472 ; Log ; 11 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:01.473 ; Trace ; 11 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:32:01.486 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:32:01.546 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:32:08.497 ; Trace ; 14 ;  ; 9900: ; Session End: User logged out @Http Req#5
2025/08/21 00:32:08.499 ; Log ; 14 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#5
2025/08/21 00:32:08.507 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#5
2025/08/21 00:32:08.507 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#5
2025/08/21 00:32:08.509 ; Log ; 14 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#5

2025/08/21 00:32:30.823 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:32:30.837 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:32:30.961 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:32:30 @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:32:30.964 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:32:30.964 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:32:30.977 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:32:30.981 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:32:31.046 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:32:31.047 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:32:31.095 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:32:31.110 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:32:31.114 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:32:31.123 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:32:31.144 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:32:31.145 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:32:31.145 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:32:31.150 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:32:31.159 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:32:31.224 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:32:31.228 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:32:31.261 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:32:31.261 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:32:31.261 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:32:31.263 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:32:31.297 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:32:31.297 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:32:31.298 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:32:31.299 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:32:31.301 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:32:31.307 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:32:31.311 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:32:31.418 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:32:31.418 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:32:31.419 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:32:31.483 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:32:31.489 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:32:31.489 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:32:31.516 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:32:31.560 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:32:31.560 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:32:31.564 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:32:31.574 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:32:31.746 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:32:31.895 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:32:32.345 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:32:32.354 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.368 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.369 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.373 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.387 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.387 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.387 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.389 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.391 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.393 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.414 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.414 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.423 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.425 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.425 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.428 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.428 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:36.317 ; Trace ; 9 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:36.322 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:36.322 ; Trace ; 9 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:32:36.331 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:32:36.388 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:32:36.419 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#5
2025/08/21 00:32:36.420 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#5
2025/08/21 00:32:36.419 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#5
2025/08/21 00:32:41.510 ; Dev ; 12 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Http Req#6 @Req#5 0s
2025/08/21 00:32:41.510 ; Info ; 12 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:32:46.691 ; Dev ; 22 ; ::1 ; 9900:admin ; >> Form is cached: hr-wd-def @Http Req#7 @Req#6 0s
2025/08/21 00:32:46.691 ; Info ; 22 ; ::1 ; 9900:admin ; hr-wd-def ;  ; /app/fms/?fm=hr-wd-def&cmd=list ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:32:46.981 ; Trace ; 22 ; ::1 ; 9900:admin ; UserClientSideCache: Add key=acc_data_list @Http Req#7 @Req#6 0s
2025/08/21 00:33:35.505 ; Info ; 17 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:33:36.437 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#8

2025/08/21 00:33:39.801 ; Info ; 16 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:33:39.868 ; Dev ; 16 ; ::1 ; 9900:admin ; GL: OnSystemConfigurationChanged: sys-cfg : hr-sys @Http Req#9 @Req#8 0s
2025/08/21 00:33:39.869 ; Trace ; 16 ; ::1 ; 9900:admin ; Config cache refreshed: hcm-cfg @Http Req#9 @Req#8 0s
2025/08/21 00:34:29.094 ; Info ; 28 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#9 0s
2025/08/21 00:34:36.445 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:35:02.802 ; Log ; 28 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=list @Req#10 0s ; #at:UserError
2025/08/21 00:35:07.318 ; Log ; 21 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-config ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-config&cmd=list @Req#11 0s ; #at:UserError
2025/08/21 00:35:10.938 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#12 0s
2025/08/21 00:35:12.603 ; Log ; 11 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-modules ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-modules&cmd=list @Req#13 0s ; #at:UserError
2025/08/21 00:35:18.275 ; Log ; 12 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-users ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-users&cmd=view&id=admin @Req#14 0s ; #at:UserError
2025/08/21 00:35:23.285 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-cfg ; ::1 @Req#15 0s
2025/08/21 00:35:29.859 ; Info ; 15 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-admin ; ::1 @Req#16 0s
2025/08/21 00:35:34.240 ; Log ; 14 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs- ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs- @Req#17 0s ; #at:UserError
2025/08/21 00:35:36.446 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:35:41.085 ; Log ; 11 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-sys-modules ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-sys-modules&cmd=list @Req#18 0s ; #at:UserError
2025/08/21 00:35:46.416 ; Log ; 19 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-app-modules ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-app-modules&cmd=list @Req#19 0s ; #at:UserError
2025/08/21 00:36:08.192 ; Info ; 14 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-admin ; ::1 @Req#20 0s
2025/08/21 00:36:11.350 ; Dev ; 19 ; ::1 ; 9900:admin ; >> Form is cached: user @Req#21 0s
2025/08/21 00:36:11.350 ; Info ; 19 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Req#21 0s
2025/08/21 00:36:13.060 ; Info ; 11 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-cfg ; ::1 @Req#22 0s
2025/08/21 00:36:14.724 ; Info ; 9 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Req#23 0s
2025/08/21 00:36:18.833 ; Info ; 21 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db ; ::1 @Req#24 0s
2025/08/21 00:36:36.447 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:37:21.417 ; Trace ; 17 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =user-menu @Req#25 0s
2025/08/21 00:37:27.211 ; Info ; 21 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#25 0s
2025/08/21 00:37:29.531 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#26 0s
2025/08/21 00:37:31.848 ; Info ; 23 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#27 0s
2025/08/21 00:37:36.448 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:37:50.266 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-cp ; ::1 @Req#28 0s
2025/08/21 00:38:00.956 ; Dev ; 17 ; ::1 ; 9900:admin ; >> Form is cached: coding @Req#29 0s
2025/08/21 00:38:00.956 ; Info ; 17 ; ::1 ; 9900:admin ; coding ;  ; /app/fms/?fm=coding&fk0=d-module&cmd=list&sind=y ; ::1 @Req#29 0s
2025/08/21 00:38:36.449 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:39:36.450 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:40:36.452 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:41:36.453 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:42:36.455 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:43:36.249 ; Dev ; 20 ; ::1 ; 9900:admin ; >> Form is cached: auth-role @Req#30 0s
2025/08/21 00:43:36.249 ; Info ; 20 ; ::1 ; 9900:admin ; auth-role ;  ; /app/fms/?fm=auth-role&sind=y ; ::1 @Req#30 0s
2025/08/21 00:43:36.457 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:43:49.335 ; Info ; 15 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&sind=y ; ::1 @Req#31 0s
2025/08/21 00:43:53.784 ; Info ; 28 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#32 0s
2025/08/21 00:43:57.378 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app/?restart ; ::1 @Req#33 0s
2025/08/21 00:43:57.380 ; Log ; 10 ; ::1 ; 9900:admin ; System Restart:  @Req#33 0s
2025/08/21 00:43:57.409 ; Trace ; 29 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:43:57.410 ; Log ; 29 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 00:43:57.410 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 00:43:57.410 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 00:43:57.419 ; Log ; 29 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 00:43:57.494 ; Info ; 37 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:43:57.512 ; Dev ; 37 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:43:57.536 ; Trace ; 37 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:43:57.537 ; Log ; 37 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:43:57.537 ; Log ; 37 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:43:57.537 ; Log ; 37 ;  ; 0000: ; 21/8/2025 00:43:57 @Http Req#0
2025/08/21 00:43:57.537 ; Log ; 37 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:43:57.537 ; Log ; 37 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:43:57.537 ; Log ; 37 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:43:57.543 ; Log ; 37 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:43:57.546 ; Log ; 37 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:43:57.611 ; Log ; 37 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:43:57.612 ; Log ; 37 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:43:57.664 ; Info ; 37 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:43:57.678 ; Log ; 37 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:43:57.681 ; Log ; 37 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:43:57.689 ; Trace ; 37 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:43:57.702 ; Trace ; 37 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:43:57.703 ; Trace ; 37 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:43:57.703 ; Trace ; 37 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:43:57.706 ; Trace ; 37 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:43:57.712 ; Info ; 37 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:43:57.783 ; Log ; 37 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:43:57.787 ; Log ; 37 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:43:57.829 ; Log ; 37 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:43:57.829 ; Log ; 37 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:43:57.829 ; Log ; 37 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:43:57.832 ; Log ; 37 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:43:57.882 ; Trace ; 37 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:43:57.883 ; Log ; 37 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:43:57.883 ; Log ; 37 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:43:57.885 ; Log ; 37 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:43:57.886 ; Log ; 37 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:43:57.892 ; Log ; 37 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:43:57.897 ; Trace ; 37 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 00:43:58.007 ; Log ; 37 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:43:58.008 ; Log ; 37 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:43:58.008 ; Log ; 37 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:43:58.008 ; Log ; 37 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:43:58.008 ; Log ; 37 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:43:58.009 ; Log ; 37 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:43:58.010 ; Log ; 37 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:43:58.034 ; Trace ; 33 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:43:58.040 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:43:58.040 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:43:58.074 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:43:58.111 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:43:58.112 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:43:58.116 ; Log ; 33 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:43:58.128 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:43:58.280 ; Trace ; 33 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:43:58.395 ; Trace ; 33 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:43:58.786 ; Trace ; 33 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:43:58.793 ; Info ; 12 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.807 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.808 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.812 ; Trace ; 12 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.824 ; Trace ; 12 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.824 ; Trace ; 12 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.824 ; Trace ; 12 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.826 ; Trace ; 12 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.827 ; Trace ; 12 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.829 ; Log ; 12 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.846 ; Log ; 12 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.846 ; Trace ; 12 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.858 ; Trace ; 12 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.860 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.860 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.865 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:43:58.865 ; Info ; 12 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:44:03.010 ; Log ; 27 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 00:44:03.011 ; Log ; 13 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 00:44:03.014 ; Log ; 13 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 00:44:06.034 ; Trace ; 23 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:44:06.064 ; Log ; 23 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:44:06.064 ; Trace ; 23 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:44:06.077 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:44:06.120 ; Info ; 24 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:44:38.373 ; Trace ; 31 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#8 @Req#7 0s
2025/08/21 00:44:38.378 ; Log ; 31 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#8 @Req#7 0s
2025/08/21 00:44:38.378 ; Trace ; 31 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#8 @Req#7 0s
2025/08/21 00:44:38.388 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:44:38.446 ; Info ; 33 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#9 0s
2025/08/21 00:45:03.031 ; Dev ; 13 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:45:25.960 ; Info ; 18 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#10 0s
2025/08/21 00:45:35.734 ; Info ; 20 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Req#11 0s
2025/08/21 00:45:39.482 ; Info ; 15 ; ::1 ; 9900:admin ; sys-cfg ; hs-fms ; /app/fms/?fm=sys-cfg&id=hs-fms ; ::1 @Req#12 0s
2025/08/21 00:46:03.037 ; Dev ; 13 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:46:06.456 ; Info ; 18 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys ; /app/fms/?fm=sys-cfg&id=hs-sys&sind=y ; ::1 @Req#13 0s
2025/08/21 00:47:03.039 ; Dev ; 13 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:47:04.790 ; Info ; 23 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys ; /app/fms/?fm=sys-cfg&id=hs-sys&sind=y ; ::1 @Req#14 0s
2025/08/21 00:47:04.880 ; Dev ; 23 ; ::1 ; 9900:admin ; GL: OnSystemConfigurationChanged: sys-cfg : hs-sys @Req#14 0s
2025/08/21 00:47:04.888 ; Trace ; 23 ; ::1 ; 9900:admin ; min_amount_fraction=0.01 @Req#14 0s
2025/08/21 00:47:04.888 ; Log ; 23 ; ::1 ; 9900:admin ; Configuration changes applied @Req#14 0s
2025/08/21 00:47:09.401 ; Info ; 18 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys ; /app/fms/?fm=sys-cfg&id=hs-sys&sind=y ; ::1 @Req#15 0s
2025/08/21 00:47:09.446 ; Dev ; 18 ; ::1 ; 9900:admin ; GL: OnSystemConfigurationChanged: sys-cfg : hs-sys @Req#15 0s
2025/08/21 00:47:09.449 ; Trace ; 18 ; ::1 ; 9900:admin ; min_amount_fraction=0.01 @Req#15 0s
2025/08/21 00:47:09.449 ; Log ; 18 ; ::1 ; 9900:admin ; Configuration changes applied @Req#15 0s
2025/08/21 00:47:14.034 ; Info ; 29 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#16 0s
2025/08/21 00:47:16.599 ; Info ; 5 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#17 0s
2025/08/21 00:47:20.337 ; Trace ; 39 ; ::1 ; 9900:admin ; Session End: User logged out @Req#19 0s
2025/08/21 00:47:20.340 ; Trace ; 39 ; ::1 ; 9900:admin ; Removed from cache: h4usxj2jxhxvfmzvvi2jycrw @Req#19 0s
2025/08/21 00:47:20.344 ; Trace ; 38 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:47:26.891 ; Trace ; 26 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#22 0s
2025/08/21 00:47:26.894 ; Log ; 26 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#22 0s
2025/08/21 00:47:26.894 ; Trace ; 26 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#22 0s
2025/08/21 00:47:26.904 ; Info ; 31 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#23 0s
2025/08/21 00:47:26.955 ; Info ; 23 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#24 0s
2025/08/21 00:47:34.120 ; Info ; 32 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Req#25 0s
2025/08/21 00:47:36.879 ; Info ; 21 ; ::1 ; 9900:admin ; sys-cfg ; hs-fms ; /app/fms/?fm=sys-cfg&id=hs-fms ; ::1 @Req#26 0s
2025/08/21 00:47:37.844 ; Info ; 15 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys ; /app/fms/?fm=sys-cfg&id=hs-sys&sind=y ; ::1 @Req#27 0s
2025/08/21 00:47:54.977 ; Info ; 43 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#28 0s
2025/08/21 00:48:01.803 ; Info ; 23 ; ::1 ; 9900:admin ;  ;  ; /app/?restart ; ::1 @Req#29 0s
2025/08/21 00:48:01.805 ; Log ; 23 ; ::1 ; 9900:admin ; System Restart:  @Req#29 0s
2025/08/21 00:48:01.818 ; Trace ; 22 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:48:01.819 ; Trace ; 22 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:48:01.819 ; Log ; 22 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 00:48:01.819 ; Log ; 13 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 00:48:01.819 ; Log ; 27 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 00:48:01.821 ; Log ; 22 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 00:48:01.901 ; Info ; 50 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:48:01.913 ; Dev ; 50 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:48:01.937 ; Trace ; 50 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:48:01.940 ; Log ; 50 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:48:01.940 ; Log ; 50 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:48:01.940 ; Log ; 50 ;  ; 0000: ; 21/8/2025 00:48:01 @Http Req#0
2025/08/21 00:48:01.940 ; Log ; 50 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:48:01.940 ; Log ; 50 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:48:01.940 ; Log ; 50 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:48:01.943 ; Log ; 50 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:48:01.947 ; Log ; 50 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:48:01.996 ; Log ; 50 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:48:01.997 ; Log ; 50 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:48:02.033 ; Info ; 50 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:48:02.053 ; Log ; 50 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:48:02.054 ; Log ; 50 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:48:02.056 ; Trace ; 50 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:48:02.060 ; Trace ; 50 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:48:02.061 ; Trace ; 50 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:48:02.061 ; Trace ; 50 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:48:02.062 ; Trace ; 50 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:48:02.064 ; Info ; 50 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:48:02.108 ; Log ; 50 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:48:02.111 ; Log ; 50 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:48:02.164 ; Log ; 50 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:48:02.164 ; Log ; 50 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:48:02.165 ; Log ; 50 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:48:02.166 ; Log ; 50 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:48:02.225 ; Trace ; 50 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:48:02.226 ; Log ; 50 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:48:02.227 ; Log ; 50 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:48:02.228 ; Log ; 50 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:48:02.229 ; Log ; 50 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:48:02.237 ; Log ; 50 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:48:02.239 ; Trace ; 50 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 00:48:02.322 ; Log ; 50 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:48:02.322 ; Log ; 50 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:48:02.322 ; Log ; 50 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:48:02.323 ; Log ; 50 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:48:02.323 ; Log ; 50 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:48:02.323 ; Log ; 50 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:48:02.323 ; Log ; 50 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:48:02.342 ; Trace ; 29 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:48:02.347 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:48:02.347 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:48:02.370 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:48:02.403 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:48:02.404 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:48:02.408 ; Log ; 29 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:48:02.415 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:48:02.554 ; Trace ; 29 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:48:02.652 ; Trace ; 29 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:48:02.978 ; Trace ; 29 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:48:02.987 ; Info ; 43 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.004 ; Log ; 43 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.004 ; Log ; 43 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.007 ; Trace ; 43 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.022 ; Trace ; 43 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.022 ; Trace ; 43 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.022 ; Trace ; 43 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.024 ; Trace ; 43 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.026 ; Trace ; 43 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.027 ; Log ; 43 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.037 ; Log ; 43 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.037 ; Trace ; 43 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.042 ; Trace ; 43 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.043 ; Log ; 43 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.043 ; Log ; 43 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.044 ; Log ; 43 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:03.044 ; Info ; 43 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:48:07.323 ; Log ; 52 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 00:48:07.324 ; Log ; 53 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 00:48:07.327 ; Log ; 53 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 00:48:09.772 ; Trace ; 42 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:48:09.802 ; Log ; 42 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:48:09.803 ; Trace ; 42 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:48:09.815 ; Info ; 41 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:48:09.870 ; Info ; 24 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:48:19.231 ; Info ; 18 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:48:20.692 ; Info ; 23 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys ; /app/fms/?fm=sys-cfg&id=hs-sys ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:49:07.341 ; Dev ; 53 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#7

2025/08/21 00:50:07.348 ; Dev ; 53 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#7

2025/08/21 00:51:11.186 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:51:11.199 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:51:11.327 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:51:11.335 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:51:11.340 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:51:11.340 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:51:11 @Http Req#0
2025/08/21 00:51:11.341 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:51:11.341 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:51:11.342 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:51:11.346 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 00:51:11.352 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:51:11.355 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:51:11.458 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:51:11.461 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:51:11.525 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:51:11.557 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:51:11.562 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:51:11.573 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:51:11.592 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:51:11.592 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:51:11.593 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:51:11.600 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:51:11.609 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:51:11.696 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:51:11.717 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:51:11.840 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:51:11.854 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:51:11.870 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:51:11.888 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:51:12.028 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:51:12.030 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:51:12.031 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:51:12.033 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:51:12.034 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:51:12.043 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:51:12.049 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 00:51:12.283 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:51:12.283 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:51:12.283 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:51:12.286 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:51:12.286 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:51:12.288 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:51:12.288 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:51:12.368 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:51:12.374 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:51:12.374 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:51:12.412 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:51:12.445 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:51:12.445 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:51:12.449 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:51:12.458 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:51:12.680 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:51:12.990 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:51:13.886 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:51:13.921 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.937 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.938 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.945 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.958 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.959 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.959 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.961 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.963 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.966 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.988 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:51:13.992 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:51:14.010 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:51:14.013 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:14.013 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:14.018 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:14.018 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:51:17.290 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 00:51:17.290 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 00:51:17.296 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 00:51:20.559 ; Trace ; 9 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:51:20.593 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:51:20.593 ; Trace ; 9 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:51:20.626 ; Info ; 11 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:51:20.687 ; Info ; 23 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:51:20.887 ; Log ; 13 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-users ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-users&cmd=view&id=admin @Http Req#6 @Req#5 0s ; #at:UserError
2025/08/21 00:51:47.038 ; Dev ; 22 ; ::1 ; 9900:admin ; >> Form is cached: user @Http Req#7 @Req#6 0s
2025/08/21 00:51:47.038 ; Info ; 22 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:51:49.021 ; Dev ; 11 ; ::1 ; 9900:admin ; Form loaded from cached: user @Http Req#8 @Req#7 0s
2025/08/21 00:51:49.021 ; Info ; 11 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:51:59.520 ; Dev ; 19 ; ::1 ; 9900:admin ; Form loaded from cached: user @Http Req#9 @Req#8 0s
2025/08/21 00:51:59.520 ; Info ; 19 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin&sind=y ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:52:09.474 ; Dev ; 17 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#9 0s
2025/08/21 00:52:09.474 ; Info ; 17 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin ; ::1 @Req#9 0s
2025/08/21 00:52:17.318 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:52:25.409 ; Dev ; 19 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#10 0s
2025/08/21 00:52:25.409 ; Info ; 19 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin ; ::1 @Req#10 0s
2025/08/21 00:52:25.480 ; Dev ; 12 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#11 0s
2025/08/21 00:52:25.480 ; Info ; 12 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin ; ::1 @Req#11 0s
2025/08/21 00:52:29.712 ; Dev ; 13 ; ::1 ; 9900:admin ; >> Form is cached: contacts @Req#12 0s
2025/08/21 00:52:29.712 ; Info ; 13 ; ::1 ; 9900:admin ; contacts ;  ; /app/fms/?fm=contacts&cmd=edit&fk0=user&fk1=admin&pfm=user&pid=admin&sind=y ; ::1 @Req#12 0s
2025/08/21 00:52:29.728 ; Log ; 13 ; ::1 ; 9900:admin ; Unable to load record (hs_contacts.admin) from db, cmd(add) . It can be authorization issue (fixed_cond=sys_client_id = '9900') @Req#12 0s ; #at:ReadFromStore
2025/08/21 00:52:33.101 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.101 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.105 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.105 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.106 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.106 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.106 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.106 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.137 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.137 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.142 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.142 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#13 0s ; #at:ReadStr:parent_fld
2025/08/21 00:52:33.144 ; Dev ; 22 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#13 0s
2025/08/21 00:52:33.145 ; Info ; 22 ; ::1 ; 9900:admin ; attrs ; admin ; /app/fms/?fm=attrs&obj_type=user&id=admin&pfm=user&pid=admin&sind=y ; ::1 @Req#13 0s
2025/08/21 00:53:02.040 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.041 ; Log ; 16 ; ::1 ; 9900:admin ; parent_fld @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.041 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.041 ; Log ; 16 ; ::1 ; 9900:admin ; parent_fld @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.041 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.041 ; Log ; 16 ; ::1 ; 9900:admin ; parent_fld @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.042 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.042 ; Log ; 16 ; ::1 ; 9900:admin ; parent_fld @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.042 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.042 ; Log ; 16 ; ::1 ; 9900:admin ; parent_fld @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.043 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.043 ; Log ; 16 ; ::1 ; 9900:admin ; parent_fld @Req#14 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:02.043 ; Dev ; 16 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#14 0s
2025/08/21 00:53:02.043 ; Info ; 16 ; ::1 ; 9900:admin ; attrs ; admin ; /app/fms/?fm=attrs&obj_type=user&id=admin&pfm=user&pid=admin&sind=y ; ::1 @Req#14 0s
2025/08/21 00:53:15.561 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.561 ; Log ; 18 ; ::1 ; 9900:admin ; parent_fld @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.561 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.561 ; Log ; 18 ; ::1 ; 9900:admin ; parent_fld @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.562 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.562 ; Log ; 18 ; ::1 ; 9900:admin ; parent_fld @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.562 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.562 ; Log ; 18 ; ::1 ; 9900:admin ; parent_fld @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.563 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.563 ; Log ; 18 ; ::1 ; 9900:admin ; parent_fld @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.563 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.563 ; Log ; 18 ; ::1 ; 9900:admin ; parent_fld @Req#15 0s ; #at:ReadStr:parent_fld
2025/08/21 00:53:15.563 ; Dev ; 18 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#15 0s
2025/08/21 00:53:15.563 ; Info ; 18 ; ::1 ; 9900:admin ; attrs ; admin ; /app/fms/?fm=attrs&obj_type=user&id=admin&pfm=user&pid=admin&sind=y ; ::1 @Req#15 0s
2025/08/21 00:53:17.329 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:53:22.489 ; Trace ; 14 ; ::1 ; 9900:admin ; Session End: User logged out @Req#17 0s
2025/08/21 00:53:22.491 ; Trace ; 14 ; ::1 ; 9900:admin ; Removed from cache: h4usxj2jxhxvfmzvvi2jycrw @Req#17 0s
2025/08/21 00:53:22.494 ; Trace ; 23 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:53:29.013 ; Trace ; 17 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#20 0s
2025/08/21 00:53:29.016 ; Log ; 17 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#20 0s
2025/08/21 00:53:29.016 ; Trace ; 17 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#20 0s
2025/08/21 00:53:29.029 ; Info ; 26 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#21 0s
2025/08/21 00:53:29.078 ; Info ; 24 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#22 0s
2025/08/21 00:53:38.012 ; Info ; 13 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#23 0s
2025/08/21 00:53:43.465 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app/?restart ; ::1 @Req#24 0s
2025/08/21 00:53:43.467 ; Log ; 12 ; ::1 ; 9900:admin ; System Restart:  @Req#24 0s
2025/08/21 00:53:43.486 ; Trace ; 26 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:53:43.488 ; Log ; 26 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 00:53:43.488 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 00:53:43.488 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 00:53:43.492 ; Log ; 26 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 00:53:43.572 ; Info ; 32 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:53:43.584 ; Dev ; 32 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:53:43.616 ; Trace ; 32 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:53:43.622 ; Log ; 32 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:53:43.623 ; Log ; 32 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:53:43.623 ; Log ; 32 ;  ; 0000: ; 21/8/2025 00:53:43 @Http Req#0
2025/08/21 00:53:43.624 ; Log ; 32 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:53:43.624 ; Log ; 32 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:53:43.624 ; Log ; 32 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:53:43.631 ; Log ; 32 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:53:43.635 ; Log ; 32 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:53:43.695 ; Log ; 32 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:53:43.695 ; Log ; 32 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:53:43.746 ; Info ; 32 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:53:43.769 ; Log ; 32 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:53:43.771 ; Log ; 32 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:53:43.774 ; Trace ; 32 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:53:43.780 ; Trace ; 32 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:53:43.781 ; Trace ; 32 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:53:43.781 ; Trace ; 32 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:53:43.783 ; Trace ; 32 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:53:43.799 ; Info ; 32 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:53:43.834 ; Log ; 32 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:53:43.835 ; Log ; 32 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:53:43.881 ; Log ; 32 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:53:43.882 ; Log ; 32 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:53:43.882 ; Log ; 32 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:53:43.883 ; Log ; 32 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:53:43.928 ; Trace ; 32 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:53:43.929 ; Log ; 32 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:53:43.929 ; Log ; 32 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:53:43.929 ; Log ; 32 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:53:43.930 ; Log ; 32 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:53:43.930 ; Log ; 32 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:53:43.931 ; Trace ; 32 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 00:53:44.018 ; Log ; 32 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:53:44.018 ; Log ; 32 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:53:44.018 ; Log ; 32 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:53:44.018 ; Log ; 32 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:53:44.018 ; Log ; 32 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:53:44.019 ; Log ; 32 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:53:44.019 ; Log ; 32 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:53:44.040 ; Trace ; 19 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:53:44.045 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:53:44.045 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:53:44.076 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:53:44.107 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:53:44.108 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:53:44.112 ; Log ; 19 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:53:44.122 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:53:44.248 ; Trace ; 19 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:53:44.320 ; Trace ; 19 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:53:44.633 ; Trace ; 19 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:53:44.643 ; Info ; 17 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.657 ; Log ; 17 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.657 ; Log ; 17 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.659 ; Trace ; 17 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.668 ; Trace ; 17 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.668 ; Trace ; 17 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.668 ; Trace ; 17 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.670 ; Trace ; 17 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.673 ; Trace ; 17 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.675 ; Log ; 17 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.688 ; Log ; 17 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.688 ; Trace ; 17 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.695 ; Trace ; 17 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.696 ; Log ; 17 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.696 ; Log ; 17 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.698 ; Log ; 17 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:44.698 ; Info ; 17 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:53:49.020 ; Log ; 10 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 00:53:49.021 ; Log ; 34 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 00:53:49.024 ; Log ; 34 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 00:54:14.590 ; Log ; 22 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( hs-users ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-users&cmd=view&id=admin @Http Req#3 @Req#2 0s ; #at:UserError
2025/08/21 00:54:19.403 ; Trace ; 15 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#4 @Req#3 0s
2025/08/21 00:54:19.421 ; Log ; 15 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#4 @Req#3 0s
2025/08/21 00:54:19.421 ; Trace ; 15 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#4 @Req#3 0s
2025/08/21 00:54:19.432 ; Info ; 23 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:54:19.489 ; Info ; 16 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:54:23.491 ; Info ; 23 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:54:31.997 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update&sind=y ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:54:32.280 ; Trace ; 22 ; ::1 ; 9900:admin ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#8 @Req#7 0s
2025/08/21 00:54:32.829 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Http Req#8 @Req#7 0s ; #at:RunSql_GetReader
2025/08/21 00:54:32.829 ; Log ; 22 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Http Req#8 @Req#7 0s ; #at:RunSql_GetReader
2025/08/21 00:54:32.831 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Http Req#8 @Req#7 0s ; #at:GetDbTableSchema
2025/08/21 00:54:32.831 ; Log ; 22 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Http Req#8 @Req#7 0s ; #at:GetDbTableSchema
2025/08/21 00:54:33.152 ; Log ; 22 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Http Req#8 @Req#7 1s
2025/08/21 00:54:48.028 ; Trace ; 14 ;  ; 9900: ; Session End: User logged out @Http Req#8
2025/08/21 00:54:48.029 ; Log ; 14 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#8
2025/08/21 00:54:48.029 ; Log ; 10 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#8
2025/08/21 00:54:48.029 ; Log ; 34 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#8
2025/08/21 00:54:48.031 ; Log ; 14 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#8

2025/08/21 00:55:03.987 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:55:04.001 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:55:04.142 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:55:04.149 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:55:04.151 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:55:04.151 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:55:04 @Http Req#0
2025/08/21 00:55:04.151 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:55:04.151 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:55:04.152 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:55:04.154 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 00:55:04.158 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:55:04.162 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:55:04.280 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:55:04.282 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:55:04.340 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:55:04.366 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:55:04.371 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:55:04.382 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:55:04.401 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:55:04.401 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:55:04.402 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:55:04.407 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:55:04.414 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:55:04.482 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:55:04.501 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:55:04.602 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:55:04.608 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:55:04.623 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:55:04.639 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:55:04.753 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:55:04.754 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:55:04.754 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:55:04.756 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:55:04.756 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:55:04.762 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:55:04.767 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 00:55:04.994 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:55:04.995 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:55:04.995 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:55:04.995 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:55:04.996 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:55:04.996 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:55:04.997 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:55:05.035 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:55:05.041 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:55:05.041 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:55:05.077 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:55:05.109 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:55:05.109 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:55:05.113 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:55:05.123 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:55:05.363 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:55:05.618 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:55:06.452 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:55:06.515 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#1
2025/08/21 00:55:06.531 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#1
2025/08/21 00:55:06.532 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#1
2025/08/21 00:55:06.536 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/08/21 00:55:06.549 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#1
2025/08/21 00:55:06.550 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#1
2025/08/21 00:55:06.550 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#1
2025/08/21 00:55:06.552 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#1
2025/08/21 00:55:06.556 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#1
2025/08/21 00:55:06.561 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#1
2025/08/21 00:55:06.582 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#1
2025/08/21 00:55:06.584 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#1
2025/08/21 00:55:06.604 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#1
2025/08/21 00:55:06.606 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#1
2025/08/21 00:55:06.606 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#1
2025/08/21 00:55:06.611 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#1
2025/08/21 00:55:06.612 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#1
2025/08/21 00:55:06.659 ; Trace ; 9 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#1
2025/08/21 00:55:06.712 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#1
2025/08/21 00:55:06.713 ; Trace ; 9 ; ::1 ; 9900:admin ; Redirecting user to: /app/?hs-sys-db&cmd=sql @Http Req#1
2025/08/21 00:55:06.756 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Http Req#2 @Req#1 0s
2025/08/21 00:55:06.808 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#3 @Req#2 0s
2025/08/21 00:55:09.999 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#3
2025/08/21 00:55:09.999 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#3
2025/08/21 00:55:10.005 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#3
2025/08/21 00:55:11.386 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:55:17.623 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-cp ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:55:19.895 ; Info ; 10 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:55:22.241 ; Info ; 14 ; ::1 ; 9900:admin ; sys-cfg ; hs-fms ; /app/fms/?fm=sys-cfg&id=hs-fms ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:55:23.270 ; Info ; 11 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys ; /app/fms/?fm=sys-cfg&id=hs-sys&sind=y ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:55:35.352 ; Dev ; 9 ; ::1 ; 9900:admin ; >> Form is cached: user @Http Req#9 @Req#8 0s
2025/08/21 00:55:35.352 ; Info ; 9 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:55:54.677 ; Trace ; 16 ; ::1 ; 9900:admin ; Session End: User logged out @Req#10 0s
2025/08/21 00:55:54.680 ; Trace ; 16 ; ::1 ; 9900:admin ; Removed from cache: h4usxj2jxhxvfmzvvi2jycrw @Req#10 0s
2025/08/21 00:55:54.685 ; Trace ; 13 ;  ; 9900: ; Session End: User logged out
2025/08/21 00:56:10.029 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:56:14.602 ; Trace ; 22 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#15 0s
2025/08/21 00:56:14.604 ; Log ; 22 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#15 0s
2025/08/21 00:56:14.604 ; Trace ; 22 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#15 0s
2025/08/21 00:56:14.614 ; Info ; 21 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#16 0s
2025/08/21 00:56:14.668 ; Info ; 25 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#17 0s
2025/08/21 00:56:23.379 ; Dev ; 21 ; ::1 ; 9900:admin ; >> Form is cached: user @Req#18 0s
2025/08/21 00:56:23.380 ; Info ; 21 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Req#18 0s
2025/08/21 00:56:25.242 ; Dev ; 10 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#19 0s
2025/08/21 00:56:25.242 ; Info ; 10 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Req#19 0s
2025/08/21 00:56:34.404 ; Dev ; 26 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#20 0s
2025/08/21 00:56:34.404 ; Info ; 26 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin&sind=y ; ::1 @Req#20 0s
2025/08/21 00:56:36.232 ; Dev ; 25 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#21 0s
2025/08/21 00:56:36.233 ; Info ; 25 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin ; ::1 @Req#21 0s
2025/08/21 00:56:55.012 ; Dev ; 14 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#22 0s
2025/08/21 00:56:55.012 ; Info ; 14 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin ; ::1 @Req#22 0s
2025/08/21 00:56:55.071 ; Dev ; 13 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#23 0s
2025/08/21 00:56:55.071 ; Info ; 13 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin ; ::1 @Req#23 0s
2025/08/21 00:56:57.695 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.695 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.696 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.696 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.697 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.697 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.697 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.697 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.719 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.719 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.729 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.729 ; Log ; 22 ; ::1 ; 9900:admin ; parent_fld @Req#24 0s ; #at:ReadStr:parent_fld
2025/08/21 00:56:57.731 ; Dev ; 22 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#24 0s
2025/08/21 00:56:57.732 ; Info ; 22 ; ::1 ; 9900:admin ; attrs ; admin ; /app/fms/?fm=attrs&obj_type=user&id=admin&pfm=user&pid=admin&sind=y ; ::1 @Req#24 0s
2025/08/21 00:57:10.038 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:57:52.241 ; Dev ; 15 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#25 0s
2025/08/21 00:57:52.241 ; Info ; 15 ; ::1 ; 9900:admin ; user ; guest ; /app/fms/?fm=user&cmd=view&id=guest&sind=y ; ::1 @Req#25 0s
2025/08/21 00:57:53.752 ; Fatal ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.752 ; Log ; 21 ; ::1 ; 9900:admin ; parent_fld @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.752 ; Fatal ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.752 ; Log ; 21 ; ::1 ; 9900:admin ; parent_fld @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Fatal ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Log ; 21 ; ::1 ; 9900:admin ; parent_fld @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Fatal ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Log ; 21 ; ::1 ; 9900:admin ; parent_fld @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Fatal ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Log ; 21 ; ::1 ; 9900:admin ; parent_fld @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Fatal ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.753 ; Log ; 21 ; ::1 ; 9900:admin ; parent_fld @Req#26 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:53.755 ; Dev ; 21 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#26 0s
2025/08/21 00:57:53.755 ; Info ; 21 ; ::1 ; 9900:admin ; attrs ; guest ; /app/fms/?fm=attrs&obj_type=user&id=guest&pfm=user&pid=guest ; ::1 @Req#26 0s
2025/08/21 00:57:58.126 ; Fatal ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Log ; 10 ; ::1 ; 9900:admin ; parent_fld @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Fatal ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Log ; 10 ; ::1 ; 9900:admin ; parent_fld @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Fatal ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Log ; 10 ; ::1 ; 9900:admin ; parent_fld @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Fatal ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.126 ; Log ; 10 ; ::1 ; 9900:admin ; parent_fld @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.127 ; Fatal ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.127 ; Log ; 10 ; ::1 ; 9900:admin ; parent_fld @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.127 ; Fatal ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.127 ; Log ; 10 ; ::1 ; 9900:admin ; parent_fld @Req#27 0s ; #at:ReadStr:parent_fld
2025/08/21 00:57:58.128 ; Dev ; 10 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#27 0s
2025/08/21 00:57:58.128 ; Info ; 10 ; ::1 ; 9900:admin ; attrs ; guest ; /app/fms/?fm=attrs&obj_type=user&id=guest&pfm=user&pid=guest ; ::1 @Req#27 0s
2025/08/21 00:58:10.043 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:58:44.188 ; Info ; 15 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-cp ; ::1 @Req#28 0s
2025/08/21 00:58:46.276 ; Info ; 27 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&sind=y ; ::1 @Req#29 0s
2025/08/21 00:58:52.019 ; Info ; 18 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check ; ::1 @Req#30 0s
2025/08/21 00:58:52.281 ; Trace ; 18 ; ::1 ; 9900:admin ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Req#30 0s
2025/08/21 00:58:52.780 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#30 0s ; #at:RunSql_GetReader
2025/08/21 00:58:52.780 ; Log ; 18 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#30 0s ; #at:RunSql_GetReader
2025/08/21 00:58:52.781 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#30 0s ; #at:GetDbTableSchema
2025/08/21 00:58:52.781 ; Log ; 18 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#30 0s ; #at:GetDbTableSchema
2025/08/21 00:58:52.945 ; Fatal ; 18 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#30 0s ; #at:GetRecordsCount
2025/08/21 00:58:52.945 ; Log ; 18 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#30 0s ; #at:GetRecordsCount
2025/08/21 00:59:08.553 ; Info ; 14 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#31 0s
2025/08/21 00:59:09.041 ; Fatal ; 14 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#31 0s ; #at:RunSql_GetReader
2025/08/21 00:59:09.041 ; Log ; 14 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#31 0s ; #at:RunSql_GetReader
2025/08/21 00:59:09.041 ; Fatal ; 14 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#31 0s ; #at:GetDbTableSchema
2025/08/21 00:59:09.041 ; Log ; 14 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#31 0s ; #at:GetDbTableSchema
2025/08/21 00:59:09.311 ; Log ; 14 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#31 0s
2025/08/21 00:59:09.404 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#32 0s
2025/08/21 00:59:10.046 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:59:12.663 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#33 0s
2025/08/21 00:59:13.123 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#33 0s ; #at:RunSql_GetReader
2025/08/21 00:59:13.123 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#33 0s ; #at:RunSql_GetReader
2025/08/21 00:59:13.123 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#33 0s ; #at:GetDbTableSchema
2025/08/21 00:59:13.123 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#33 0s ; #at:GetDbTableSchema
2025/08/21 00:59:13.213 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#33 0s ; #at:GetRecordsCount
2025/08/21 00:59:13.214 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#33 0s ; #at:GetRecordsCount
2025/08/21 00:59:42.416 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#34 0s
2025/08/21 00:59:42.864 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#34 0s ; #at:RunSql_GetReader
2025/08/21 00:59:42.864 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#34 0s ; #at:RunSql_GetReader
2025/08/21 00:59:42.864 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#34 0s ; #at:GetDbTableSchema
2025/08/21 00:59:42.864 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#34 0s ; #at:GetDbTableSchema
2025/08/21 00:59:43.092 ; Log ; 20 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#34 0s
2025/08/21 01:00:10.049 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:01:10.051 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:01:29.312 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#35 0s
2025/08/21 01:01:29.756 ; Fatal ; 9 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#35 0s ; #at:RunSql_GetReader
2025/08/21 01:01:29.756 ; Log ; 9 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#35 0s ; #at:RunSql_GetReader
2025/08/21 01:01:29.757 ; Fatal ; 9 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#35 0s ; #at:GetDbTableSchema
2025/08/21 01:01:29.757 ; Log ; 9 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#35 0s ; #at:GetDbTableSchema
2025/08/21 01:01:29.836 ; Fatal ; 9 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#35 0s ; #at:GetRecordsCount
2025/08/21 01:01:29.836 ; Log ; 9 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#35 0s ; #at:GetRecordsCount
2025/08/21 01:01:58.108 ; Info ; 11 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#36 0s
2025/08/21 01:01:58.569 ; Fatal ; 11 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#36 0s ; #at:RunSql_GetReader
2025/08/21 01:01:58.569 ; Log ; 11 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#36 0s ; #at:RunSql_GetReader
2025/08/21 01:01:58.569 ; Fatal ; 11 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#36 0s ; #at:GetDbTableSchema
2025/08/21 01:01:58.569 ; Log ; 11 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#36 0s ; #at:GetDbTableSchema
2025/08/21 01:01:58.658 ; Fatal ; 11 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#36 0s ; #at:GetRecordsCount
2025/08/21 01:01:58.658 ; Log ; 11 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#36 0s ; #at:GetRecordsCount
2025/08/21 01:02:00.308 ; Info ; 29 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update&sind=y ; ::1 @Req#37 0s
2025/08/21 01:02:00.814 ; Fatal ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#37 0s ; #at:RunSql_GetReader
2025/08/21 01:02:00.814 ; Log ; 29 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#37 0s ; #at:RunSql_GetReader
2025/08/21 01:02:00.814 ; Fatal ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#37 0s ; #at:GetDbTableSchema
2025/08/21 01:02:00.814 ; Log ; 29 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#37 0s ; #at:GetDbTableSchema
2025/08/21 01:02:01.065 ; Log ; 29 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#37 0s
2025/08/21 01:02:05.292 ; Info ; 16 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=list-tables&sind=y ; ::1 @Req#38 0s
2025/08/21 01:02:10.052 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:02:30.802 ; Info ; 26 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#39 0s
2025/08/21 01:02:31.271 ; Fatal ; 26 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#39 0s ; #at:RunSql_GetReader
2025/08/21 01:02:31.271 ; Log ; 26 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#39 0s ; #at:RunSql_GetReader
2025/08/21 01:02:31.271 ; Fatal ; 26 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#39 0s ; #at:GetDbTableSchema
2025/08/21 01:02:31.271 ; Log ; 26 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#39 0s ; #at:GetDbTableSchema
2025/08/21 01:02:31.351 ; Fatal ; 26 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#39 0s ; #at:GetRecordsCount
2025/08/21 01:02:31.351 ; Log ; 26 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#39 0s ; #at:GetRecordsCount
2025/08/21 01:02:56.962 ; Info ; 16 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#40 0s
2025/08/21 01:02:57.398 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#40 0s ; #at:RunSql_GetReader
2025/08/21 01:02:57.398 ; Log ; 16 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#40 0s ; #at:RunSql_GetReader
2025/08/21 01:02:57.398 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#40 0s ; #at:GetDbTableSchema
2025/08/21 01:02:57.398 ; Log ; 16 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#40 0s ; #at:GetDbTableSchema
2025/08/21 01:02:57.473 ; Fatal ; 16 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#40 0s ; #at:GetRecordsCount
2025/08/21 01:02:57.473 ; Log ; 16 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#40 0s ; #at:GetRecordsCount
2025/08/21 01:03:10.054 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:03:13.093 ; Info ; 32 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#41 0s
2025/08/21 01:03:32.298 ; Info ; 29 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check ; ::1 @Req#42 0s
2025/08/21 01:03:32.716 ; Fatal ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#42 0s ; #at:RunSql_GetReader
2025/08/21 01:03:32.716 ; Log ; 29 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#42 0s ; #at:RunSql_GetReader
2025/08/21 01:03:32.717 ; Fatal ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#42 0s ; #at:GetDbTableSchema
2025/08/21 01:03:32.717 ; Log ; 29 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#42 0s ; #at:GetDbTableSchema
2025/08/21 01:03:32.804 ; Fatal ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#42 0s ; #at:GetRecordsCount
2025/08/21 01:03:32.804 ; Log ; 29 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#42 0s ; #at:GetRecordsCount
2025/08/21 01:04:10.056 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:04:12.967 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#43 0s
2025/08/21 01:04:13.474 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#43 0s ; #at:RunSql_GetReader
2025/08/21 01:04:13.474 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#43 0s ; #at:RunSql_GetReader
2025/08/21 01:04:13.474 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#43 0s ; #at:GetDbTableSchema
2025/08/21 01:04:13.474 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#43 0s ; #at:GetDbTableSchema
2025/08/21 01:04:13.545 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#43 0s ; #at:GetRecordsCount
2025/08/21 01:04:13.545 ; Log ; 20 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#43 0s ; #at:GetRecordsCount
2025/08/21 01:04:45.775 ; Info ; 41 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#44 0s
2025/08/21 01:04:58.024 ; Dev ; 37 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#45 0s
2025/08/21 01:04:58.024 ; Info ; 37 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user&sind=y ; ::1 @Req#45 0s
2025/08/21 01:05:06.265 ; Info ; 30 ; ::1 ; 9900:admin ;  ; server ; /app/?hs-user&cmd=clear-cache&id=server&sind=y ; ::1 @Req#46 0s
2025/08/21 01:05:07.463 ; Info ; 36 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#47 0s
2025/08/21 01:05:10.057 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:05:22.325 ; Info ; 36 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#48 0s
2025/08/21 01:05:26.547 ; Info ; 35 ; ::1 ; 9900:admin ;  ;  ; /app/?restart ; ::1 @Req#49 0s
2025/08/21 01:05:26.548 ; Log ; 35 ; ::1 ; 9900:admin ; System Restart:  @Req#49 0s
2025/08/21 01:05:26.567 ; Trace ; 40 ;  ; 9900: ; Session End: User logged out
2025/08/21 01:05:26.567 ; Trace ; 40 ;  ; 9900: ; Session End: User logged out
2025/08/21 01:05:26.568 ; Log ; 40 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 01:05:26.568 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 01:05:26.568 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 01:05:26.573 ; Log ; 40 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 01:05:26.648 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 01:05:26.660 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 01:05:26.744 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 01:05:26.744 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 01:05:26.745 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 01:05:26.745 ; Log ; 1 ;  ; 0000: ; 21/8/2025 01:05:26 @Http Req#0
2025/08/21 01:05:26.745 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 01:05:26.745 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 01:05:26.745 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 01:05:26.752 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 01:05:26.757 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 01:05:26.819 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 01:05:26.819 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 01:05:26.889 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 01:05:26.918 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 01:05:26.924 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 01:05:26.933 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 01:05:26.950 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 01:05:26.951 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 01:05:26.951 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 01:05:26.954 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 01:05:26.960 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 01:05:27.006 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 01:05:27.010 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 01:05:27.043 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 01:05:27.043 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 01:05:27.044 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 01:05:27.045 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 01:05:27.094 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 01:05:27.096 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 01:05:27.096 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 01:05:27.096 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 01:05:27.096 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 01:05:27.096 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 01:05:27.099 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 01:05:27.202 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 01:05:27.202 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 01:05:27.202 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 01:05:27.202 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 01:05:27.202 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 01:05:27.203 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 01:05:27.203 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 01:05:27.225 ; Trace ; 31 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 01:05:27.231 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 01:05:27.231 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 01:05:27.258 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 01:05:27.293 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 01:05:27.294 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 01:05:27.297 ; Log ; 31 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 01:05:27.308 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 01:05:27.480 ; Trace ; 31 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 01:05:27.614 ; Trace ; 31 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 01:05:28.006 ; Trace ; 31 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 01:05:28.015 ; Info ; 12 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.036 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.037 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.041 ; Trace ; 12 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.056 ; Trace ; 12 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.057 ; Trace ; 12 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.057 ; Trace ; 12 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.059 ; Trace ; 12 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.060 ; Trace ; 12 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.062 ; Log ; 12 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.084 ; Log ; 12 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.084 ; Trace ; 12 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.094 ; Trace ; 12 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.096 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.096 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.098 ; Log ; 12 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:28.098 ; Info ; 12 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:05:32.203 ; Log ; 50 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 01:05:32.204 ; Log ; 51 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 01:05:32.207 ; Log ; 51 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 01:05:42.299 ; Trace ; 40 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#5 @Req#4 0s
2025/08/21 01:05:42.329 ; Log ; 40 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#5 @Req#4 0s
2025/08/21 01:05:42.329 ; Trace ; 40 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#5 @Req#4 0s
2025/08/21 01:05:42.340 ; Info ; 35 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 01:05:42.397 ; Info ; 24 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 01:06:32.224 ; Dev ; 51 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#7

2025/08/21 01:06:41.480 ; Info ; 42 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 01:06:53.427 ; Info ; 26 ; ::1 ; 9900:admin ;  ; app-usr-fav ; /app/?menu&id=app-usr-fav ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 01:07:32.232 ; Dev ; 51 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#9

2025/08/21 01:07:40.501 ; Trace ; 11 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =user-menu @Http Req#9 @Req#9 0s
2025/08/21 01:07:42.857 ; Info ; 18 ; ::1 ; 9900:admin ;  ; app-usr-fav ; /app/?menu&id=app-usr-fav ; ::1 @Req#9 0s
2025/08/21 01:08:13.320 ; Dev ; 21 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Req#10 0s
2025/08/21 01:08:13.320 ; Info ; 21 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#10 0s
2025/08/21 01:08:16.892 ; Dev ; 22 ; ::1 ; 9900:admin ; >> Form is cached: hr-wd-def @Req#11 0s
2025/08/21 01:08:16.892 ; Info ; 22 ; ::1 ; 9900:admin ; hr-wd-def ;  ; /app/fms/?fm=hr-wd-def&cmd=list ; ::1 @Req#11 0s
2025/08/21 01:08:17.198 ; Trace ; 22 ; ::1 ; 9900:admin ; UserClientSideCache: Add key=acc_data_list @Req#11 0s
2025/08/21 01:08:32.233 ; Dev ; 51 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:09:32.236 ; Dev ; 51 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:09:49.862 ; Trace ; 39 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#14 0s
2025/08/21 01:09:49.865 ; Log ; 39 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#14 0s
2025/08/21 01:09:49.865 ; Trace ; 39 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#14 0s
2025/08/21 01:09:49.874 ; Info ; 42 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#15 0s
2025/08/21 01:09:49.932 ; Info ; 34 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#16 0s
2025/08/21 01:09:54.154 ; Dev ; 22 ; ::1 ; 9900:admin ; >> Form is cached: hs-cms @Req#17 0s
2025/08/21 01:09:54.154 ; Info ; 22 ; ::1 ; 9900:admin ; hs-cms ;  ; /app/fms/?fm=hs-cms ; ::1 @Req#17 0s
2025/08/21 01:09:59.032 ; Dev ; 19 ; ::1 ; 9900:admin ; >> Form is cached: hs-cms @Req#18 0s
2025/08/21 01:09:59.033 ; Info ; 19 ; ::1 ; 9900:admin ; hs-cms ;  ; /app/fms/?fm=hs-cms&fm-mode=cont-te&cmd=list ; ::1 @Req#18 0s
2025/08/21 01:10:32.237 ; Dev ; 51 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:10:44.724 ; Info ; 21 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys ; ::1 @Req#19 0s
2025/08/21 01:11:21.022 ; Info ; 24 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys ; ::1 @Req#20 0s
2025/08/21 01:11:27.541 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-cfg ; ::1 @Req#21 0s
2025/08/21 01:11:32.240 ; Dev ; 51 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:11:33.688 ; Info ; 29 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#22 0s
2025/08/21 01:11:37.896 ; Info ; 40 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check&sind=y ; ::1 @Req#23 0s
2025/08/21 01:11:38.160 ; Trace ; 40 ; ::1 ; 9900:admin ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Req#23 0s
2025/08/21 01:11:38.637 ; Fatal ; 40 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#23 0s ; #at:RunSql_GetReader
2025/08/21 01:11:38.637 ; Log ; 40 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:RunSql_GetReader
2025/08/21 01:11:38.637 ; Fatal ; 40 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#23 0s ; #at:GetDbTableSchema
2025/08/21 01:11:38.637 ; Log ; 40 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:GetDbTableSchema
2025/08/21 01:11:38.772 ; Fatal ; 40 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#23 0s ; #at:GetRecordsCount
2025/08/21 01:11:38.772 ; Log ; 40 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:GetRecordsCount
2025/08/21 01:11:49.249 ; Trace ; 29 ;  ; 9900: ; Session End: User logged out
2025/08/21 01:11:49.250 ; Trace ; 29 ;  ; 9900: ; Session End: User logged out
2025/08/21 01:11:49.250 ; Trace ; 29 ;  ; 9900: ; Session End: User logged out
2025/08/21 01:11:49.250 ; Log ; 29 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 01:11:49.250 ; Log ; 51 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 01:11:49.250 ; Log ; 50 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 01:11:49.252 ; Log ; 29 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 01:12:12.074 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 01:12:12.089 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 01:12:12.215 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 01:12:12.222 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 01:12:12.223 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 01:12:12.223 ; Log ; 1 ;  ; 0000: ; 21/8/2025 01:12:12 @Http Req#0
2025/08/21 01:12:12.224 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 01:12:12.224 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 01:12:12.224 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 01:12:12.238 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 01:12:12.243 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 01:12:12.349 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 01:12:12.351 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 01:12:12.418 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 01:12:12.452 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 01:12:12.457 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 01:12:12.469 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 01:12:12.487 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 01:12:12.487 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 01:12:12.487 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 01:12:12.493 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 01:12:12.501 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 01:12:12.585 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 01:12:12.606 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 01:12:12.726 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 01:12:12.733 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 01:12:12.747 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 01:12:12.764 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 01:12:12.908 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 01:12:12.909 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 01:12:12.909 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 01:12:12.911 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 01:12:12.911 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 01:12:12.919 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 01:12:12.924 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/21 01:12:13.169 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 01:12:13.169 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 01:12:13.170 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 01:12:13.170 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 01:12:13.170 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 01:12:13.171 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 01:12:13.172 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 01:12:13.251 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 01:12:13.258 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 01:12:13.258 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 01:12:13.289 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 01:12:13.343 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 01:12:13.344 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 01:12:13.347 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 01:12:13.357 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 01:12:13.595 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 01:12:13.907 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 01:12:14.800 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 01:12:14.841 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.861 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.862 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.869 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.883 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.885 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.885 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.887 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.889 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.895 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.919 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.921 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.939 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.941 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.941 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.945 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:14.945 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:12:18.173 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 01:12:18.173 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 01:12:18.179 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 01:12:20.897 ; Trace ; 13 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#5 @Req#4 0s
2025/08/21 01:12:20.942 ; Log ; 13 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#5 @Req#4 0s
2025/08/21 01:12:20.943 ; Trace ; 13 ; ::1 ; 9900:admin ; Redirecting user to: /app/?menu&id=hcm-sys @Http Req#5 @Req#4 0s
2025/08/21 01:12:20.984 ; Info ; 15 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 01:12:21.051 ; Info ; 11 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 01:12:32.895 ; Info ; 20 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 01:13:13.968 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#9 0s
2025/08/21 01:13:18.202 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:13:57.307 ; Trace ; 9 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =user-menu @Req#10 0s
2025/08/21 01:13:59.479 ; Info ; 13 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#10 0s
2025/08/21 01:14:18.211 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:14:34.572 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#11 0s
2025/08/21 01:14:45.640 ; Info ; 10 ; ::1 ; 9900:admin ;  ; app-usr-fav ; /app/?menu&id=app-usr-fav ; ::1 @Req#12 0s
2025/08/21 01:14:54.252 ; Trace ; 25 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =user-menu @Req#13 0s
2025/08/21 01:14:59.293 ; Info ; 15 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#13 0s
2025/08/21 01:15:08.222 ; Info ; 12 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#14 0s
2025/08/21 01:15:12.816 ; Dev ; 18 ; ::1 ; 9900:admin ; >> Form is cached: user @Req#15 0s
2025/08/21 01:15:12.816 ; Info ; 18 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user&sind=y ; ::1 @Req#15 0s
2025/08/21 01:15:15.452 ; Dev ; 10 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#16 0s
2025/08/21 01:15:15.453 ; Info ; 10 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user&sind=y ; ::1 @Req#16 0s
2025/08/21 01:15:17.881 ; Dev ; 13 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#17 0s
2025/08/21 01:15:17.881 ; Info ; 13 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin ; ::1 @Req#17 0s
2025/08/21 01:15:18.213 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:15:22.457 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.458 ; Log ; 20 ; ::1 ; 9900:admin ; parent_fld @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.462 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.462 ; Log ; 20 ; ::1 ; 9900:admin ; parent_fld @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.462 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.462 ; Log ; 20 ; ::1 ; 9900:admin ; parent_fld @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.462 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.462 ; Log ; 20 ; ::1 ; 9900:admin ; parent_fld @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.488 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.488 ; Log ; 20 ; ::1 ; 9900:admin ; parent_fld @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.503 ; Fatal ; 20 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.503 ; Log ; 20 ; ::1 ; 9900:admin ; parent_fld @Req#18 0s ; #at:ReadStr:parent_fld
2025/08/21 01:15:22.507 ; Dev ; 20 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#18 0s
2025/08/21 01:15:22.509 ; Info ; 20 ; ::1 ; 9900:admin ; attrs ; admin ; /app/fms/?fm=attrs&obj_type=user&id=admin&pfm=user&pid=admin&sind=y ; ::1 @Req#18 0s
2025/08/21 01:15:46.784 ; Trace ; 13 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =user-menu @Req#19 0s
2025/08/21 01:15:48.812 ; Info ; 21 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#19 0s
2025/08/21 01:15:51.045 ; Info ; 9 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys ; ::1 @Req#20 0s
2025/08/21 01:16:04.598 ; Info ; 24 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#21 0s
2025/08/21 01:16:09.885 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_sp_accs.xml @Req#22 0s
2025/08/21 01:16:09.902 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_im_config.xml @Req#22 0s
2025/08/21 01:16:09.903 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_hcm.xml @Req#22 0s
2025/08/21 01:16:09.903 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_gl.xml @Req#22 0s
2025/08/21 01:16:09.904 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_es_items.xml @Req#22 0s
2025/08/21 01:16:09.904 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_es_docs.xml @Req#22 0s
2025/08/21 01:16:09.905 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_doc_common.xml @Req#22 0s
2025/08/21 01:16:09.905 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\remox_config.xml @Req#22 0s
2025/08/21 01:16:09.907 ; Log ; 22 ; ::1 ; 9900:admin ; Loading help file: D:\mohhash\App\bin\webapp\help\files\hs_core.xml @Req#22 0s
2025/08/21 01:16:09.907 ; Trace ; 22 ; ::1 ; 9900:admin ; System help loaded.. @Req#22 0s
2025/08/21 01:16:13.878 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#23 0s
2025/08/21 01:16:14.152 ; Trace ; 22 ; ::1 ; 9900:admin ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Req#23 0s
2025/08/21 01:16:14.656 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#23 0s ; #at:RunSql_GetReader
2025/08/21 01:16:14.656 ; Log ; 22 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:RunSql_GetReader
2025/08/21 01:16:14.656 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#23 0s ; #at:GetDbTableSchema
2025/08/21 01:16:14.656 ; Log ; 22 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:GetDbTableSchema
2025/08/21 01:16:14.943 ; Log ; 22 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#23 1s
2025/08/21 01:16:18.213 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:16:23.506 ; Info ; 12 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys&sind=y ; ::1 @Req#24 0s
2025/08/21 01:16:23.786 ; Trace ; 12 ; ::1 ; 9900:admin ; UserClientSideCache: Add key=acc_data_list @Req#24 0s
2025/08/21 01:16:29.496 ; Info ; 15 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys&sind=y ; ::1 @Req#25 0s
2025/08/21 01:16:29.552 ; Dev ; 15 ; ::1 ; 9900:admin ; GL: OnSystemConfigurationChanged: sys-cfg : hr-sys @Req#25 0s
2025/08/21 01:16:29.557 ; Trace ; 15 ; ::1 ; 9900:admin ; Config cache refreshed: hcm-cfg @Req#25 0s
2025/08/21 01:16:37.145 ; Info ; 22 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#27 0s
2025/08/21 01:16:37.820 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#27 0s ; #at:RunSql_GetReader
2025/08/21 01:16:37.820 ; Log ; 22 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#27 0s ; #at:RunSql_GetReader
2025/08/21 01:16:37.821 ; Fatal ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#27 0s ; #at:GetDbTableSchema
2025/08/21 01:16:37.821 ; Log ; 22 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#27 0s ; #at:GetDbTableSchema
2025/08/21 01:16:38.108 ; Log ; 22 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#27 0s
2025/08/21 01:16:48.036 ; Info ; 25 ; ::1 ; 9900:admin ;  ;  ; /app/?fi-act&cmd=init ; ::1 @Req#29 0s
2025/08/21 01:17:08.942 ; Info ; 18 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys&sind=y ; ::1 @Req#30 0s
2025/08/21 01:17:15.284 ; Info ; 24 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#32 0s
2025/08/21 01:17:15.897 ; Fatal ; 24 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#32 0s ; #at:RunSql_GetReader
2025/08/21 01:17:15.897 ; Log ; 24 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#32 0s ; #at:RunSql_GetReader
2025/08/21 01:17:15.897 ; Fatal ; 24 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#32 0s ; #at:GetDbTableSchema
2025/08/21 01:17:15.897 ; Log ; 24 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#32 0s ; #at:GetDbTableSchema
2025/08/21 01:17:16.202 ; Log ; 24 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#32 0s
2025/08/21 01:17:18.218 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:18:18.219 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:19:18.221 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:20:18.223 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:21:14.098 ; Info ; 29 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#33 0s
2025/08/21 01:21:14.267 ; Info ; 13 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#34 0s
2025/08/21 01:21:14.414 ; Info ; 26 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#35 0s
2025/08/21 01:21:14.628 ; Info ; 20 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#36 0s
2025/08/21 01:21:14.781 ; Info ; 18 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#37 0s
2025/08/21 01:21:14.975 ; Info ; 22 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#38 0s
2025/08/21 01:21:15.147 ; Info ; 21 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#39 0s
2025/08/21 01:21:15.454 ; Info ; 33 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#40 0s
2025/08/21 01:21:15.774 ; Info ; 16 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#41 0s
2025/08/21 01:21:18.224 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:21:27.269 ; Info ; 14 ; ::1 ; 9900:admin ;  ; hcm-sys ; /app/?menu&id=hcm-sys&ui=rep ; ::1 @Req#42 0s
2025/08/21 01:21:30.569 ; Info ; 11 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#43 0s
2025/08/21 01:21:49.182 ; Info ; 37 ; ::1 ; 9900:admin ; hs-dev-tools ;  ; /app/fms/?fm=hs-dev-tools&cmd=home&sind=y ; ::1 @Req#44 0s
2025/08/21 01:21:57.822 ; Info ; 35 ; ::1 ; 9900:admin ; sys-cfg ;  ; /app/fms/?fm=sys-cfg&sind=y ; ::1 @Req#45 0s
2025/08/21 01:22:09.243 ; Info ; 11 ; ::1 ; 9900:admin ; sys-cfg ; hs-fms ; /app/fms/?fm=sys-cfg&id=hs-fms ; ::1 @Req#46 0s
2025/08/21 01:22:18.225 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:23:18.227 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:24:18.228 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:25:18.230 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:26:18.231 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:27:18.232 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:28:18.232 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:29:18.233 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:30:18.234 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:31:18.236 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:32:18.238 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:33:18.239 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:34:18.241 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:35:18.242 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 01:35:18.385 ; Info ; 31 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#47 0s
2025/08/21 01:36:12.232 ; Trace ; 20 ;  ; 9900: ; Session End: User logged out
2025/08/21 01:36:12.233 ; Log ; 20 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 01:36:12.233 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 01:36:12.233 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 01:36:12.239 ; Log ; 20 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 01:37:55.521 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 01:37:55.522 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 01:37:55.522 ; Log ; 1 ;  ; 0000: ; 21/8/2025 01:37:55 @Http Req#0
2025/08/21 01:37:55.523 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 01:37:55.523 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 01:37:55.523 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 01:37:55.529 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 01:37:55.533 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 01:37:55.613 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 01:37:55.614 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 01:37:55.693 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 01:37:55.699 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 01:37:55.780 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 01:37:55.784 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 01:37:55.822 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 01:37:55.823 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 01:37:55.823 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 01:37:55.826 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 01:37:55.890 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 01:37:55.890 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 01:37:55.893 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 01:37:55.894 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 01:37:55.902 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 01:37:56.029 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 01:37:56.030 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 01:37:56.030 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 01:37:56.030 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 01:37:56.030 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 01:37:56.031 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 01:37:56.032 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 01:37:56.196 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 01:37:57.028 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:37:57.029 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:37:57.053 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 01:37:57.072 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 01:37:57.083 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:37:57.083 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:37:57.087 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 01:38:01.032 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 01:38:01.033 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 01:38:01.036 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 01:38:01.683 ; Log ; 23 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; 21/8/2025 18:04:45 @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 18:04:45.448 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 18:04:45.511 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 18:04:45.511 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 18:04:45.589 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 18:04:45.589 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 18:04:45.683 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 18:04:45.683 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 18:04:45.714 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 18:04:45.714 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 18:04:45.714 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 18:04:45.714 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 18:04:45.730 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 18:04:45.730 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 18:04:45.745 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 18:04:45.745 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 18:04:45.745 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 18:04:45.855 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 18:04:45.980 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 18:04:46.740 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:04:46.742 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:04:46.769 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 18:04:46.790 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 18:04:46.812 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:04:46.812 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:04:46.817 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:04:50.866 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 18:04:50.866 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 18:04:50.866 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2

2025/08/21 18:13:06.397 ; Log ; 31 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/21 18:34:46.201 ; Log ; 65 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#6
2025/08/21 18:34:46.201 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#6
2025/08/21 18:34:46.201 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#6
2025/08/21 18:34:46.213 ; Log ; 65 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#6

2025/08/21 18:39:04.849 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 18:39:04.851 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 18:39:04.851 ; Log ; 1 ;  ; 0000: ; 21/8/2025 18:39:04 @Http Req#0
2025/08/21 18:39:04.851 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 18:39:04.851 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 18:39:04.852 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 18:39:04.861 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 18:39:04.864 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 18:39:04.918 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 18:39:04.918 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 18:39:04.990 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 18:39:04.995 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 18:39:05.082 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 18:39:05.086 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 18:39:05.110 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 18:39:05.110 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 18:39:05.110 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 18:39:05.112 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 18:39:05.164 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 18:39:05.164 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 18:39:05.166 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 18:39:05.167 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 18:39:05.173 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 18:39:05.313 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 18:39:05.313 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 18:39:05.314 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 18:39:05.314 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 18:39:05.314 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 18:39:05.314 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 18:39:05.315 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 18:39:05.435 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 18:39:06.822 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:39:06.823 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:39:06.851 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 18:39:06.868 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 18:39:06.888 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:39:06.890 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:39:06.899 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 18:39:10.315 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 18:39:10.315 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 18:39:10.323 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 18:39:14.742 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 18:39:36.195 ; Log ; 13 ; ::1 ; 9900:admin ; Configuration changes applied @Http Req#5 @Req#4 0s
2025/08/21 18:39:49.465 ; Log ; 22 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#9 0s

2025/08/21 18:48:07.805 ; Log ; 19 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#14 0s

2025/08/21 18:56:46.232 ; Error ; 31 ; ::1 ; 9900:admin ;  حقل ( الحساب الرئيسي ) مطلوب <br/> @Req#30 0s ; #at:UserError

2025/08/21 19:19:05.548 ; Log ; 27 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 19:19:05.549 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 19:19:05.549 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 19:19:05.604 ; Log ; 27 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/21 19:37:10.083 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 19:37:10.083 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 19:37:10.084 ; Log ; 1 ;  ; 0000: ; 21/8/2025 19:37:10 @Http Req#0
2025/08/21 19:37:10.084 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 19:37:10.084 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 19:37:10.084 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 19:37:10.091 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 19:37:10.094 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 19:37:10.161 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 19:37:10.161 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 19:37:10.244 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 19:37:10.249 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 19:37:10.356 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 19:37:10.361 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 19:37:10.395 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 19:37:10.395 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 19:37:10.397 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 19:37:10.401 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 19:37:10.438 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 19:37:10.439 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 19:37:10.440 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 19:37:10.441 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 19:37:10.447 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 19:37:10.568 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 19:37:10.568 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 19:37:10.568 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 19:37:10.568 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 19:37:10.568 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 19:37:10.568 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 19:37:10.569 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 19:37:10.694 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 19:37:11.527 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 19:37:11.528 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 19:37:11.550 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 19:37:11.574 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 19:37:11.597 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 19:37:11.597 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 19:37:11.603 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 19:37:15.570 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 19:37:15.570 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 19:37:15.573 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2

2025/08/21 19:58:10.794 ; Log ; 35 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#2
2025/08/21 19:58:10.795 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#2
2025/08/21 19:58:10.795 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#2
2025/08/21 19:58:10.804 ; Log ; 35 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#2

2025/08/21 22:19:35.771 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 22:19:35.772 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 22:19:35.772 ; Log ; 1 ;  ; 0000: ; 21/8/2025 22:19:35 @Http Req#0
2025/08/21 22:19:35.772 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 22:19:35.773 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 22:19:35.773 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 22:19:35.780 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 22:19:35.783 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 22:19:35.854 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 22:19:35.854 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 22:19:35.922 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 22:19:35.927 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 22:19:36.015 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 22:19:36.019 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 22:19:36.048 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 22:19:36.048 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 22:19:36.050 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 22:19:36.053 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 22:19:36.107 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 22:19:36.108 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 22:19:36.110 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 22:19:36.111 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 22:19:36.118 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 22:19:36.235 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 22:19:36.235 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 22:19:36.236 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 22:19:36.236 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 22:19:36.236 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 22:19:36.236 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 22:19:36.237 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 22:19:36.402 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 22:19:37.209 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 22:19:37.212 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 22:19:37.238 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 22:19:37.263 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 22:19:37.277 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 22:19:37.277 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 22:19:37.282 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 22:19:41.237 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 22:19:41.238 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 22:19:41.241 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 22:19:41.273 ; Log ; 8 ;  ; 9900: ; Starting: database backup (20250821_221941_Auto), path=D:\\mohhash\\Backup\\20250821_221941_Auto.bak @Http Req#2
2025/08/21 22:19:41.420 ; Fatal ; 8 ;  ; 9900: ; System.NullReferenceException: Object reference not set to an instance of an object.
   at HS.HSU.Utils.Files.ZipFile(String srcFile, String _outputZipFile)
   at HS.hdb.Tools.DatabaseBackup(String backup_name, String dest_db_backup_file_path) @Http Req#2 ; #at:DatabaseBackup:20250821_221941_Auto
2025/08/21 22:19:41.421 ; Log ; 8 ;  ; 9900: ; Object reference not set to an instance of an object. @Http Req#2 ; #at:DatabaseBackup:20250821_221941_Auto
2025/08/21 22:19:41.425 ; Log ; 8 ;  ; 9900: ; Database backup (20250821_221941_Auto) is failed, path=D:\\mohhash\\Backup\\20250821_221941_Auto.bak @Http Req#2 ; #at:DatabaseBackup
2025/08/21 22:19:41.426 ; Log ; 8 ;  ; 9900: ; Task (24BPU7FHR1W:BackUp) failed:  @Http Req#2
2025/08/21 22:19:44.901 ; Log ; 5 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 22:20:05.488 ; Log ; 16 ; ::1 ; 9900:admin ; Unable to load record (hs_contacts.6003) from db, cmd(add) . It can be authorization issue (fixed_cond=sys_client_id = '9900') @Req#10 0s ; #at:ReadFromStore

2025/08/21 22:41:36.452 ; Log ; 53 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/21 22:41:36.452 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/21 22:41:36.452 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/21 22:41:36.463 ; Log ; 53 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

