'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SearchableAccountSelect } from '@/components/ui/searchable-account-select'
import { FinancialAccountSelect } from '@/components/ui/financial-account-select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Receipt, Plus, Edit, Trash2, Search, Calendar, DollarSign, User, Building, Eye, Printer } from 'lucide-react'

interface PaymentVoucher {
  id: number
  entry_number: string // رقم القيد (يعمل كرقم السند)
  entry_date: string   // تاريخ القيد (يعمل كتاريخ السند)
  beneficiary_name: string
  beneficiary_type: string
  beneficiary_id?: number
  debit_account_id: number  // الحساب المدين (المستفيد)
  credit_account_id: number // الحساب الدائن (الصندوق/البنك)
  amount: number
  currency_id: number
  currency_code?: string
  payment_method_id: number
  payment_method_name?: string
  cost_center_id?: number
  cost_center_name?: string
  description: string
  reference_number?: string
  case_id?: number
  case_number?: string
  service_id?: number
  service_name?: string
  status: string
  created_by_name?: string
  created_at: string
  voucher_type: string // 'payment'
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  linked_table?: string
}

interface Currency {
  id: number
  currency_code: string
  currency_name: string
  symbol: string
}

interface PaymentMethod {
  id: number
  method_code: string
  method_name: string
}

interface Case {
  id: number
  case_number: string
  case_title: string
}

interface CostCenter {
  id: number
  center_code: string
  center_name: string
}

interface Service {
  id: number
  name: string
  lineage_id: number
  lineage_name: string
}

export default function PaymentVouchersPage() {
  const [vouchers, setVouchers] = useState<PaymentVoucher[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [costCenters, setCostCenters] = useState<CostCenter[]>([])
  const [cases, setCases] = useState<Case[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [clients, setClients] = useState<any[]>([])
  const [employees, setEmployees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingVoucher, setEditingVoucher] = useState<PaymentVoucher | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // بيانات النموذج
  const [formData, setFormData] = useState({
    entry_date: new Date().toISOString().split('T')[0], // تاريخ القيد
    debit_account_id: '', // الحساب المدين (الذي يستلم المبلغ)
    credit_account_id: '', // الحساب الدائن (الصندوق/البنك المدفوع منه)
    amount: '',
    currency_id: '1',
    payment_method_id: '1', // نقداً كافتراضي
    cost_center_id: '',
    description: '',
    reference_number: '',
    case_id: '',
    service_id: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)

      // جلب السندات
      const vouchersResponse = await fetch('/api/accounting/payment-vouchers')
      if (vouchersResponse.ok) {
        const vouchersData = await vouchersResponse.json()
        setVouchers(vouchersData.vouchers || [])
      }

      // جلب الحسابات (المستوى 4 فقط + العملاء والموظفين)
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?only_transactional=true&include_linked=true')
      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()
        // ترتيب الحسابات: الحسابات العادية أولاً، ثم العملاء، ثم الموظفين
        const sortedAccounts = accountsData.accounts.sort((a: any, b: any) => {
          if (a.is_linked_record && !b.is_linked_record) return 1
          if (!a.is_linked_record && b.is_linked_record) return -1
          if (a.is_linked_record && b.is_linked_record) {
            if (a.original_table === 'clients' && b.original_table === 'employees') return -1
            if (a.original_table === 'employees' && b.original_table === 'clients') return 1
          }
          return a.account_name.localeCompare(b.account_name, 'ar')
        })
        setAccounts(sortedAccounts)
      }

      // جلب العملات
      const currenciesResponse = await fetch('/api/accounting/currencies')
      if (currenciesResponse.ok) {
        const currenciesData = await currenciesResponse.json()
        setCurrencies(currenciesData.currencies || [])
      }

      // جلب طرق الدفع
      const paymentMethodsResponse = await fetch('/api/accounting/payment-methods')
      if (paymentMethodsResponse.ok) {
        const paymentMethodsData = await paymentMethodsResponse.json()
        setPaymentMethods(paymentMethodsData.methods || [])
      }

      // جلب مراكز التكلفة
      const costCentersResponse = await fetch('/api/cost-centers')
      if (costCentersResponse.ok) {
        const costCentersData = await costCentersResponse.json()
        setCostCenters(costCentersData.centers || [])
      }

      // جلب القضايا
      const casesResponse = await fetch('/api/issues')
      if (casesResponse.ok) {
        const casesData = await casesResponse.json()
        console.log('Cases data:', casesData)
        setCases(casesData.data || casesData.issues || [])
      }

      // جلب الخدمات
      const servicesResponse = await fetch('/api/services')
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        console.log('Services data:', servicesData)
        setServices(servicesData.data || servicesData.services || [])
      }

      // جلب العملاء
      const clientsResponse = await fetch('/api/clients')
      if (clientsResponse.ok) {
        const clientsData = await clientsResponse.json()
        setClients(clientsData.clients || [])
      }

      // جلب الموظفين
      const employeesResponse = await fetch('/api/employees')
      if (employeesResponse.ok) {
        const employeesData = await employeesResponse.json()
        setEmployees(employeesData.employees || [])
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingVoucher
        ? `/api/accounting/payment-vouchers/${editingVoucher.id}`
        : '/api/accounting/payment-vouchers'

      const method = editingVoucher ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          amount: parseFloat(formData.amount),
          debit_account_id: formData.debit_account_id, // سيتم معالجته في الخادم
          credit_account_id: parseInt(formData.credit_account_id),
          currency_id: parseInt(formData.currency_id),
          payment_method_id: formData.payment_method_id ? parseInt(formData.payment_method_id) : null,
          cost_center_id: formData.cost_center_id ? parseInt(formData.cost_center_id) : null,
          beneficiary_id: formData.beneficiary_id ? parseInt(formData.beneficiary_id) : null,
          case_id: formData.case_id ? parseInt(formData.case_id) : null
        }),
      })

      if (response.ok) {
        await fetchData()
        setShowAddDialog(false)
        setEditingVoucher(null)
        resetForm()
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حفظ السند:', error)
      alert('حدث خطأ أثناء حفظ السند')
    }
  }

  const resetForm = () => {
    setFormData({
      entry_date: new Date().toISOString().split('T')[0],
      debit_account_id: '',
      credit_account_id: '',
      amount: '',
      currency_id: '1',
      payment_method_id: '1', // نقداً كافتراضي
      cost_center_id: '',
      description: '',
      reference_number: '',
      case_id: '',
      service_id: ''
    })
  }

  const handleEdit = (voucher: PaymentVoucher) => {
    setEditingVoucher(voucher)
    setFormData({
      entry_date: voucher.entry_date,
      debit_account_id: voucher.debit_account_id.toString(),
      credit_account_id: voucher.credit_account_id.toString(),
      amount: voucher.amount.toString(),
      currency_id: voucher.currency_id.toString(),
      payment_method_id: voucher.payment_method_id?.toString() || '',
      cost_center_id: voucher.cost_center_id?.toString() || '',
      description: voucher.description,
      reference_number: voucher.reference_number || '',
      case_id: voucher.case_id?.toString() || '',
      service_id: voucher.service_id?.toString() || ''
    })
    setShowAddDialog(true)
  }

  const handleView = (voucher: PaymentVoucher) => {
    // إنشاء نافذة جديدة لعرض السند
    const printWindow = window.open('', '_blank', 'width=800,height=600')
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>سند صرف رقم ${voucher.entry_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .voucher-info { border: 2px solid #333; padding: 20px; }
              .row { display: flex; justify-content: space-between; margin: 10px 0; text-align: right; }
              .label { font-weight: bold; text-align: right; }
              .amount { font-size: 18px; font-weight: bold; color: #d32f2f; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>سند صرف</h1>
              <h2>رقم: ${voucher.entry_number}</h2>
            </div>
            <div class="voucher-info">
              <div class="row">
                <span class="label">التاريخ:</span>
                <span>${new Date(voucher.entry_date).toLocaleDateString('en-GB')}</span>
              </div>
              <div class="row">
                <span class="label">المبلغ:</span>
                <span class="amount">${(voucher.amount || voucher.total_debit || 0).toLocaleString()} ${voucher.currency_code || 'ر.ي'}</span>
              </div>
              <div class="row">
                <span class="label">المستفيد:</span>
                <span>${voucher.enhanced_debit_name || voucher.beneficiary_name}</span>
              </div>
              <div class="row">
                <span class="label">الحساب الدائن:</span>
                <span>${voucher.credit_account_name}</span>
              </div>
              <div class="row">
                <span class="label">البيان:</span>
                <span>${voucher.description}</span>
              </div>
              <div class="row">
                <span class="label">طريقة الدفع:</span>
                <span>${voucher.payment_method_name || 'نقد'}</span>
              </div>
              ${voucher.reference_number ? `
              <div class="row">
                <span class="label">رقم المرجع:</span>
                <span>${voucher.reference_number}</span>
              </div>
              ` : ''}
            </div>
          </body>
        </html>
      `)
      printWindow.document.close()
    }
  }

  const handleDelete = async (voucherId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا السند؟')) {
      return
    }

    try {
      const response = await fetch(`/api/accounting/payment-vouchers/${voucherId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchData()
        alert('تم حذف السند بنجاح')
      } else {
        const errorData = await response.json()
        alert(`خطأ في حذف السند: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في حذف السند:', error)
      alert('حدث خطأ أثناء حذف السند')
    }
  }

  const handlePrint = (voucher: PaymentVoucher) => {
    // إنشاء نافذة طباعة للسند المحدد
    const printWindow = window.open('', '_blank', 'width=800,height=600')
    if (printWindow) {
      printWindow.document.write(`
        <html dir="rtl">
          <head>
            <title>طباعة سند صرف رقم ${voucher.entry_number}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .voucher-info { border: 2px solid #333; padding: 20px; }
              .row { display: flex; justify-content: space-between; margin: 15px 0; text-align: right; }
              .label { font-weight: bold; width: 150px; text-align: right; }
              .value { flex: 1; border-bottom: 1px dotted #333; padding-bottom: 5px; text-align: right; }
              .amount { font-size: 20px; font-weight: bold; color: #d32f2f; text-align: center; }
              .signature { margin-top: 50px; display: flex; justify-content: space-between; }
              .signature div { text-align: center; width: 200px; }
              .signature-line { border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; }
              @media print {
                body { margin: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>سند صرف</h1>
              <h2>رقم: ${voucher.entry_number}</h2>
            </div>
            <div class="voucher-info">
              <div class="row">
                <span class="label">التاريخ:</span>
                <span class="value">${new Date(voucher.entry_date).toLocaleDateString('en-GB')}</span>
              </div>
              <div class="row">
                <span class="label">المبلغ:</span>
                <span class="value amount">${(voucher.amount || voucher.total_debit || 0).toLocaleString()} ${voucher.currency_code || 'ر.ي'}</span>
              </div>
              <div class="row">
                <span class="label">صرف إلى:</span>
                <span class="value">${voucher.enhanced_debit_name || voucher.debit_account_name || voucher.beneficiary_name}</span>
                ${voucher.enhanced_debit_name && voucher.enhanced_debit_name !== voucher.debit_account_name ?
                  `<br><small style="color: #666;">${voucher.debit_account_name}</small>` : ''}
              </div>
              <div class="row">
                <span class="label">من حساب:</span>
                <span class="value">${voucher.credit_account_name}</span>
              </div>
              <div class="row">
                <span class="label">البيان:</span>
                <span class="value">${voucher.description}</span>
              </div>
              <div class="row">
                <span class="label">طريقة الدفع:</span>
                <span class="value">${voucher.payment_method_name || 'نقد'}</span>
              </div>
              ${voucher.reference_number ? `
              <div class="row">
                <span class="label">رقم المرجع:</span>
                <span class="value">${voucher.reference_number}</span>
              </div>
              ` : ''}
            </div>
            <div class="signature">
              <div>
                <div class="signature-line">المحاسب</div>
              </div>
              <div>
                <div class="signature-line">المستلم</div>
              </div>
              <div>
                <div class="signature-line">المدير المالي</div>
              </div>
            </div>
            <script>
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                }
              }
            </script>
          </body>
        </html>
      `)
      printWindow.document.close()
    }
  }

  const filteredVouchers = vouchers.filter(voucher =>
    voucher.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (voucher.enhanced_debit_name || voucher.debit_account_name || voucher.beneficiary_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    voucher.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'draft': { label: 'مسودة', variant: 'secondary' as const },
      'approved': { label: 'معتمد', variant: 'default' as const },
      'cancelled': { label: 'ملغي', variant: 'destructive' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Receipt className="h-8 w-8 text-red-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">سندات الصرف</h1>
              <p className="text-gray-600">إدارة سندات الصرف والمدفوعات</p>
            </div>
          </div>

          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="h-4 w-4 ml-2" />
            سند صرف جديد
          </Button>
        </div>

        {/* جدول السندات */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
            <CardTitle>سندات الصرف</CardTitle>
            <div className="relative w-80">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في سندات الصرف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل السندات...</p>
              </div>
            ) : filteredVouchers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Receipt className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد سندات صرف</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-right p-3 font-semibold text-gray-700">رقم السند</th>
                      <th className="text-right p-3 font-semibold text-gray-700">التاريخ</th>
                      <th className="text-right p-3 font-semibold text-gray-700">المبلغ</th>
                      <th className="text-right p-3 font-semibold text-gray-700">الدافع</th>
                      <th className="text-right p-3 font-semibold text-gray-700">المستفيد</th>
                      <th className="text-right p-3 font-semibold text-gray-700">البيان</th>
                      <th className="text-center p-3 font-semibold text-gray-700">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredVouchers.map((voucher, index) => (
                      <tr key={`voucher-${voucher.id}-${index}`} className={`border-b hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'}`}>
                        <td className="p-3">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="font-mono text-blue-600 font-medium">
                              {voucher.entry_number}
                            </span>
                            {getStatusBadge(voucher.status)}
                          </div>
                        </td>
                        <td className="p-3 text-gray-600">
                          {new Date(voucher.entry_date).toLocaleDateString('en-GB')}
                        </td>
                        <td className="p-3">
                          <span className="font-bold text-red-600">
                            {(voucher.amount || voucher.total_debit || 0).toLocaleString()} {voucher.currency_code || 'ر.ي'}
                          </span>
                        </td>
                        <td className="p-3 text-gray-700">
                          <span className="font-medium">
                            {voucher.enhanced_credit_name || 'غير محدد'}
                          </span>
                        </td>
                        <td className="p-3 text-gray-700">
                          <span className="font-medium">
                            {voucher.enhanced_debit_name || voucher.beneficiary_name || 'غير محدد'}
                          </span>
                        </td>
                        <td className="p-3 text-gray-600 max-w-xs truncate" title={voucher.description}>
                          {voucher.description}
                        </td>
                        <td className="p-3">
                          <div className="flex justify-center space-x-1 space-x-reverse">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleView(voucher)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                              title="مشاهدة"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEdit(voucher)}
                              className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                              title="تعديل"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(voucher.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title="حذف"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePrint(voucher)}
                              className="text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                              title="طباعة"
                            >
                              <Printer className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة/تعديل السند */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-[95vw] max-h-[95vh] w-[95vw] overflow-y-auto">
            <DialogHeader className="bg-gradient-to-r from-red-50 to-red-100 p-4 border-b">
              <DialogTitle className="text-2xl font-bold text-red-800 flex items-center">
                <Receipt className="ml-2 h-6 w-6" />
                {editingVoucher ? 'تعديل سند الصرف' : 'سند صرف جديد'}
              </DialogTitle>
              <DialogDescription className="text-red-600 mt-2">
                {editingVoucher ? 'تعديل بيانات سند الصرف المحدد' : 'إنشاء سند صرف جديد مع تفاصيل المبلغ والحساب'}
              </DialogDescription>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto p-4">
              <form onSubmit={handleSubmit} className="space-y-6">
              {/* الصف الأول: البيانات الأساسية */}
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="entry_date" className="text-sm font-semibold text-green-700 bg-green-50 px-2 py-1 rounded-md inline-block mb-2">
                    📅 تاريخ السند
                  </Label>
                  <Input
                    id="entry_date"
                    type="date"
                    value={formData.entry_date}
                    onChange={(e) => setFormData({...formData, entry_date: e.target.value})}
                    required
                    className="bg-gray-50 border-gray-300 focus:border-green-500 focus:bg-white transition-colors"
                  />
                </div>

                <div>
                  <Label htmlFor="amount" className="text-sm font-semibold text-red-700 bg-red-50 px-2 py-1 rounded-md inline-block mb-2">
                    💰 المبلغ
                  </Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount}
                    onChange={(e) => setFormData({...formData, amount: e.target.value})}
                    required
                    placeholder="0.00"
                    className="bg-gray-50 border-gray-300 focus:border-red-500 focus:bg-white transition-colors text-right font-medium"
                  />
                </div>

                <div>
                  <Label htmlFor="currency_id" className="text-sm font-semibold text-orange-700 bg-orange-50 px-2 py-1 rounded-md inline-block mb-2">
                    💱 العملة
                  </Label>
                  <Select value={formData.currency_id} onValueChange={(value) => setFormData({...formData, currency_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-orange-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="اختر العملة" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.id} value={currency.id.toString()}>
                          <span className="font-medium">{currency.currency_code}</span> - {currency.currency_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="payment_method_id" className="text-sm font-semibold text-indigo-700 bg-indigo-50 px-2 py-1 rounded-md inline-block mb-2">
                    💳 طريقة الدفع
                  </Label>
                  <Select value={formData.payment_method_id} onValueChange={(value) => setFormData({...formData, payment_method_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-indigo-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="اختر طريقة الدفع" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id.toString()}>
                          {method.method_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* الصف الثاني: مركز التكلفة والخدمة */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="cost_center_id" className="text-sm font-semibold text-orange-700 bg-orange-50 px-2 py-1 rounded-md inline-block mb-2">
                    🏢 مركز التكلفة
                  </Label>
                  <Select value={formData.cost_center_id} onValueChange={(value) => setFormData({...formData, cost_center_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-orange-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="🔍 اختر مركز التكلفة..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون مركز تكلفة</SelectItem>
                      {costCenters.map((center) => (
                        <SelectItem key={center.id} value={center.id.toString()}>
                          <div className="flex flex-col py-1">
                            <span className="font-medium text-orange-700">{center.center_code}</span>
                            <span className="text-sm text-gray-600">{center.center_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="service_id" className="text-sm font-semibold text-teal-700 bg-teal-50 px-2 py-1 rounded-md inline-block mb-2">
                    🔧 الخدمة
                  </Label>
                  <Select value={formData.service_id} onValueChange={(value) => setFormData({...formData, service_id: value})}>
                    <SelectTrigger className="bg-gray-50 border-gray-300 focus:border-teal-500 focus:bg-white transition-colors">
                      <SelectValue placeholder="🔍 اختر الخدمة..." />
                    </SelectTrigger>
                    <SelectContent className="max-h-60">
                      <SelectItem value="0">بدون خدمة</SelectItem>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id.toString()}>
                          <div className="flex flex-col py-1">
                            <span className="font-medium text-teal-700">{service.name}</span>
                            <span className="text-sm text-gray-600">{service.lineage_name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>



              {/* الصف الثالث: الحسابات */}
              <div className="grid grid-cols-2 gap-4">
                <SearchableAccountSelect
                  value={formData.debit_account_id}
                  onChange={(value) => setFormData({...formData, debit_account_id: value})}
                  label="👥 المستفيد (العملاء والموظفين والموردين)"
                  placeholder="اختر المستفيد أو ابحث..."
                  required
                />

                <FinancialAccountSelect
                  value={formData.credit_account_id}
                  onChange={(value) => setFormData({...formData, credit_account_id: value})}
                  label="🏦 الحساب الدائن (المدفوع منه)"
                  placeholder="اختر الصندوق أو البنك..."
                  accountTypes={['أصول']} // صناديق وبنوك فقط
                  required
                />
              </div>

              {/* الصف الخامس: البيان */}
              <div>
                <Label htmlFor="description" className="text-sm font-semibold text-gray-700 bg-gray-100 px-2 py-1 rounded-md inline-block mb-2">
                  📝 البيان
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  required
                  placeholder="اكتب وصف العملية..."
                  className="bg-gray-50 border-gray-300 focus:border-gray-500 focus:bg-white transition-colors min-h-[80px]"
                />
              </div>



              <DialogFooter className="flex justify-between items-center pt-6 border-t">
                <div className="flex space-x-2 space-x-reverse">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddDialog(false)}
                    className="px-6"
                  >
                    ❌ إلغاء
                  </Button>
                  {editingVoucher && (
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={() => {
                        // طباعة السند
                        window.print()
                      }}
                      className="px-6"
                    >
                      🖨️ طباعة
                    </Button>
                  )}
                </div>
                <Button
                  type="submit"
                  className="px-8 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
                >
                  {editingVoucher ? '✅ تحديث السند' : '💾 حفظ السند'}
                </Button>
              </DialogFooter>
              </form>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
