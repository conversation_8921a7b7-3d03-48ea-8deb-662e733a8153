import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب بيانات التقارير
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'last_6_months'
    const reportType = searchParams.get('report_type') || 'overview'
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // تحديد نطاق التواريخ بناءً على الفترة
    let dateFilter = ''
    let dateParams: any[] = []
    let paramIndex = 1

    if (startDate && endDate) {
      dateFilter = `AND created_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`
      dateParams = [startDate, endDate]
      paramIndex += 2
    } else {
      switch (period) {
        case 'last_month':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '1 month'`
          break
        case 'last_3_months':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '3 months'`
          break
        case 'last_6_months':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '6 months'`
          break
        case 'last_year':
          dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '1 year'`
          break
      }
    }

    // الملخص العام
    const summaryQuery = `
      SELECT 
        (SELECT COUNT(*) FROM issues WHERE 1=1 ${dateFilter}) as total_cases,
        (SELECT COUNT(*) FROM issues WHERE status = 'active' ${dateFilter}) as active_cases,
        (SELECT COUNT(*) FROM issues WHERE status = 'completed' ${dateFilter}) as completed_cases,
        (SELECT COUNT(DISTINCT client_id) FROM issues WHERE 1=1 ${dateFilter}) as total_clients,
        (SELECT COALESCE(SUM(total_amount), 0) FROM invoices WHERE payment_status = 'paid' ${dateFilter}) as total_revenue,
        (SELECT COUNT(*) FROM invoices WHERE payment_status = 'unpaid' ${dateFilter}) as pending_invoices,
        (SELECT COALESCE(SUM(duration_minutes), 0) / 60.0 FROM time_entries WHERE 1=1 ${dateFilter}) as total_hours,
        (SELECT COALESCE(SUM(duration_minutes), 0) / 60.0 FROM time_entries WHERE is_billable = true ${dateFilter}) as billable_hours
    `

    const summaryResult = await query(summaryQuery, dateParams)
    const summary = summaryResult.rows[0]

    // الإحصائيات الشهرية
    const monthlyStatsQuery = `
      SELECT 
        TO_CHAR(created_date, 'YYYY-MM') as month,
        COUNT(*) as cases,
        COALESCE(SUM(amount), 0) as revenue,
        0 as hours
      FROM issues 
      WHERE 1=1 ${dateFilter}
      GROUP BY TO_CHAR(created_date, 'YYYY-MM')
      ORDER BY month DESC
      LIMIT 12
    `

    const monthlyStatsResult = await query(monthlyStatsQuery, dateParams)

    // إضافة بيانات الساعات للإحصائيات الشهرية
    const monthlyHoursQuery = `
      SELECT 
        TO_CHAR(start_time, 'YYYY-MM') as month,
        COALESCE(SUM(duration_minutes), 0) / 60.0 as hours
      FROM time_entries 
      WHERE 1=1 ${dateFilter.replace('created_date', 'start_time')}
      GROUP BY TO_CHAR(start_time, 'YYYY-MM')
    `

    const monthlyHoursResult = await query(monthlyHoursQuery, dateParams)
    const hoursMap = monthlyHoursResult.rows.reduce((acc: any, row: any) => {
      acc[row.month] = parseFloat(row.hours)
      return acc
    }, {})

    const monthlyStats = monthlyStatsResult.rows.map((row: any) => ({
      month: row.month,
      cases: parseInt(row.cases),
      revenue: parseFloat(row.revenue),
      hours: hoursMap[row.month] || 0
    }))

    // أنواع القضايا
    const caseTypesQuery = `
      SELECT 
        it.name as type,
        COUNT(i.id) as count,
        (COUNT(i.id) * 100.0 / (SELECT COUNT(*) FROM issues WHERE 1=1 ${dateFilter})) as percentage
      FROM issue_types it
      LEFT JOIN issues i ON it.id = i.issue_type_id ${dateFilter.replace('created_date', 'i.created_date')}
      GROUP BY it.id, it.name
      HAVING COUNT(i.id) > 0
      ORDER BY count DESC
    `

    const caseTypesResult = await query(caseTypesQuery, dateParams)
    const caseTypes = caseTypesResult.rows.map((row: any) => ({
      type: row.type,
      count: parseInt(row.count),
      percentage: parseFloat(row.percentage)
    }))

    // إحصائيات العملاء
    const clientStatsQuery = `
      SELECT 
        c.name as client_name,
        COUNT(i.id) as cases_count,
        COALESCE(SUM(i.amount), 0) as total_amount,
        MAX(i.updated_at) as last_activity
      FROM clients c
      LEFT JOIN issues i ON c.id = i.client_id ${dateFilter.replace('created_date', 'i.created_date')}
      GROUP BY c.id, c.name
      HAVING COUNT(i.id) > 0
      ORDER BY total_amount DESC
      LIMIT 10
    `

    const clientStatsResult = await query(clientStatsQuery, dateParams)
    const clientStats = clientStatsResult.rows.map((row: any) => ({
      client_name: row.client_name,
      cases_count: parseInt(row.cases_count),
      total_amount: parseFloat(row.total_amount),
      last_activity: row.last_activity
    }))

    // أداء الموظفين
    const employeePerformanceQuery = `
      SELECT 
        e.name as employee_name,
        COUNT(DISTINCT i.id) as cases_handled,
        COALESCE(SUM(te.duration_minutes), 0) / 60.0 as hours_worked,
        COALESCE(SUM(te.billable_amount), 0) as revenue_generated,
        CASE 
          WHEN COUNT(DISTINCT i.id) > 0 THEN 
            LEAST(100, (COUNT(DISTINCT CASE WHEN i.status = 'completed' THEN i.id END) * 100.0 / COUNT(DISTINCT i.id)))
          ELSE 0 
        END as efficiency_score
      FROM employees e
      LEFT JOIN issues i ON e.id = i.assigned_to ${dateFilter.replace('created_date', 'i.created_date')}
      LEFT JOIN time_entries te ON e.id = te.employee_id ${dateFilter.replace('created_date', 'te.start_time')}
      GROUP BY e.id, e.name
      HAVING COUNT(DISTINCT i.id) > 0 OR SUM(te.duration_minutes) > 0
      ORDER BY revenue_generated DESC
      LIMIT 10
    `

    const employeePerformanceResult = await query(employeePerformanceQuery, dateParams.concat(dateParams))
    const employeePerformance = employeePerformanceResult.rows.map((row: any) => ({
      employee_name: row.employee_name,
      cases_handled: parseInt(row.cases_handled),
      hours_worked: parseFloat(row.hours_worked),
      revenue_generated: parseFloat(row.revenue_generated),
      efficiency_score: parseFloat(row.efficiency_score)
    }))

    // النظرة المالية
    const monthlyRevenueQuery = `
      SELECT 
        TO_CHAR(invoice_date, 'YYYY-MM') as month,
        COALESCE(SUM(total_amount), 0) as revenue,
        0 as expenses,
        COALESCE(SUM(total_amount), 0) as profit
      FROM invoices 
      WHERE payment_status = 'paid' ${dateFilter.replace('created_date', 'invoice_date')}
      GROUP BY TO_CHAR(invoice_date, 'YYYY-MM')
      ORDER BY month DESC
      LIMIT 12
    `

    const monthlyRevenueResult = await query(monthlyRevenueQuery, dateParams)
    const monthlyRevenue = monthlyRevenueResult.rows.map((row: any) => ({
      month: row.month,
      revenue: parseFloat(row.revenue),
      expenses: parseFloat(row.expenses),
      profit: parseFloat(row.profit)
    }))

    // حالة المدفوعات
    const paymentStatusQuery = `
      SELECT 
        COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END), 0) as paid,
        COALESCE(SUM(CASE WHEN payment_status = 'unpaid' THEN total_amount ELSE 0 END), 0) as pending,
        COALESCE(SUM(CASE WHEN payment_status = 'overdue' THEN total_amount ELSE 0 END), 0) as overdue
      FROM invoices 
      WHERE 1=1 ${dateFilter.replace('created_date', 'invoice_date')}
    `

    const paymentStatusResult = await query(paymentStatusQuery, dateParams)
    const paymentStatus = {
      paid: parseFloat(paymentStatusResult.rows[0].paid),
      pending: parseFloat(paymentStatusResult.rows[0].pending),
      overdue: parseFloat(paymentStatusResult.rows[0].overdue)
    }

    // تجميع البيانات
    const reportData = {
      summary: {
        total_cases: parseInt(summary.total_cases),
        active_cases: parseInt(summary.active_cases),
        completed_cases: parseInt(summary.completed_cases),
        total_clients: parseInt(summary.total_clients),
        total_revenue: parseFloat(summary.total_revenue),
        pending_invoices: parseInt(summary.pending_invoices),
        total_hours: parseFloat(summary.total_hours),
        billable_hours: parseFloat(summary.billable_hours)
      },
      monthly_stats: monthlyStats,
      case_types: caseTypes,
      client_stats: clientStats,
      employee_performance: employeePerformance,
      financial_overview: {
        monthly_revenue: monthlyRevenue,
        payment_status: paymentStatus
      }
    }

    return NextResponse.json({
      success: true,
      data: reportData
    })

  } catch (error) {
    console.error('خطأ في جلب بيانات التقارير:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات التقارير' },
      { status: 500 }
    )
  }
}