#!/bin/bash

# سكريبت إعداد التشغيل التلقائي باستخدام crontab
# Legal System Auto-Start Setup using crontab

echo "🚀 إعداد التشغيل التلقائي للنظام القانوني..."
echo "=============================================="

PROJECT_DIR="/home/<USER>/Downloads/legal-system"
STARTUP_SCRIPT="$PROJECT_DIR/scripts/start-legal-system.sh"

# التحقق من وجود السكريبت
if [ ! -f "$STARTUP_SCRIPT" ]; then
    echo "❌ سكريبت البدء غير موجود: $STARTUP_SCRIPT"
    exit 1
fi

echo "✅ تم العثور على سكريبت البدء"

# إنشاء سكريبت التشغيل التلقائي
AUTOSTART_SCRIPT="/home/<USER>/autostart-legal-system.sh"

cat > "$AUTOSTART_SCRIPT" << EOF
#!/bin/bash
# سكريبت التشغيل التلقائي للنظام القانوني

# انتظار حتى يصبح النظام جاهز
sleep 30

# تسجيل بدء التشغيل
echo "\$(date): بدء تشغيل النظام القانوني تلقائياً" >> /home/<USER>/autostart.log

# بدء تشغيل النظام
cd "$PROJECT_DIR"
./scripts/start-legal-system.sh >> /home/<USER>/autostart.log 2>&1

# تسجيل انتهاء التشغيل
echo "\$(date): انتهى تشغيل سكريبت البدء التلقائي" >> /home/<USER>/autostart.log
EOF

chmod +x "$AUTOSTART_SCRIPT"
echo "✅ تم إنشاء سكريبت التشغيل التلقائي: $AUTOSTART_SCRIPT"

# إضافة مهمة crontab للتشغيل عند بدء النظام
echo "📅 إضافة مهمة crontab..."

# إنشاء ملف crontab مؤقت
TEMP_CRON=$(mktemp)

# الحصول على crontab الحالي (إن وجد)
crontab -l 2>/dev/null > "$TEMP_CRON"

# التحقق من وجود المهمة مسبقاً
if grep -q "autostart-legal-system.sh" "$TEMP_CRON"; then
    echo "⚠️  مهمة التشغيل التلقائي موجودة مسبقاً"
else
    # إضافة المهمة الجديدة
    echo "@reboot $AUTOSTART_SCRIPT" >> "$TEMP_CRON"
    
    # تطبيق crontab الجديد
    crontab "$TEMP_CRON"
    
    if [ $? -eq 0 ]; then
        echo "✅ تم إضافة مهمة التشغيل التلقائي بنجاح"
    else
        echo "❌ فشل في إضافة مهمة التشغيل التلقائي"
        rm -f "$TEMP_CRON"
        exit 1
    fi
fi

# تنظيف الملف المؤقت
rm -f "$TEMP_CRON"

echo ""
echo "📋 مهام crontab الحالية:"
crontab -l

echo ""
echo "🎉 تم إعداد التشغيل التلقائي بنجاح!"
echo "=================================="
echo "📁 مجلد المشروع: $PROJECT_DIR"
echo "🚀 سكريبت التشغيل: $AUTOSTART_SCRIPT"
echo "📝 ملف السجل: /home/<USER>/autostart.log"

echo ""
echo "🔄 سيتم تشغيل النظام تلقائياً عند:"
echo "   - إعادة تشغيل الجهاز"
echo "   - بدء تشغيل النظام"

echo ""
echo "🎛️ أوامر الإدارة:"
echo "=================="
echo "▶️  بدء النظام الآن:    ./scripts/start-legal-system.sh"
echo "⏹️  إيقاف النظام:      ./scripts/stop-legal-system.sh"
echo "🔄 إعادة تشغيل:        ./scripts/restart-legal-system.sh"
echo "📊 حالة النظام:        ./scripts/status-legal-system.sh"
echo "📋 سجل التشغيل التلقائي: tail -f /home/<USER>/autostart.log"

echo ""
echo "🌐 الوصول للنظام:"
echo "=================="
echo "🏠 الرابط: http://localhost:7443"
echo "👤 المدير: admin"
echo "🔑 كلمة المرور: ana8080"

echo ""
echo "⚠️  ملاحظات مهمة:"
echo "=================="
echo "• سيبدأ النظام تلقائياً بعد 30 ثانية من بدء تشغيل الجهاز"
echo "• تأكد من أن قاعدة البيانات PostgreSQL تعمل تلقائياً"
echo "• يمكنك مراقبة التشغيل التلقائي من خلال ملف السجل"

# اختبار التشغيل الآن
echo ""
echo "🧪 اختبار التشغيل الآن..."
./scripts/start-legal-system.sh
