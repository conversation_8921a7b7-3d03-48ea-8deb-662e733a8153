-- إنشاء مستخدم admin إذا لم يكن موجوداً
INSERT INTO users (username, email, role, employee_id, created_at, updated_at, is_active, last_login, online_status, device_id)
VALUES (
    'admin',
    '<EMAIL>',
    'admin',
    NULL,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    true,
    NULL,
    false,
    NULL
)
ON CONFLICT (username) DO NOTHING;

-- تحديث دور المستخدم admin إذا كان موجوداً
UPDATE users SET role = 'admin' WHERE username = 'admin';
