{"version": 3, "sources": ["../../../src/server/node-environment-extensions/utils.tsx"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  abortOnSynchronousPlatformIOAccess,\n  trackSynchronousPlatformIOAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype ApiType = 'time' | 'random' | 'crypto'\n\nexport function io(expression: string, type: ApiType) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workUnitStore || !workStore) {\n    return\n  }\n\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime': {\n      const prerenderSignal = workUnitStore.controller.signal\n\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct\n        // any stacks because something else actually terminated the prerender.\n        let message: string\n        switch (type) {\n          case 'time':\n            message = `Route \"${workStore.route}\" used ${expression} instead of using \\`performance\\` or without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time`\n            break\n          case 'random':\n            message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-random`\n            break\n          case 'crypto':\n            message = `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto`\n            break\n          default:\n            throw new InvariantError(\n              'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n            )\n        }\n\n        abortOnSynchronousPlatformIOAccess(\n          workStore.route,\n          expression,\n          applyOwnerStack(new Error(message), workUnitStore),\n          workUnitStore\n        )\n      }\n      break\n    }\n    case 'prerender-client': {\n      const prerenderSignal = workUnitStore.controller.signal\n\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct\n        // any stacks because something else actually terminated the prerender.\n        let message: string\n        switch (type) {\n          case 'time':\n            message = `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time-client`\n            break\n          case 'random':\n            message = `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-random-client`\n            break\n          case 'crypto':\n            message = `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto-client`\n            break\n          default:\n            throw new InvariantError(\n              'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n            )\n        }\n\n        abortOnSynchronousPlatformIOAccess(\n          workStore.route,\n          expression,\n          applyOwnerStack(new Error(message), workUnitStore),\n          workUnitStore\n        )\n      }\n      break\n    }\n    case 'request':\n      if (workUnitStore.prerenderPhase === true) {\n        trackSynchronousPlatformIOAccessInDev(workUnitStore)\n      }\n      break\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction applyOwnerStack(error: Error, workUnitStore: PrerenderStoreModern) {\n  // TODO: Instead of stitching the stacks here, we should log the original\n  // error as-is when it occurs, and let `patchErrorInspect` handle adding the\n  // owner stack, instead of logging it deferred in the `LogSafely` component\n  // via `throwIfDisallowedDynamic`.\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    workUnitStore.captureOwnerStack\n  ) {\n    const ownerStack = workUnitStore.captureOwnerStack()\n\n    if (ownerStack) {\n      let stack = ownerStack\n\n      if (error.stack) {\n        const frames: string[] = []\n\n        for (const frame of error.stack.split('\\n').slice(1)) {\n          if (frame.includes('react_stack_bottom_frame')) {\n            break\n          }\n\n          frames.push(frame)\n        }\n\n        stack = '\\n' + frames.join('\\n') + stack\n      }\n\n      error.stack = error.name + ': ' + error.message + stack\n    }\n  }\n\n  return error\n}\n"], "names": ["workAsyncStorage", "workUnitAsyncStorage", "abortOnSynchronousPlatformIOAccess", "trackSynchronousPlatformIOAccessInDev", "InvariantError", "io", "expression", "type", "workUnitStore", "getStore", "workStore", "prerenderSignal", "controller", "signal", "aborted", "message", "route", "applyOwnerStack", "Error", "prerenderPhase", "error", "process", "env", "NODE_ENV", "captureOwnerStack", "ownerStack", "stack", "frames", "frame", "split", "slice", "includes", "push", "join", "name"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,4CAA2C;AAC5E,SACEC,oBAAoB,QAEf,iDAAgD;AACvD,SACEC,kCAAkC,EAClCC,qCAAqC,QAChC,kCAAiC;AACxC,SAASC,cAAc,QAAQ,mCAAkC;AAIjE,OAAO,SAASC,GAAGC,UAAkB,EAAEC,IAAa;IAClD,MAAMC,gBAAgBP,qBAAqBQ,QAAQ;IACnD,MAAMC,YAAYV,iBAAiBS,QAAQ;IAE3C,IAAI,CAACD,iBAAiB,CAACE,WAAW;QAChC;IACF;IAEA,OAAQF,cAAcD,IAAI;QACxB,KAAK;QACL,KAAK;YAAqB;gBACxB,MAAMI,kBAAkBH,cAAcI,UAAU,CAACC,MAAM;gBAEvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;oBACrC,wEAAwE;oBACxE,uEAAuE;oBACvE,IAAIC;oBACJ,OAAQR;wBACN,KAAK;4BACHQ,UAAU,CAAC,OAAO,EAAEL,UAAUM,KAAK,CAAC,OAAO,EAAEV,WAAW,mLAAmL,CAAC;4BAC5O;wBACF,KAAK;4BACHS,UAAU,CAAC,OAAO,EAAEL,UAAUM,KAAK,CAAC,OAAO,EAAEV,WAAW,wKAAwK,CAAC;4BACjO;wBACF,KAAK;4BACHS,UAAU,CAAC,OAAO,EAAEL,UAAUM,KAAK,CAAC,OAAO,EAAEV,WAAW,wKAAwK,CAAC;4BACjO;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIF,eACR,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBAEAF,mCACEQ,UAAUM,KAAK,EACfV,YACAW,gBAAgB,qBAAkB,CAAlB,IAAIC,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB,IAAGP,gBACpCA;gBAEJ;gBACA;YACF;QACA,KAAK;YAAoB;gBACvB,MAAMG,kBAAkBH,cAAcI,UAAU,CAACC,MAAM;gBAEvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;oBACrC,wEAAwE;oBACxE,uEAAuE;oBACvE,IAAIC;oBACJ,OAAQR;wBACN,KAAK;4BACHQ,UAAU,CAAC,OAAO,EAAEL,UAAUM,KAAK,CAAC,OAAO,EAAEV,WAAW,wJAAwJ,CAAC;4BACjN;wBACF,KAAK;4BACHS,UAAU,CAAC,OAAO,EAAEL,UAAUM,KAAK,CAAC,OAAO,EAAEV,WAAW,kJAAkJ,CAAC;4BAC3M;wBACF,KAAK;4BACHS,UAAU,CAAC,OAAO,EAAEL,UAAUM,KAAK,CAAC,OAAO,EAAEV,WAAW,kJAAkJ,CAAC;4BAC3M;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIF,eACR,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBAEAF,mCACEQ,UAAUM,KAAK,EACfV,YACAW,gBAAgB,qBAAkB,CAAlB,IAAIC,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB,IAAGP,gBACpCA;gBAEJ;gBACA;YACF;QACA,KAAK;YACH,IAAIA,cAAcW,cAAc,KAAK,MAAM;gBACzChB,sCAAsCK;YACxC;YACA;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH;QACF;YACEA;IACJ;AACF;AAEA,SAASS,gBAAgBG,KAAY,EAAEZ,aAAmC;IACxE,yEAAyE;IACzE,4EAA4E;IAC5E,2EAA2E;IAC3E,kCAAkC;IAClC,IACEa,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBf,cAAcgB,iBAAiB,EAC/B;QACA,MAAMC,aAAajB,cAAcgB,iBAAiB;QAElD,IAAIC,YAAY;YACd,IAAIC,QAAQD;YAEZ,IAAIL,MAAMM,KAAK,EAAE;gBACf,MAAMC,SAAmB,EAAE;gBAE3B,KAAK,MAAMC,SAASR,MAAMM,KAAK,CAACG,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAI;oBACpD,IAAIF,MAAMG,QAAQ,CAAC,6BAA6B;wBAC9C;oBACF;oBAEAJ,OAAOK,IAAI,CAACJ;gBACd;gBAEAF,QAAQ,OAAOC,OAAOM,IAAI,CAAC,QAAQP;YACrC;YAEAN,MAAMM,KAAK,GAAGN,MAAMc,IAAI,GAAG,OAAOd,MAAML,OAAO,GAAGW;QACpD;IACF;IAEA,OAAON;AACT", "ignoreList": [0]}