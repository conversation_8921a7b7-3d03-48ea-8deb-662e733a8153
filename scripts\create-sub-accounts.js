const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function createSubAccounts() {
  const client = await pool.connect();
  
  try {
    console.log('🔗 إنشاء الحسابات الفرعية للعملاء والموظفين...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. إنشاء حسابات فرعية للعملاء
    console.log('👥 إنشاء حسابات العملاء...');
    
    // البحث عن حساب العملاء الرئيسي
    const clientsMainAccount = await client.query(`
      SELECT coa.id, coa.account_code, coa.account_name 
      FROM main_accounts ma
      JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      WHERE ma.account_name = 'حسابات العملاء'
    `);

    if (clientsMainAccount.rows.length > 0) {
      const parentAccount = clientsMainAccount.rows[0];
      console.log(`✅ تم العثور على حساب العملاء الرئيسي: ${parentAccount.account_code} - ${parentAccount.account_name}`);

      // جلب جميع العملاء
      const clients = await client.query('SELECT id, name FROM clients WHERE status = \'active\'');
      console.log(`📋 سيتم إنشاء ${clients.rows.length} حساب للعملاء`);

      let clientAccountsCreated = 0;
      for (const client_record of clients.rows) {
        try {
          // إنشاء كود حساب فرعي للعميل
          const subAccountCode = `${parentAccount.account_code}${String(client_record.id).padStart(3, '0')}`;
          
          // التحقق من عدم وجود الحساب مسبقاً
          const existingAccount = await client.query(
            'SELECT id FROM chart_of_accounts WHERE account_code = $1',
            [subAccountCode]
          );

          if (existingAccount.rows.length === 0) {
            // إنشاء الحساب الفرعي
            const result = await client.query(`
              INSERT INTO chart_of_accounts (
                account_code, account_name, account_type, account_nature, 
                account_level, parent_id, allow_transactions, is_active, created_date
              ) VALUES ($1, $2, $3, $4, $5, $6, true, true, CURRENT_DATE)
              RETURNING id
            `, [
              subAccountCode,
              `حساب العميل: ${client_record.name}`,
              'أصول',
              'مدين',
              5, // مستوى فرعي
              parentAccount.id
            ]);

            // ربط العميل بالحساب
            await client.query(`
              UPDATE clients 
              SET account_id = $1, current_balance = 0 
              WHERE id = $2
            `, [result.rows[0].id, client_record.id]);

            clientAccountsCreated++;
            console.log(`✅ ${subAccountCode} - حساب العميل: ${client_record.name}`);
          } else {
            console.log(`⚠️ ${subAccountCode} - حساب العميل موجود مسبقاً: ${client_record.name}`);
          }
        } catch (error) {
          console.error(`❌ خطأ في إنشاء حساب العميل ${client_record.name}:`, error.message);
        }
      }
      console.log(`✅ تم إنشاء ${clientAccountsCreated} حساب للعملاء\n`);
    } else {
      console.log('❌ لم يتم العثور على حساب العملاء الرئيسي\n');
    }

    // 2. إنشاء حسابات فرعية للموظفين
    console.log('👨‍💼 إنشاء حسابات الموظفين...');
    
    // البحث عن حساب الموظفين الرئيسي
    const employeesMainAccount = await client.query(`
      SELECT coa.id, coa.account_code, coa.account_name 
      FROM main_accounts ma
      JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      WHERE ma.account_name = 'حسابات الموظفين'
    `);

    if (employeesMainAccount.rows.length > 0) {
      const parentAccount = employeesMainAccount.rows[0];
      console.log(`✅ تم العثور على حساب الموظفين الرئيسي: ${parentAccount.account_code} - ${parentAccount.account_name}`);

      // جلب جميع الموظفين
      const employees = await client.query('SELECT id, name FROM employees WHERE status = \'active\'');
      console.log(`📋 سيتم إنشاء ${employees.rows.length} حساب للموظفين`);

      let employeeAccountsCreated = 0;
      for (const employee of employees.rows) {
        try {
          // إنشاء كود حساب فرعي للموظف
          const subAccountCode = `${parentAccount.account_code}${String(employee.id).padStart(3, '0')}`;
          
          // التحقق من عدم وجود الحساب مسبقاً
          const existingAccount = await client.query(
            'SELECT id FROM chart_of_accounts WHERE account_code = $1',
            [subAccountCode]
          );

          if (existingAccount.rows.length === 0) {
            // إنشاء الحساب الفرعي
            const result = await client.query(`
              INSERT INTO chart_of_accounts (
                account_code, account_name, account_type, account_nature, 
                account_level, parent_id, allow_transactions, is_active, created_date
              ) VALUES ($1, $2, $3, $4, $5, $6, true, true, CURRENT_DATE)
              RETURNING id
            `, [
              subAccountCode,
              `حساب الموظف: ${employee.name}`,
              'مصروفات',
              'مدين',
              5, // مستوى فرعي
              parentAccount.id
            ]);

            // ربط الموظف بالحساب
            await client.query(`
              UPDATE employees 
              SET account_id = $1, current_balance = 0 
              WHERE id = $2
            `, [result.rows[0].id, employee.id]);

            employeeAccountsCreated++;
            console.log(`✅ ${subAccountCode} - حساب الموظف: ${employee.name}`);
          } else {
            console.log(`⚠️ ${subAccountCode} - حساب الموظف موجود مسبقاً: ${employee.name}`);
          }
        } catch (error) {
          console.error(`❌ خطأ في إنشاء حساب الموظف ${employee.name}:`, error.message);
        }
      }
      console.log(`✅ تم إنشاء ${employeeAccountsCreated} حساب للموظفين\n`);
    } else {
      console.log('❌ لم يتم العثور على حساب الموظفين الرئيسي\n');
    }

    // 3. إنشاء حساب الموردين الرئيسي إذا لم يكن موجوداً
    console.log('🏪 التحقق من حساب الموردين...');
    
    // البحث عن حساب الموردين في دليل الحسابات
    const suppliersAccount = await client.query(`
      SELECT id FROM chart_of_accounts WHERE account_code = '2111'
    `);

    if (suppliersAccount.rows.length > 0) {
      // إضافة حساب الموردين في الحسابات الرئيسية
      const existingMainAccount = await client.query(`
        SELECT id FROM main_accounts WHERE account_name = 'حسابات الموردين'
      `);

      if (existingMainAccount.rows.length === 0) {
        await client.query(`
          INSERT INTO main_accounts (
            account_name, description, chart_account_id, is_required, created_date
          ) VALUES ('حسابات الموردين', 'حسابات الموردين والدائنين', $1, true, CURRENT_DATE)
        `, [suppliersAccount.rows[0].id]);
        
        console.log('✅ تم إضافة حساب الموردين في الحسابات الرئيسية');
      }
    }

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم إنشاء الحسابات الفرعية بنجاح!');
    console.log('================================');
    
    // إحصائيات نهائية
    const totalSubAccounts = await client.query(`
      SELECT COUNT(*) as count 
      FROM chart_of_accounts 
      WHERE account_level = 5 AND is_active = true
    `);
    
    console.log(`📊 إجمالي الحسابات الفرعية: ${totalSubAccounts.rows[0].count}`);

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إنشاء الحسابات الفرعية:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

createSubAccounts().catch(console.error);
