{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/preferences.ts"], "sourcesContent": ["import { useState } from 'react'\nimport {\n  STORAGE_KEY_POSITION,\n  STORAGE_KEY_SCALE,\n  STORAGE_KEY_THEME,\n} from '../../../../../shared'\n\nconst INDICATOR_POSITION =\n  (process.env\n    .__NEXT_DEV_INDICATOR_POSITION as typeof window.__NEXT_DEV_INDICATOR_POSITION) ||\n  'bottom-left'\n\nexport type DevToolsIndicatorPosition = typeof INDICATOR_POSITION\n\nexport function getInitialPosition() {\n  if (\n    typeof localStorage !== 'undefined' &&\n    localStorage.getItem(STORAGE_KEY_POSITION)\n  ) {\n    return localStorage.getItem(\n      STORAGE_KEY_POSITION\n    ) as DevToolsIndicatorPosition\n  }\n  return INDICATOR_POSITION\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nconst BASE_SIZE = 16\n\nexport const NEXT_DEV_TOOLS_SCALE = {\n  Small: BASE_SIZE / 14,\n  Medium: BASE_SIZE / 16,\n  Large: BASE_SIZE / 18,\n} as const\n\nexport type DevToolsScale =\n  (typeof NEXT_DEV_TOOLS_SCALE)[keyof typeof NEXT_DEV_TOOLS_SCALE]\n\nfunction getInitialScale() {\n  if (\n    typeof localStorage !== 'undefined' &&\n    localStorage.getItem(STORAGE_KEY_SCALE)\n  ) {\n    return Number(localStorage.getItem(STORAGE_KEY_SCALE)) as DevToolsScale\n  }\n  return NEXT_DEV_TOOLS_SCALE.Medium\n}\n\nexport function useDevToolsScale(): [\n  DevToolsScale,\n  (value: DevToolsScale) => void,\n] {\n  const [scale, setScale] = useState<DevToolsScale>(getInitialScale())\n\n  function set(value: DevToolsScale) {\n    setScale(value)\n    localStorage.setItem(STORAGE_KEY_SCALE, String(value))\n  }\n\n  return [scale, set]\n}\n\n//////////////////////////////////////////////////////////////////////////////////////\n\nexport function getInitialTheme() {\n  if (typeof localStorage === 'undefined') {\n    return 'system'\n  }\n  const theme = localStorage.getItem(STORAGE_KEY_THEME)\n  return theme === 'dark' || theme === 'light' ? theme : 'system'\n}\n"], "names": ["useState", "STORAGE_KEY_POSITION", "STORAGE_KEY_SCALE", "STORAGE_KEY_THEME", "INDICATOR_POSITION", "process", "env", "__NEXT_DEV_INDICATOR_POSITION", "getInitialPosition", "localStorage", "getItem", "BASE_SIZE", "NEXT_DEV_TOOLS_SCALE", "Small", "Medium", "Large", "getInitialScale", "Number", "useDevToolsScale", "scale", "setScale", "set", "value", "setItem", "String", "getInitialTheme", "theme"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,QAAO;AAChC,SACEC,oBAAoB,EACpBC,iBAAiB,EACjBC,iBAAiB,QACZ,wBAAuB;AAE9B,MAAMC,qBACJ,AAACC,QAAQC,GAAG,CACTC,6BAA6B,IAChC;AAIF,OAAO,SAASC;IACd,IACE,OAAOC,iBAAiB,eACxBA,aAAaC,OAAO,CAACT,uBACrB;QACA,OAAOQ,aAAaC,OAAO,CACzBT;IAEJ;IACA,OAAOG;AACT;AAEA,sFAAsF;AAEtF,MAAMO,YAAY;AAElB,OAAO,MAAMC,uBAAuB;IAClCC,OAAOF,YAAY;IACnBG,QAAQH,YAAY;IACpBI,OAAOJ,YAAY;AACrB,EAAU;AAKV,SAASK;IACP,IACE,OAAOP,iBAAiB,eACxBA,aAAaC,OAAO,CAACR,oBACrB;QACA,OAAOe,OAAOR,aAAaC,OAAO,CAACR;IACrC;IACA,OAAOU,qBAAqBE,MAAM;AACpC;AAEA,OAAO,SAASI;IAId,MAAM,CAACC,OAAOC,SAAS,GAAGpB,SAAwBgB;IAElD,SAASK,IAAIC,KAAoB;QAC/BF,SAASE;QACTb,aAAac,OAAO,CAACrB,mBAAmBsB,OAAOF;IACjD;IAEA,OAAO;QAACH;QAAOE;KAAI;AACrB;AAEA,sFAAsF;AAEtF,OAAO,SAASI;IACd,IAAI,OAAOhB,iBAAiB,aAAa;QACvC,OAAO;IACT;IACA,MAAMiB,QAAQjB,aAAaC,OAAO,CAACP;IACnC,OAAOuB,UAAU,UAAUA,UAAU,UAAUA,QAAQ;AACzD"}