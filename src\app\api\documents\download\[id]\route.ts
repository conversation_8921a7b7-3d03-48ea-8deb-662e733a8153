import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'
import { readFile } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const documentId = parseInt(params.id)

    if (isNaN(documentId)) {
      return NextResponse.json(
        { success: false, error: 'معرف الوثيقة غير صحيح' },
        { status: 400 }
      )
    }

    // جلب معلومات الوثيقة
    const documentQuery = `
      SELECT * FROM documents 
      WHERE id = $1 AND is_active = true
    `

    const result = await query(documentQuery, [documentId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الوثيقة غير موجودة' },
        { status: 404 }
      )
    }

    const document = result.rows[0]

    // التحقق من وجود الملف
    const filePath = join(process.cwd(), 'public', document.file_path)
    
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { success: false, error: 'الملف غير موجود على الخادم' },
        { status: 404 }
      )
    }

    // قراءة الملف
    const fileBuffer = await readFile(filePath)

    // تسجيل عملية التحميل (اختياري)
    await query(`
      INSERT INTO document_access_log (document_id, access_type, access_date, ip_address)
      VALUES ($1, 'download', CURRENT_TIMESTAMP, $2)
    `, [documentId, request.ip || 'unknown'])

    // إرجاع الملف
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': document.mime_type || 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(document.file_name)}"`,
        'Content-Length': document.file_size.toString(),
      },
    })

  } catch (error) {
    console.error('خطأ في تحميل الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحميل الوثيقة' },
      { status: 500 }
    )
  }
}