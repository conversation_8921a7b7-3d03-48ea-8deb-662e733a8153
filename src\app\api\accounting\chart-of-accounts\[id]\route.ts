import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب حساب واحد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: accountId } = await params

    const result = await query(`
      SELECT 
        id,
        account_code,
        account_name,
        account_name_en,
        level_1_code,
        level_2_code,
        level_3_code,
        level_4_code,
        account_level,
        parent_id,
        account_type,
        account_nature,
        is_active,
        allow_transactions,
        linked_table,
        auto_create_sub_accounts,
        opening_balance,
        current_balance,
        description,
        created_date
      FROM chart_of_accounts
      WHERE id = $1
    `, [accountId])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      account: result.rows[0],
      message: 'تم جلب الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث حساب
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json()
    const { id: accountId } = await params

    console.log('تحديث الحساب:', { accountId, body })

    const {
      account_name,
      account_name_en,
      account_type,
      account_nature,
      linked_table,
      auto_create_sub_accounts,
      description,
      is_active = true,
      allow_transactions,
      notes
    } = body

    // التحقق من صحة البيانات
    if (!account_name || !account_type) {
      return NextResponse.json({
        success: false,
        error: 'اسم الحساب ونوع الحساب مطلوبان'
      }, { status: 400 })
    }

    // منع تفعيل المعاملات على حسابات التحكم
    if (allow_transactions && linked_table && linked_table !== 'none') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن تفعيل المعاملات على حسابات التحكم المرتبطة بجداول خارجية'
      }, { status: 400 })
    }

    // التحقق من وجود الحساب أولاً
    const existingAccount = await query(
      'SELECT id, account_level FROM chart_of_accounts WHERE id = $1',
      [accountId]
    )

    if (existingAccount.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    const result = await query(`
      UPDATE chart_of_accounts
      SET
        account_name = $2,
        account_name_en = $3,
        account_type = $4,
        account_nature = $5,
        linked_table = $6,
        auto_create_sub_accounts = $7,
        description = $8,
        is_active = $9,
        allow_transactions = $10,
        notes = $11,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [
      accountId,
      account_name,
      account_name_en || null,
      account_type,
      account_nature || 'مدين',
      linked_table || null,
      auto_create_sub_accounts || false,
      description || null,
      is_active !== undefined ? is_active : true,
      allow_transactions !== undefined ? allow_transactions : false,
      notes || null
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'فشل في تحديث الحساب'
      }, { status: 500 })
    }

    console.log('تم تحديث الحساب بنجاح:', result.rows[0])

    return NextResponse.json({
      success: true,
      account: result.rows[0],
      message: 'تم تحديث الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف حساب
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: accountId } = await params

    // التحقق من وجود حسابات فرعية
    const childrenResult = await query(
      'SELECT COUNT(*) as count FROM chart_of_accounts WHERE parent_id = $1',
      [accountId]
    )

    if (parseInt(childrenResult.rows[0].count) > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف الحساب لوجود حسابات فرعية'
      }, { status: 400 })
    }

    // التحقق من وجود معاملات
    const transactionsCheck = await query(`
      SELECT
        (SELECT COUNT(*) FROM payment_vouchers WHERE debit_account_id = $1 OR credit_account_id = $1) +
        (SELECT COUNT(*) FROM receipt_vouchers WHERE debit_account_id = $1 OR credit_account_id = $1) +
        (SELECT COUNT(*) FROM journal_entry_details WHERE account_id = $1) as total_transactions
    `, [accountId])

    if (parseInt(transactionsCheck.rows[0].total_transactions) > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف الحساب لوجود معاملات مرتبطة به'
      }, { status: 400 })
    }

    const result = await query(
      'DELETE FROM chart_of_accounts WHERE id = $1 RETURNING *',
      [accountId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
