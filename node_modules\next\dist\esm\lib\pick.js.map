{"version": 3, "sources": ["../../src/lib/pick.ts"], "sourcesContent": ["export function pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {\n  const newObj = {} as Pick<T, K>\n  for (const key of keys) {\n    newObj[key] = obj[key]\n  }\n  return newObj\n}\n"], "names": ["pick", "obj", "keys", "newObj", "key"], "mappings": "AAAA,OAAO,SAASA,KAA2BC,GAAM,EAAEC,IAAS;IAC1D,MAAMC,SAAS,CAAC;IAChB,KAAK,MAAMC,OAAOF,KAAM;QACtBC,MAAM,CAACC,IAAI,GAAGH,GAAG,CAACG,IAAI;IACxB;IACA,OAAOD;AACT"}