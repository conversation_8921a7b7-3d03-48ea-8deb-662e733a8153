'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { IssueSelect } from '@/components/ui/issue-select'
import { LineageSelect } from '@/components/ui/lineage-select'
import { EmployeeSelect } from '@/components/ui/employee-select'
import { ServiceSelect } from '@/components/ui/service-select'
import {
  Share2,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  X,
  Save,
  Calculator,
  DollarSign,
  Users,
  FileText
} from 'lucide-react'

interface ServiceDistribution {
  service_id: number
  service_name?: string
  percentage?: number
  amount?: number
  lawyer_id: number
  lawyer_name?: string
}

interface CaseDistribution {
  id: number
  issue_id: number
  issue_title: string
  case_number: string
  case_amount?: number
  lineage_id?: number
  lineage_name?: string
  admin_percentage?: number
  commission_percentage?: number
  admin_amount?: number
  commission_amount?: number
  remaining_amount?: number
  service_distributions?: ServiceDistribution[]
  created_date: string
}

export default function CaseDistributionPage() {
  const [distributions, setDistributions] = useState<CaseDistribution[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [selectedDistribution, setSelectedDistribution] = useState<CaseDistribution | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [formData, setFormData] = useState({
    issue_id: '',
    issue_data: null as any,
    lineage_id: '',
    lineage_data: null as any,
    admin_amount: 0,
    remaining_amount: 0,
    service_distributions: [] as ServiceDistribution[]
  })

  const fetchDistributions = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/case-distribution')
      const result = await response.json()

      if (result.success) {
        setDistributions(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات توزيع القضايا')
        setDistributions([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setDistributions([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchDistributions()
  }, [])

  // إعادة جلب التوزيعات عند إغلاق النافذة
  useEffect(() => {
    if (!isModalOpen) {
      fetchDistributions()
    }
  }, [isModalOpen])

  const handleAddNew = () => {
    console.log('CaseDistribution: handleAddNew called, setting modalType to add')
    setSelectedDistribution(null)
    setFormData({
      issue_id: '',
      issue_data: null,
      lineage_id: '',
      lineage_data: null,
      admin_amount: 0,
      remaining_amount: 0,
      service_distributions: []
    })
    setDbError(null)
    setModalType('add')
    setIsModalOpen(true)
    
    // إعادة جلب التوزيعات للتأكد من أحدث البيانات
    fetchDistributions()
  }

  const handleIssueChange = (issueId: string, issueData: any) => {
    setFormData({
      ...formData,
      issue_id: issueId,
      issue_data: issueData
    })
  }

  const handleLineageChange = (lineageId: string, lineageData: any) => {
    if (lineageData && formData.issue_data) {
      // حساب نسبة الإدارة ومبلغها
      const adminAmount = (formData.issue_data.amount * lineageData.admin_percentage) / 100
      const commissionAmount = (formData.issue_data.amount * (lineageData.commission_percentage || 0)) / 100
      const remainingAmount = formData.issue_data.amount - adminAmount - commissionAmount

      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        admin_amount: adminAmount,
        remaining_amount: remainingAmount,
        service_distributions: []
      })
    } else {
      setFormData({
        ...formData,
        lineage_id: lineageId,
        lineage_data: lineageData,
        admin_amount: 0,
        remaining_amount: 0,
        service_distributions: []
      })
    }
  }

  const handleLawyerChange = (serviceId: number, lawyerId: string, lawyerData: any) => {
    const updatedDistributions = formData.service_distributions.map(dist =>
      dist.service_id === serviceId
        ? { ...dist, lawyer_id: Number(lawyerId), lawyer_name: lawyerData ? lawyerData.name : '' }
        : dist
    )

    setFormData({
      ...formData,
      service_distributions: updatedDistributions
    })
  }

  const addNewService = () => {
    if (!formData.issue_data || !formData.lineage_data) return

    const newServiceId = Math.max(0, ...formData.service_distributions.map(s => s.service_id)) + 1
    const newService: ServiceDistribution = {
      service_id: newServiceId,
      service_name: '',
      percentage: 0,
      amount: 0,
      lawyer_id: 0,
      lawyer_name: ''
    }

    setFormData({
      ...formData,
      service_distributions: [...formData.service_distributions, newService]
    })
  }

  const removeService = (serviceId: number) => {
    const newDistributions = formData.service_distributions.filter(dist => dist.service_id !== serviceId)
    setFormData({...formData, service_distributions: newDistributions})
  }

  const updateServiceName = (serviceId: number, serviceName: string) => {
    const newDistributions = formData.service_distributions.map(dist =>
      dist.service_id === serviceId
        ? { ...dist, service_name: serviceName }
        : dist
    )
    setFormData({...formData, service_distributions: newDistributions})
  }

  // دالة لإعادة حساب مبالغ جميع الخدمات
  const recalculateServiceAmounts = (distributions: ServiceDistribution[]) => {
    if (!formData.issue_data || !formData.lineage_data) return distributions

    const availableAmount = formData.remaining_amount || 0
    
    return distributions.map(dist => ({
      ...dist,
      amount: Math.floor((availableAmount * (dist.percentage || 0)) / 100)
    }))
  }

  // دوال الإجراءات
  const handleViewDistribution = (distribution: CaseDistribution) => {
    setSelectedDistribution(distribution)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleEditDistribution = (distribution: CaseDistribution) => {
    // تحميل بيانات التوزيع في النموذج
    setFormData({
      issue_id: distribution.issue_id.toString(),
      issue_data: {
        id: distribution.issue_id,
        case_number: distribution.case_number,
        title: distribution.issue_title,
        amount: distribution.case_amount || 0
      },
      lineage_id: distribution.lineage_id?.toString() || '',
      lineage_data: {
        id: distribution.lineage_id || 0,
        name: distribution.lineage_name || 'غير محدد',
        admin_percentage: distribution.admin_percentage || 0,
        commission_percentage: distribution.commission_percentage || 0
      },
      admin_amount: distribution.admin_amount || 0,
      remaining_amount: distribution.remaining_amount || 0,
      service_distributions: distribution.service_distributions || []
    })

    setSelectedDistribution(distribution)
    setDbError(null)
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleDeleteDistribution = async (distribution: CaseDistribution) => {
    if (confirm(`هل أنت متأكد من حذف توزيع القضية "${distribution.case_number}"؟\nسيتم حذف جميع توزيعات الخدمات المرتبطة بها.`)) {
      try {
        const response = await fetch(`/api/case-distribution?id=${distribution.id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          await fetchDistributions()
          alert('تم حذف توزيع القضية بنجاح')
        } else {
          setDbError(result.error)
        }
      } catch (error) {
        console.error('Error deleting distribution:', error)
        setDbError('فشل في حذف التوزيع')
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.issue_data || !formData.lineage_data) {
      setDbError('يرجى اختيار القضية والنسب المالية')
      return
    }

    if (formData.service_distributions.length === 0) {
      setDbError('يرجى إضافة خدمة واحدة على الأقل')
      return
    }

    // التحقق من صحة بيانات الخدمات
    const invalidServices = formData.service_distributions.filter(service => 
      !service.service_name || !service.lawyer_id || service.percentage <= 0
    )
    
    if (invalidServices.length > 0) {
      setDbError('يرجى إكمال جميع بيانات الخدمات (الخدمة، المحامي، النسبة)')
      return
    }

    // التحقق من مجموع النسب
    const totalPercentage = formData.service_distributions.reduce((sum, service) => sum + (service.percentage || 0), 0)
    if (totalPercentage > 100) {
      setDbError(`مجموع نسب الخدمات لا يجب أن يزيد عن 100%. المجموع الحالي: ${totalPercentage}%`)
      return
    }

    try {
      const url = '/api/case-distribution'
      const method = modalType === 'edit' && selectedDistribution ? 'PUT' : 'POST'
      const body = {
        ...(modalType === 'edit' && selectedDistribution ? { id: selectedDistribution.id } : {}),
        issue_id: formData.issue_data.id,
        lineage_id: formData.lineage_data.id,
        admin_amount: formData.admin_amount,
        remaining_amount: formData.remaining_amount,
        service_distributions: formData.service_distributions
      }

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      const result = await response.json()

      if (result.success) {
        // إعادة تعيين النموذج
        setFormData({
          issue_id: '',
          issue_data: null,
          lineage_id: '',
          lineage_data: null,
          admin_amount: 0,
          remaining_amount: 0,
          service_distributions: []
        })

        // إغلاق النافذة المنبثقة وتحديث القائمة
        setIsModalOpen(false)
        setSelectedDistribution(null)
        
        // تحديث قائمة التوزيعات
        await fetchDistributions()

        setDbError(null)
        alert(modalType === 'edit' ? 'تم تحديث توزيع القضية بنجاح' : 'تم إضافة توزيع القضية بنجاح')
      } else {
        setDbError(result.error)
      }
    } catch (error) {
      console.error('Error submitting distribution:', error)
      setDbError('فشل في حفظ التوزيع')
    }
  }

  const stats = {
    total: distributions.length,
    totalAmount: distributions.reduce((sum, d) => sum + (d.case_amount || 0), 0),
    totalAdminAmount: distributions.reduce((sum, d) => sum + (d.admin_amount || 0), 0)
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Share2 className="h-8 w-8 mr-3 text-green-600" />
              توزيع القضايا
            </h1>
            <p className="text-gray-600 mt-1">إدارة وتوزيع مبالغ القضايا على الخدمات والمحامين</p>
          </div>

          <Button onClick={handleAddNew} className="bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة توزيع جديد
          </Button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي التوزيعات</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">إجمالي المبالغ</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAmount.toLocaleString()} ريال</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calculator className="h-8 w-8 text-purple-600" />
                <div className="mr-4">
                  <p className="text-sm font-medium text-gray-600">مبالغ الإدارة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalAdminAmount.toLocaleString()} ريال</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* قائمة التوزيعات الموجودة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              التوزيعات الموجودة ({distributions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل التوزيعات...</p>
              </div>
            ) : dbError ? (
              <div className="text-center py-8 text-red-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-red-300" />
                <p className="font-semibold">خطأ في تحميل البيانات</p>
                <p className="text-sm">{dbError}</p>
                <Button
                  onClick={fetchDistributions}
                  className="mt-4"
                  variant="outline"
                >
                  إعادة المحاولة
                </Button>
              </div>
            ) : distributions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Share2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد توزيعات مضافة بعد</p>
                <p className="text-sm">انقر على "إضافة توزيع جديد" لبدء إضافة التوزيعات</p>
              </div>
            ) : (
              <div className="space-y-4">
                {distributions.map((distribution) => (
                  <Card key={distribution.id} className="border border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {distribution.case_number}
                          </Badge>
                          <h3 className="font-semibold text-gray-900">{distribution.issue_title}</h3>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-blue-600 hover:text-blue-700"
                            onClick={() => handleViewDistribution(distribution)}
                            title="عرض التفاصيل"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-green-600 hover:text-green-700"
                            onClick={() => handleEditDistribution(distribution)}
                            title="تعديل التوزيع"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleDeleteDistribution(distribution)}
                            title="حذف التوزيع"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-3">
                        <div className="text-sm">
                          <span className="text-gray-600">المبلغ الإجمالي:</span>
                          <div className="font-semibold text-green-600">{(distribution.case_amount || 0).toLocaleString()} ريال</div>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">مبلغ الإدارة:</span>
                          <div className="font-semibold text-purple-600">{(distribution.admin_amount || 0).toLocaleString()} ريال</div>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">المبلغ المتبقي:</span>
                          <div className="font-semibold text-orange-600">{(distribution.remaining_amount || 0).toLocaleString()} ريال</div>
                        </div>
                      </div>

                      <div className="text-sm mb-3">
                        <span className="text-gray-600">النسب المالية:</span>
                        <span className="font-medium mr-2">{distribution.lineage_name}</span>
                        <span className="text-gray-500">
                          (إدارة: {distribution.admin_percentage}% - عمولة: {distribution.commission_percentage}%)
                        </span>
                      </div>

                      {/* تفاصيل توزيع الخدمات */}
                      {distribution.service_distributions && distribution.service_distributions.length > 0 && (
                        <div className="mt-4 border-t pt-3">
                          <h4 className="text-sm font-semibold text-gray-700 mb-2">توزيع الخدمات:</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {distribution.service_distributions.map((service, index) => (
                              <div key={index} className="bg-gray-50 p-3 rounded border">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium text-sm text-gray-800">{service.service_name}</span>
                                  <Badge variant="outline" className="text-xs">{(service.percentage || 0)}%</Badge>
                                </div>
                                <div className="text-xs text-gray-600 mb-1">
                                  المحامي: {service.lawyer_name || `محامي ${service.lawyer_id}`}
                                </div>
                                <div className="text-sm font-semibold text-green-600">
                                  {(service.amount || 0).toLocaleString()} ريال
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* النموذج المحسن */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg w-full max-w-7xl h-[95vh] flex flex-col relative">
              {/* رأس النافذة */}
              <div className="flex items-center justify-between p-4 border-b bg-gray-50">
                <h2 className="text-xl font-bold text-gray-900">
                  {modalType === 'add' && '📋 إضافة توزيع قضية جديد'}
                  {modalType === 'edit' && '✏️ تعديل توزيع القضية'}
                  {modalType === 'view' && '👁️ عرض تفاصيل التوزيع'}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* المحتوى الرئيسي */}
              <div className="flex-1 overflow-visible p-4">

              {modalType === 'view' && selectedDistribution ? (
                /* عرض تفاصيل التوزيع */
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>معلومات القضية</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div>
                          <span className="text-gray-600">رقم القضية:</span>
                          <div className="font-semibold">{selectedDistribution.case_number}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">عنوان القضية:</span>
                          <div className="font-semibold">{selectedDistribution.issue_title}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">المبلغ الإجمالي:</span>
                          <div className="font-semibold text-green-600">{(selectedDistribution.case_amount || 0).toLocaleString()} ريال</div>
                        </div>
                        <div>
                          <span className="text-gray-600">تاريخ الإنشاء:</span>
                          <div className="font-semibold">{new Date(selectedDistribution.created_date).toLocaleDateString('ar-SA')}</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>التوزيع المالي</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                          <div className="text-2xl font-bold text-purple-600">{(selectedDistribution.admin_amount || 0).toLocaleString()}</div>
                          <div className="text-sm text-purple-600">مبلغ الإدارة</div>
                        </div>
                        <div className="text-center p-4 bg-orange-50 rounded-lg">
                          <div className="text-2xl font-bold text-orange-600">{(selectedDistribution.remaining_amount || 0).toLocaleString()}</div>
                          <div className="text-sm text-orange-600">المبلغ المتبقي</div>
                        </div>
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{(selectedDistribution.service_distributions || []).length}</div>
                          <div className="text-sm text-blue-600">عدد الخدمات</div>
                        </div>
                      </div>

                      {selectedDistribution.service_distributions && selectedDistribution.service_distributions.length > 0 && (
                        <div>
                          <h4 className="font-semibold mb-4">تفاصيل توزيع الخدمات:</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {selectedDistribution.service_distributions.map((service, index) => (
                              <div key={index} className="border rounded-lg p-4 bg-gray-50">
                                <div className="flex items-center justify-between mb-2">
                                  <h5 className="font-medium">{service.service_name}</h5>
                                  <Badge variant="outline">{(service.percentage || 0)}%</Badge>
                                </div>
                                <div className="space-y-1 text-sm">
                                  <div>
                                    <span className="text-gray-600">المحامي:</span>
                                    <span className="mr-2">{service.lawyer_name || `محامي ${service.lawyer_id}`}</span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">المبلغ:</span>
                                    <span className="mr-2 font-semibold text-green-600">{(service.amount || 0).toLocaleString()} ريال</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ) : (
                /* نموذج الإضافة/التعديل */
                <form onSubmit={handleSubmit} className="space-y-2">
                {/* القسم الأول: اختيار القضية والنسب المالية */}
                <Card>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-2 gap-6 mb-6">
                      {/* اختيار القضية */}
                      <div>
                        <IssueSelect
                          key={`issue-select-${modalType}-${isModalOpen}`}
                          value={formData.issue_id}
                          onChange={handleIssueChange}
                          label={modalType === 'add' ? "القضية (القضايا غير الموزعة فقط)" : "القضية"}
                          placeholder="اختر القضية"
                          required
                          excludeDistributed={modalType === 'add'}
                        />
                        {modalType === 'add' && (
                          <div className="text-xs text-blue-600 mt-1">
                            💡 تظهر فقط القضايا التي لم يتم توزيعها مسبقاً
                          </div>
                        )}
                      </div>

                      {/* النسب المالية */}
                      <div>
                        <LineageSelect
                          value={formData.lineage_id}
                          onChange={handleLineageChange}
                          label="النسب المالية"
                          placeholder="اختر النسب"
                          required
                        />
                      </div>
                    </div>

                    {/* عرض تفاصيل النسب والحسابات - فقط إذا كان التعاقد بالعقد */}
                    {formData.lineage_data && formData.issue_data && formData.issue_data.contract_method === 'بالعقد' && (
                      <div className="bg-gray-50 p-4 rounded-lg border">
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          {/* نسبة الإدارة */}
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Label className="text-purple-800 font-bold text-sm">نسبة الإدارة</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              step="0.1"
                              value={formData.lineage_data.admin_percentage || 0}
                              onChange={(e) => {
                                const newAdminPercentage = Number(e.target.value)
                                const newData = {...formData.lineage_data, admin_percentage: newAdminPercentage}
                                const adminAmount = (formData.issue_data.amount * newAdminPercentage) / 100
                                const commissionAmount = (formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100
                                const remainingAmount = formData.issue_data.amount - adminAmount - commissionAmount

                                setFormData({
                                  ...formData,
                                  lineage_data: newData,
                                  admin_amount: adminAmount,
                                  remaining_amount: remainingAmount
                                })
                              }}
                              className="w-16 text-center text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                            <span className="text-purple-600 font-bold">%</span>
                            <span className="text-sm text-gray-600">
                              {((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100).toLocaleString()} ريال
                            </span>
                          </div>

                          {/* نسبة العمولة */}
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Label className="text-green-800 font-bold text-sm">العمولة</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              step="0.1"
                              value={formData.lineage_data.commission_percentage || 0}
                              onChange={(e) => {
                                const newCommissionPercentage = Number(e.target.value)
                                const newData = {...formData.lineage_data, commission_percentage: newCommissionPercentage}
                                const adminAmount = (formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100
                                const commissionAmount = (formData.issue_data.amount * newCommissionPercentage) / 100
                                const remainingAmount = formData.issue_data.amount - adminAmount - commissionAmount

                                setFormData({
                                  ...formData,
                                  lineage_data: newData,
                                  admin_amount: adminAmount,
                                  remaining_amount: remainingAmount
                                })
                              }}
                              className="w-16 text-center text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            />
                            <span className="text-green-600 font-bold">%</span>
                            <span className="text-sm text-gray-600">
                              {((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100).toLocaleString()} ريال
                            </span>
                          </div>
                        </div>

                        {/* ملخص النسب - سطر واحد فقط */}
                        <div className="p-2 bg-blue-50 border border-blue-200 rounded text-center">
                          <div className="font-bold text-blue-800 text-sm">
                            = إجمالي النسبة {(Number(formData.lineage_data.admin_percentage || 0) + Number(formData.lineage_data.commission_percentage || 0)).toFixed(1)}%
                            مبلغ: {(((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) + ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)).toLocaleString()} ريال
                          </div>
                        </div>
                      </div>
                    )}

                    {/* رسالة توضيحية للتعاقد بالجلسة */}
                    {formData.lineage_data && formData.issue_data && formData.issue_data.contract_method === 'بالجلسة' && (
                      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                        <div className="text-center text-yellow-800">
                          <div className="text-2xl mb-2">⚖️</div>
                          <h3 className="font-bold mb-2">قضية بالجلسة</h3>
                          <p className="text-sm">
                            هذه القضية تعاقد بالجلسة، لذلك لا يتم تطبيق نسب الإدارة والعمولة.
                            <br />
                            يمكنك توزيع الخدمات مباشرة على المحامين.
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* رسالة توضيحية عند عدم اختيار النسب */}
                {(!formData.lineage_data || !formData.issue_data) && (
                  <Card>
                    <CardContent className="p-12">
                      <div className="text-center text-gray-500">
                        <div className="text-4xl mb-4">📊</div>
                        <h3 className="text-lg font-medium mb-2">اختر القضية والنسب المالية</h3>
                        <p className="text-sm">
                          {!formData.issue_data && !formData.lineage_data
                            ? "يرجى اختيار القضية ومجموعة النسب المالية لعرض تفاصيل التوزيع"
                            : !formData.issue_data
                            ? "يرجى اختيار القضية أولاً"
                            : "يرجى اختيار مجموعة النسب المالية لعرض تفاصيل التوزيع"
                          }
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* توزيع الخدمات على المحامين */}
                {formData.lineage_data && formData.issue_data && (
                  <Card className="overflow-visible">
                    <CardContent className="p-4 overflow-visible">
                      {/* عرض المبلغ المتاح حسب نوع التعاقد */}
                      {formData.issue_data.contract_method === 'بالعقد' ? (
                        <div className="mb-1 p-1 bg-green-50 border border-green-200 rounded text-xs text-gray-600">
                          النسبة القابلة للتوزيع: <strong className="text-green-700">{(100 - Number(formData.lineage_data.admin_percentage || 0) - Number(formData.lineage_data.commission_percentage || 0)).toFixed(1)}%</strong>
                          - المبلغ المتاح: <strong className="text-green-700">{Math.floor(formData.issue_data.amount - ((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) - ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100)).toLocaleString()} ريال</strong>
                        </div>
                      ) : (
                        <div className="mb-1 p-1 bg-blue-50 border border-blue-200 rounded text-xs text-gray-600">
                          قضية بالجلسة - المبلغ الكامل متاح للتوزيع: <strong className="text-blue-700">{formData.issue_data.amount.toLocaleString()} ريال</strong>
                        </div>
                      )}
                      {formData.service_distributions.length > 0 ? (
                        <div className="overflow-visible">
                          <table className="w-full border-collapse border-2 border-gray-300 rounded-lg">
                            <thead>
                              <tr className="bg-gradient-to-r from-green-100 to-blue-100">
                                <th className="border border-gray-300 p-1 text-right font-bold text-gray-700 bg-gray-50 text-xs w-48">الخدمة</th>
                                {formData.issue_data.contract_method === 'بالعقد' && (
                                  <th className="border border-gray-300 p-1 text-center font-bold text-gray-700 bg-blue-50 text-xs w-20">النسبة (%)</th>
                                )}
                                <th className="border border-gray-300 p-1 text-center font-bold text-gray-700 bg-green-50 text-xs w-24">المبلغ (ريال)</th>
                                <th className="border border-gray-300 p-1 text-right font-bold text-gray-700 bg-purple-50 text-xs">المحامي المكلف</th>
                                <th className="border border-gray-300 p-1 text-center font-bold text-gray-700 bg-red-50 text-xs w-12">حذف</th>
                              </tr>
                            </thead>
                            <tbody>
                              {formData.service_distributions.map((service, index) => (
                                <tr key={service.service_id} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                                  {/* الخدمة */}
                                  <td className="border border-gray-300 p-1 w-48">
                                    <ServiceSelect
                                      value={service.service_name}
                                      onChange={(serviceName) => updateServiceName(service.service_id, serviceName)}
                                      placeholder="اختر الخدمة"
                                    />
                                  </td>

                                  {/* النسبة - قابلة للتحرير - فقط للتعاقد بالعقد */}
                                  {formData.issue_data.contract_method === 'بالعقد' && (
                                    <td className="border border-gray-300 p-1 text-center">
                                      <Input
                                        type="number"
                                        min="0"
                                        max="100"
                                        step="0.1"
                                        value={service.percentage}
                                        onChange={(e) => {
                                          const newDistributions = [...formData.service_distributions]
                                          newDistributions[index].percentage = Number(e.target.value)
                                          // حساب المبلغ بناءً على النسبة
                                          if (formData.issue_data && formData.lineage_data) {
                                            let availableAmount;
                                            if (formData.issue_data.contract_method === 'بالجلسة') {
                                              // للتعاقد بالجلسة: المبلغ الكامل متاح
                                              availableAmount = formData.issue_data.amount;
                                            } else {
                                              // للتعاقد بالعقد: المبلغ بعد خصم نسب الإدارة والعمولة
                                              availableAmount = Math.floor(formData.issue_data.amount -
                                                ((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) -
                                                ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100));
                                            }
                                            newDistributions[index].amount = Math.floor((availableAmount * Number(e.target.value)) / 100)
                                          }
                                          setFormData({...formData, service_distributions: newDistributions})
                                        }}
                                        className="text-center font-bold text-blue-600 bg-blue-50 border border-blue-200 text-xs h-6 w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                      />
                                    </td>
                                  )}

                                  {/* المبلغ - قابل للتحرير */}
                                  <td className="border border-gray-300 p-1 text-center">
                                    <Input
                                      type="number"
                                      min="0"
                                      step="1"
                                      value={Math.floor(service.amount)}
                                      onChange={(e) => {
                                        const value = e.target.value
                                        if (value === '' || /^\d+$/.test(value)) {
                                          const newDistributions = [...formData.service_distributions]
                                          newDistributions[index].amount = Number(value) || 0
                                          // حساب النسبة بناءً على المبلغ (فقط للتعاقد بالعقد)
                                          if (formData.issue_data && formData.lineage_data && formData.issue_data.contract_method === 'بالعقد') {
                                            let availableAmount;
                                            if (formData.issue_data.contract_method === 'بالجلسة') {
                                              // للتعاقد بالجلسة: المبلغ الكامل متاح
                                              availableAmount = formData.issue_data.amount;
                                            } else {
                                              // للتعاقد بالعقد: المبلغ بعد خصم نسب الإدارة والعمولة
                                              availableAmount = Math.floor(formData.issue_data.amount -
                                                ((formData.issue_data.amount * (formData.lineage_data.admin_percentage || 0)) / 100) -
                                                ((formData.issue_data.amount * (formData.lineage_data.commission_percentage || 0)) / 100));
                                            }
                                            newDistributions[index].percentage = Math.round((Number(value) / availableAmount) * 100 * 10) / 10
                                          }
                                          setFormData({...formData, service_distributions: newDistributions})
                                        }
                                      }}
                                      className="text-center font-bold text-green-600 bg-green-50 border border-green-200 text-xs h-6 w-16 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                    />
                                  </td>

                                  {/* المحامي */}
                                  <td className="border border-gray-300 p-1">
                                    <EmployeeSelect
                                      value={service.lawyer_id.toString()}
                                      onChange={(lawyerId, lawyerData) => handleLawyerChange(service.service_id, lawyerId, lawyerData)}
                                      label=""
                                      placeholder="اختر المحامي"
                                      required
                                    />
                                  </td>

                                  {/* حذف */}
                                  <td className="border border-gray-300 p-1 text-center">
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => removeService(service.service_id)}
                                      className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-50"
                                    >
                                      ✕
                                    </Button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <div className="text-lg mb-2">📋</div>
                          <p>لم يتم إضافة خدمات للتوزيع بعد</p>
                          <p className="text-sm">انقر على "إضافة خدمة" لبدء التوزيع</p>
                        </div>
                      )}

                    </CardContent>
                  </Card>
                )}
              </form>
              )}
              </div>

              {/* أزرار التحكم في أسفل النافذة */}
              <div className="border-t bg-gray-50 p-4 flex space-x-3 space-x-reverse">
                {/* زر إضافة خدمة */}
                <Button
                  type="button"
                  onClick={addNewService}
                  className="bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4"
                  disabled={!formData.issue_data || !formData.lineage_data}
                >
                  ➕ إضافة خدمة
                </Button>

                <div className="flex-1"></div>

                {/* أزرار الحفظ والإلغاء */}
                {modalType !== 'view' && (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsModalOpen(false)}
                      className="border-gray-300 text-gray-700 hover:bg-gray-50 font-bold py-2 px-6 text-sm"
                    >
                      <X className="h-4 w-4 mr-2" />
                      إلغاء
                    </Button>
                    <Button
                      onClick={(e) => {
                        e.preventDefault()
                        handleSubmit(e as any)
                      }}
                      className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 text-sm"
                      disabled={!formData.issue_data || !formData.lineage_data || formData.service_distributions.length === 0}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'edit' ? 'تحديث التوزيع' : 'حفظ التوزيع'}
                    </Button>
                  </>
                )}

                {modalType === 'view' && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 font-bold py-2 px-6 text-sm"
                  >
                    <X className="h-4 w-4 mr-2" />
                    إغلاق
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
