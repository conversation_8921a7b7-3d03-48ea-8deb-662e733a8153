const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function updateManagerCredentials() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 تحديث بيانات تسجيل الدخول للمدير محمد الحاشدي...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. التحقق من وجود المستخدم الحالي
    console.log('🔍 البحث عن المستخدم الحالي...');
    
    const currentUser = await client.query(`
      SELECT id, username, email, employee_id 
      FROM users 
      WHERE username = 'mohamed.alhashedi' OR email = '<EMAIL>'
    `);

    if (currentUser.rows.length === 0) {
      throw new Error('المستخدم محمد الحاشدي غير موجود');
    }

    const user = currentUser.rows[0];
    console.log('✅ تم العثور على المستخدم:');
    console.log(`   🆔 معرف المستخدم: ${user.id}`);
    console.log(`   👤 اسم المستخدم الحالي: ${user.username}`);
    console.log(`   📧 البريد الحالي: ${user.email}`);
    console.log(`   🔗 مرتبط بالموظف: ${user.employee_id}`);

    // 2. التحقق من عدم وجود مستخدم آخر باسم "admin"
    console.log('\n🔍 التحقق من توفر اسم المستخدم الجديد...');
    
    const existingAdmin = await client.query(`
      SELECT id, username FROM users WHERE username = 'admin' AND id != $1
    `, [user.id]);

    if (existingAdmin.rows.length > 0) {
      console.log('⚠️  يوجد مستخدم آخر باسم "admin"، سيتم تحديث اسم المستخدم إلى "admin2"');
      var newUsername = 'admin2';
    } else {
      var newUsername = 'admin';
      console.log('✅ اسم المستخدم "admin" متاح');
    }

    // 3. تشفير كلمة المرور الجديدة
    console.log('\n🔐 تشفير كلمة المرور الجديدة...');
    const hashedPassword = await bcrypt.hash('ana8080', 10);
    console.log('✅ تم تشفير كلمة المرور بنجاح');

    // 4. تحديث بيانات المستخدم
    console.log('\n📝 تحديث بيانات المستخدم...');
    
    const updateResult = await client.query(`
      UPDATE users 
      SET 
        username = $1,
        password_hash = $2,
        updated_at = NOW()
      WHERE id = $3
      RETURNING id, username, email, role, employee_id
    `, [newUsername, hashedPassword, user.id]);

    const updatedUser = updateResult.rows[0];

    // 5. تحديث البريد الإلكتروني للموظف المرتبط (اختياري)
    console.log('\n📧 تحديث البريد الإلكتروني للموظف...');
    
    await client.query(`
      UPDATE employees 
      SET 
        email = $1,
        updated_at = NOW()
      WHERE id = $2
    `, ['<EMAIL>', user.employee_id]);

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم تحديث بيانات المستخدم بنجاح!');
    console.log('================================');
    console.log('🔐 بيانات تسجيل الدخول الجديدة:');
    console.log(`   👤 اسم المستخدم: ${updatedUser.username}`);
    console.log(`   🔑 كلمة المرور: ana8080`);
    console.log(`   📧 البريد الإلكتروني: <EMAIL>`);
    console.log(`   👨‍💼 الدور: ${updatedUser.role}`);
    console.log(`   🆔 معرف المستخدم: ${updatedUser.id}`);
    console.log(`   🔗 مرتبط بالموظف: ${updatedUser.employee_id}`);
    
    console.log('\n================================');
    console.log('✅ يمكنك الآن تسجيل الدخول باستخدام:');
    console.log(`   اسم المستخدم: ${updatedUser.username}`);
    console.log(`   كلمة المرور: ana8080`);
    console.log('================================');

    // 6. عرض معلومات إضافية
    console.log('\n📊 معلومات إضافية:');
    
    // جلب معلومات الموظف
    const employeeInfo = await client.query(`
      SELECT name, position, department, phone 
      FROM employees 
      WHERE id = $1
    `, [user.employee_id]);

    if (employeeInfo.rows.length > 0) {
      const emp = employeeInfo.rows[0];
      console.log('👤 معلومات الموظف المرتبط:');
      console.log(`   الاسم: ${emp.name}`);
      console.log(`   المنصب: ${emp.position}`);
      console.log(`   القسم: ${emp.department}`);
      console.log(`   الهاتف: ${emp.phone}`);
    }

    // جلب معلومات الصلاحيات
    const userPermissions = await client.query(`
      SELECT permissions, user_type 
      FROM users 
      WHERE id = $1
    `, [user.id]);

    if (userPermissions.rows.length > 0) {
      const perms = userPermissions.rows[0];
      console.log('\n🔐 الصلاحيات:');
      console.log(`   نوع المستخدم: ${perms.user_type}`);
      console.log(`   الصلاحيات: ${perms.permissions}`);
    }

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تحديث بيانات المستخدم:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

updateManagerCredentials().catch(console.error);
