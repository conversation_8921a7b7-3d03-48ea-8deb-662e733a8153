{"version": 3, "sources": ["../../../src/server/route-matcher-providers/pages-route-matcher-provider.ts"], "sourcesContent": ["import { isAPIRoute } from '../../lib/is-api-route'\nimport { BLOCKED_PAGES, PAGES_MANIFEST } from '../../shared/lib/constants'\nimport { RouteKind } from '../route-kind'\nimport {\n  PagesLocaleRouteMatcher,\n  PagesRouteMatcher,\n} from '../route-matchers/pages-route-matcher'\nimport type {\n  <PERSON>ife<PERSON>,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\nimport type { I18NProvider } from '../lib/i18n-provider'\nimport { PagesNormalizers } from '../normalizers/built/pages'\n\nexport class PagesRouteMatcherProvider extends ManifestRouteMatcherProvider<PagesRouteMatcher> {\n  private readonly normalizers: PagesNormalizers\n\n  constructor(\n    distDir: string,\n    manifestLoader: ManifestLoader,\n    private readonly i18nProvider?: I18NProvider\n  ) {\n    super(PAGES_MANIFEST, manifestLoader)\n\n    this.normalizers = new PagesNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<PagesRouteMatcher>> {\n    // This matcher is only for Pages routes, not Pages API routes which are\n    // included in this manifest.\n    const pathnames = Object.keys(manifest)\n      .filter((pathname) => !isAPIRoute(pathname))\n      // Remove any blocked pages (page that can't be routed to, like error or\n      // internal pages).\n      .filter((pathname) => {\n        const normalized =\n          this.i18nProvider?.analyze(pathname).pathname ?? pathname\n\n        // Skip any blocked pages.\n        if (BLOCKED_PAGES.includes(normalized)) return false\n\n        return true\n      })\n\n    const matchers: Array<PagesRouteMatcher> = []\n    for (const page of pathnames) {\n      if (this.i18nProvider) {\n        // Match the locale on the page name, or default to the default locale.\n        const { detectedLocale, pathname } = this.i18nProvider.analyze(page)\n\n        matchers.push(\n          new PagesLocaleRouteMatcher({\n            kind: RouteKind.PAGES,\n            pathname,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n            i18n: {\n              locale: detectedLocale,\n            },\n          })\n        )\n      } else {\n        matchers.push(\n          new PagesRouteMatcher({\n            kind: RouteKind.PAGES,\n            // In `pages/`, the page is the same as the pathname.\n            pathname: page,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["isAPIRoute", "BLOCKED_PAGES", "PAGES_MANIFEST", "RouteKind", "PagesLocaleRouteMatcher", "PagesRouteMatcher", "ManifestRouteMatcherProvider", "PagesNormalizers", "PagesRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "normalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "normalized", "analyze", "includes", "matchers", "page", "detectedLocale", "push", "kind", "PAGES", "bundlePath", "normalize", "filename", "i18n", "locale"], "mappings": "AAAA,SAASA,UAAU,QAAQ,yBAAwB;AACnD,SAASC,aAAa,EAAEC,cAAc,QAAQ,6BAA4B;AAC1E,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,uBAAuB,EACvBC,iBAAiB,QACZ,wCAAuC;AAK9C,SAASC,4BAA4B,QAAQ,oCAAmC;AAEhF,SAASC,gBAAgB,QAAQ,6BAA4B;AAE7D,OAAO,MAAMC,kCAAkCF;IAG7CG,YACEC,OAAe,EACfC,cAA8B,EAC9B,AAAiBC,YAA2B,CAC5C;QACA,KAAK,CAACV,gBAAgBS,sBAFLC,eAAAA;QAIjB,IAAI,CAACC,WAAW,GAAG,IAAIN,iBAAiBG;IAC1C;IAEA,MAAgBI,UACdC,QAAkB,EACyB;QAC3C,wEAAwE;QACxE,6BAA6B;QAC7B,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAC3BI,MAAM,CAAC,CAACC,WAAa,CAACpB,WAAWoB,UAClC,wEAAwE;QACxE,mBAAmB;SAClBD,MAAM,CAAC,CAACC;gBAEL;YADF,MAAMC,aACJ,EAAA,qBAAA,IAAI,CAACT,YAAY,qBAAjB,mBAAmBU,OAAO,CAACF,UAAUA,QAAQ,KAAIA;YAEnD,0BAA0B;YAC1B,IAAInB,cAAcsB,QAAQ,CAACF,aAAa,OAAO;YAE/C,OAAO;QACT;QAEF,MAAMG,WAAqC,EAAE;QAC7C,KAAK,MAAMC,QAAQT,UAAW;YAC5B,IAAI,IAAI,CAACJ,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEc,cAAc,EAAEN,QAAQ,EAAE,GAAG,IAAI,CAACR,YAAY,CAACU,OAAO,CAACG;gBAE/DD,SAASG,IAAI,CACX,IAAIvB,wBAAwB;oBAC1BwB,MAAMzB,UAAU0B,KAAK;oBACrBT;oBACAK;oBACAK,YAAY,IAAI,CAACjB,WAAW,CAACiB,UAAU,CAACC,SAAS,CAACN;oBAClDO,UAAU,IAAI,CAACnB,WAAW,CAACmB,QAAQ,CAACD,SAAS,CAAChB,QAAQ,CAACU,KAAK;oBAC5DQ,MAAM;wBACJC,QAAQR;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASG,IAAI,CACX,IAAItB,kBAAkB;oBACpBuB,MAAMzB,UAAU0B,KAAK;oBACrB,qDAAqD;oBACrDT,UAAUK;oBACVA;oBACAK,YAAY,IAAI,CAACjB,WAAW,CAACiB,UAAU,CAACC,SAAS,CAACN;oBAClDO,UAAU,IAAI,CAACnB,WAAW,CAACmB,QAAQ,CAACD,SAAS,CAAChB,QAAQ,CAACU,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}