# ملخص نقل نظام الإدارة القانونية
# Legal Management System Transfer Summary

## 📦 ما تم نقله / What Was Transferred

### ✅ الملفات الأساسية / Core Files
- **package.json** - تبعيات المشروع ومعلومات التكوين
- **package-lock.json** - قفل إصدارات التبعيات
- **tsconfig.json** - إعدادات TypeScript
- **next.config.ts** - إعدادات Next.js
- **eslint.config.mjs** - إعدادات ESLint
- **postcss.config.mjs** - إعدادات PostCSS

### 📂 المجلدات الأساسية / Core Directories

#### 🔧 src/ - الكود المصدري
- **app/** - صفحات التطبيق (Next.js 13+ App Router)
- **components/** - مكونات React القابلة لإعادة الاستخدام
- **lib/** - مكتبات ووظائف مساعدة
- **hooks/** - React Hooks مخصصة
- **middleware.ts** - وسطاء Next.js للمصادقة والحماية

#### 🎨 public/ - الملفات العامة
- الصور والأيقونات
- ملفات الخطوط
- ملفات التحميل

#### 🗄️ database/ - قاعدة البيانات
- سكريپتات إنشاء الجداول
- سكريپتات الهجرة
- ملفات الإعداد والصيانة

#### ⚙️ scripts/ - السكريپتات
- سكريپتات التشغيل والإيقاف
- سكريپتات الصيانة
- سكريپتات إعداد البيانات

#### ⚖️ laws/ - القوانين والتشريعات
- ملفات القوانين اليمنية
- التشريعات والأنظمة
- الدستور والمواثيق

#### 🔤 fonts/ - الخطوط
- خطوط عربية مخصصة
- خطوط النظام

### 💾 النسخة الاحتياطية / Database Backup
- **database_backups/mohammi_backup_20250821_175739.sql.gz**
- حجم الملف المضغوط: 60KB
- يحتوي على جميع البيانات والجداول

## 🚀 خطوات التثبيت السريعة / Quick Installation Steps

### للمستخدمين المتقدمين / For Advanced Users
```bash
# 1. نسخ المجلد إلى الجهاز الجديد
# 2. تثبيت التبعيات
npm install

# 3. إعداد قاعدة البيانات
createdb -U postgres mohammi
gunzip database_backups/*.gz
psql -U postgres -d mohammi < database_backups/*.sql

# 4. تحديث إعدادات قاعدة البيانات في src/lib/database.ts
# 5. تشغيل النظام
npm run dev
```

### للمستخدمين العاديين / For Regular Users
1. **شغل ملف WINDOWS_SETUP.bat** (على Windows)
2. **اتبع التعليمات التفاعلية**
3. **أدخل كلمة مرور PostgreSQL عند الطلب**

## 🔧 المتطلبات / Requirements

### البرامج المطلوبة / Required Software
- **Node.js** v18+ ([تحميل](https://nodejs.org/))
- **PostgreSQL** v12+ ([تحميل](https://www.postgresql.org/download/))
- **Git** (اختياري) ([تحميل](https://git-scm.com/))

### مواصفات النظام / System Specifications
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **الذاكرة**: 4GB RAM (الحد الأدنى)
- **التخزين**: 2GB مساحة فارغة
- **الشبكة**: اتصال إنترنت للتحديثات

## 🌐 معلومات الوصول / Access Information

### الروابط / URLs
- **التطوير**: http://localhost:7443
- **الإنتاج**: حسب إعدادات الخادم

### بيانات الدخول الافتراضية / Default Credentials
- **المستخدم**: admin
- **كلمة المرور**: admin123

### قاعدة البيانات / Database
- **الاسم**: mohammi
- **المستخدم**: postgres
- **المنفذ**: 5432

## 📋 قائمة التحقق / Checklist

### قبل النقل / Before Transfer
- [x] إنشاء نسخة احتياطية من قاعدة البيانات
- [x] نسخ جميع الملفات الأساسية
- [x] تجميع ملفات التوثيق
- [x] إنشاء سكريپتات التثبيت

### بعد النقل / After Transfer
- [ ] تثبيت Node.js
- [ ] تثبيت PostgreSQL
- [ ] نسخ مجلد mohaminew
- [ ] تشغيل WINDOWS_SETUP.bat
- [ ] تحديث كلمة مرور قاعدة البيانات
- [ ] اختبار تشغيل النظام
- [ ] تغيير كلمات المرور الافتراضية

## 🛠️ الملفات المساعدة / Helper Files

### ملفات التوثيق / Documentation Files
- **README_TRANSFER.md** - دليل التثبيت التفصيلي
- **WINDOWS_SETUP.bat** - سكريپت التثبيت التلقائي
- **TRANSFER_SUMMARY.md** - هذا الملف
- **LOGIN_CREDENTIALS.md** - بيانات الدخول
- **DATABASE_SCHEMA_DIAGRAM.md** - مخطط قاعدة البيانات

### سكريپتات مفيدة / Useful Scripts
- **backup_database.sh** - إنشاء نسخة احتياطية
- **start-server.sh** - تشغيل الخادم
- **stop-server.sh** - إيقاف الخادم
- **restart-server.sh** - إعادة تشغيل الخادم

## ⚠️ تحذيرات مهمة / Important Warnings

1. **لا تنس تحديث كلمة مرور قاعدة البيانات** في ملف `src/lib/database.ts`
2. **غير كلمات المرور الافتراضية** بعد التثبيت
3. **تأكد من تشغيل PostgreSQL** قبل بدء النظام
4. **احتفظ بنسخة احتياطية** من قاعدة البيانات بانتظام
5. **تأكد من توفر المنفذ 7443** أو غيره حسب الحاجة

## 📞 الدعم الفني / Technical Support

### في حالة المشاكل / In Case of Issues
1. راجع ملف **README_TRANSFER.md** للتفاصيل
2. تحقق من تشغيل جميع الخدمات المطلوبة
3. راجع ملفات السجل (logs) للأخطاء
4. تأكد من صحة إعدادات قاعدة البيانات

### أوامر التشخيص / Diagnostic Commands
```bash
# فحص Node.js
node --version

# فحص PostgreSQL
psql --version

# فحص حالة قاعدة البيانات
psql -U postgres -d mohammi -c "SELECT version();"

# فحص المنافذ
netstat -ano | findstr :7443
```

## ✅ نجح التثبيت؟ / Installation Successful?

إذا تم كل شيء بنجاح، يجب أن تتمكن من:
- الوصول إلى النظام عبر http://localhost:7443
- تسجيل الدخول باستخدام admin/admin123
- رؤية لوحة التحكم الرئيسية
- الوصول إلى جميع وظائف النظام

**مبروك! تم نقل النظام بنجاح! 🎉**
