import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الأرصدة الافتتاحية
export async function GET() {
  try {
    // جلب الأرصدة الافتتاحية مع معلومات الحسابات
    const result = await query(`
      SELECT 
        ob.id,
        ob.account_id,
        ob.debit_balance,
        ob.credit_balance,
        ob.balance_date,
        ob.created_date,
        CASE 
          WHEN ob.account_id ~ '^[0-9]+$' THEN coa.account_code
          WHEN ob.account_id LIKE 'client_%' THEN 'C' || LPAD(SUBSTRING(ob.account_id FROM 8)::text, 6, '0')
          WHEN ob.account_id LIKE 'employee_%' THEN 'E' || LPAD(SUBSTRING(ob.account_id FROM 10)::text, 6, '0')
          ELSE ob.account_id
        END as account_code,
        CASE 
          WHEN ob.account_id ~ '^[0-9]+$' THEN coa.account_name
          WHEN ob.account_id LIKE 'client_%' THEN c.name
          WHEN ob.account_id LIKE 'employee_%' THEN e.name
          ELSE 'حساب غير معروف'
        END as account_name
      FROM opening_balances ob
      LEFT JOIN chart_of_accounts coa ON ob.account_id ~ '^[0-9]+$' AND ob.account_id::integer = coa.id
      LEFT JOIN clients c ON ob.account_id LIKE 'client_%' AND SUBSTRING(ob.account_id FROM 8) ~ '^[0-9]+$' AND SUBSTRING(ob.account_id FROM 8)::integer = c.id
      LEFT JOIN employees e ON ob.account_id LIKE 'employee_%' AND SUBSTRING(ob.account_id FROM 10) ~ '^[0-9]+$' AND SUBSTRING(ob.account_id FROM 10)::integer = e.id
      ORDER BY 
        CASE 
          WHEN ob.account_id ~ '^[0-9]+$' THEN coa.account_code
          WHEN ob.account_id LIKE 'client_%' THEN 'C' || LPAD(SUBSTRING(ob.account_id FROM 8)::text, 6, '0')
          WHEN ob.account_id LIKE 'employee_%' THEN 'E' || LPAD(SUBSTRING(ob.account_id FROM 10)::text, 6, '0')
          ELSE ob.account_id
        END
    `)

    return NextResponse.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      message: 'تم جلب الأرصدة الافتتاحية بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب الأرصدة الافتتاحية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الأرصدة الافتتاحية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة رصيد افتتاحي جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      account_id,
      debit_balance = 0,
      credit_balance = 0,
      balance_date
    } = body

    // التحقق من صحة البيانات
    if (!account_id || !balance_date) {
      return NextResponse.json({
        success: false,
        error: 'الحساب وتاريخ الرصيد مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم وجود رصيد افتتاحي للحساب مسبقاً
    const existingBalance = await query(
      'SELECT id FROM opening_balances WHERE account_id = $1',
      [account_id]
    )

    if (existingBalance.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'يوجد رصيد افتتاحي لهذا الحساب مسبقاً'
      }, { status: 400 })
    }

    // تحويل القيم إلى أرقام للتحقق
    const debitAmount = parseFloat(debit_balance) || 0
    const creditAmount = parseFloat(credit_balance) || 0

    // التحقق من أن أحد الرصيدين على الأقل أكبر من صفر
    if (debitAmount <= 0 && creditAmount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'يجب أن يكون أحد الرصيدين على الأقل أكبر من صفر'
      }, { status: 400 })
    }

    // التحقق من أن الرصيدين ليسا أكبر من صفر في نفس الوقت
    if (debitAmount > 0 && creditAmount > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن أن يكون للحساب رصيد مدين ودائن في نفس الوقت'
      }, { status: 400 })
    }

    const result = await query(`
      INSERT INTO opening_balances (account_id, debit_balance, credit_balance, balance_date)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [
      parseInt(account_id),
      parseFloat(debit_balance) || 0,
      parseFloat(credit_balance) || 0,
      balance_date
    ])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الرصيد الافتتاحي بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الرصيد الافتتاحي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الرصيد الافتتاحي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث رصيد افتتاحي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      account_id,
      debit_balance = 0,
      credit_balance = 0,
      balance_date
    } = body

    if (!id || !account_id || !balance_date) {
      return NextResponse.json({
        success: false,
        error: 'المعرف والحساب وتاريخ الرصيد مطلوبان'
      }, { status: 400 })
    }

    // تحويل القيم إلى أرقام للتحقق
    const debitAmount = parseFloat(debit_balance) || 0
    const creditAmount = parseFloat(credit_balance) || 0

    // التحقق من أن أحد الرصيدين على الأقل أكبر من صفر
    if (debitAmount <= 0 && creditAmount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'يجب أن يكون أحد الرصيدين على الأقل أكبر من صفر'
      }, { status: 400 })
    }

    // التحقق من أن الرصيدين ليسا أكبر من صفر في نفس الوقت
    if (debitAmount > 0 && creditAmount > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن أن يكون للحساب رصيد مدين ودائن في نفس الوقت'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE opening_balances
      SET account_id = $1, debit_balance = $2, credit_balance = $3, balance_date = $4
      WHERE id = $5
      RETURNING *
    `, [
      parseInt(account_id),
      parseFloat(debit_balance) || 0,
      parseFloat(credit_balance) || 0,
      balance_date,
      parseInt(id)
    ])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الرصيد الافتتاحي غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الرصيد الافتتاحي بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الرصيد الافتتاحي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الرصيد الافتتاحي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف رصيد افتتاحي
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الرصيد الافتتاحي مطلوب'
      }, { status: 400 })
    }

    const result = await query(
      'DELETE FROM opening_balances WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الرصيد الافتتاحي غير موجود'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الرصيد الافتتاحي بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الرصيد الافتتاحي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الرصيد الافتتاحي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
