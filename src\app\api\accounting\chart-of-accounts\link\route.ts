import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { account_id, link_type } = await request.json()

    if (!account_id || !link_type) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب ونوع الربط مطلوبان'
      }, { status: 400 })
    }

    // جلب بيانات الحساب
    const accountResult = await query(
      'SELECT id, account_code, account_name FROM chart_of_accounts WHERE id = $1',
      [account_id]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    const account = accountResult.rows[0]
    let results = { created_accounts: 0, linked_records: 0 }

    switch (link_type) {
      case 'clients':
        results = await linkClients(account)
        break
      case 'employees':
        results = await linkEmployees(account)
        break
      case 'suppliers':
        results = await linkSuppliers(account)
        break
      default:
        return NextResponse.json({
          success: false,
          error: 'نوع ربط غير صحيح'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: `تم ربط ${results.linked_records} ${getLinkTypeLabel(link_type)} بالحساب ${account.account_code} مباشرة`,
      details: results
    })

  } catch (error) {
    console.error('Error in chart accounts linking:', error)
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في تطبيق الربط'
    }, { status: 500 })
  }
}

async function linkClients(parentAccount: any) {
  try {
    // جلب جميع العملاء النشطين
    const clients = await query(
      'SELECT id, name FROM clients WHERE status = $1 ORDER BY id',
      ['active']
    )

    let linkedRecords = 0

    // ربط جميع العملاء بالحساب الأب مباشرة
    const updateResult = await query(
      'UPDATE clients SET account_id = $1 WHERE status = $2',
      [parentAccount.id, 'active']
    )

    linkedRecords = updateResult.rowCount || 0

    console.log(`✅ تم ربط ${linkedRecords} عميل بالحساب ${parentAccount.account_code}`)

    return { created_accounts: 0, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in linkClients:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

async function linkEmployees(parentAccount: any) {
  try {
    // ربط جميع الموظفين بالحساب الأب مباشرة
    const updateResult = await query(
      'UPDATE employees SET account_id = $1 WHERE status = $2',
      [parentAccount.id, 'active']
    )

    const linkedRecords = updateResult.rowCount || 0

    console.log(`✅ تم ربط ${linkedRecords} موظف بالحساب ${parentAccount.account_code}`)

    return { created_accounts: 0, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in linkEmployees:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

async function linkSuppliers(parentAccount: any) {
  try {
    // التحقق من وجود جدول الموردين
    const tableCheck = await query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `)

    if (tableCheck.rows.length === 0) {
      return { created_accounts: 0, linked_records: 0 }
    }

    // ربط جميع الموردين بالحساب الأب مباشرة
    const updateResult = await query(
      'UPDATE suppliers SET account_id = $1 WHERE status = $2',
      [parentAccount.id, 'active']
    )

    const linkedRecords = updateResult.rowCount || 0

    console.log(`✅ تم ربط ${linkedRecords} مورد بالحساب ${parentAccount.account_code}`)

    return { created_accounts: 0, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in linkSuppliers:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

function getLinkTypeLabel(linkType: string): string {
  switch (linkType) {
    case 'clients': return 'عميل'
    case 'employees': return 'موظف'
    case 'suppliers': return 'مورد'
    default: return 'سجل'
  }
}
