<!DOCTYPE html>
<html>
<head>
    <title>النظام المتكامل - mohhash</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin: 0;
        }

        .systems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .system-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .system-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .system-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .system-icon {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
        }

        .legal-system .system-icon { color: #e74c3c; }
        .accounting-system .system-icon { color: #27ae60; }
        .hr-system .system-icon { color: #3498db; }

        .system-card h3 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        .system-card p {
            color: #7f8c8d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .system-btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .system-btn:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: scale(1.05);
            text-decoration: none;
            color: white;
        }

        .stats-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🏢 النظام المتكامل</h1>
            <p>نظام إدارة شامل للمحاسبة والموارد البشرية والشؤون القانونية</p>
        </div>

        <div class="systems-grid">
            <!-- النظام القانوني -->
            <div class="system-card legal-system">
                <span class="system-icon">⚖️</span>
                <h3>النظام القانوني</h3>
                <p>إدارة شاملة للقضايا والمتابعات القانونية مع التكامل المحاسبي الكامل</p>
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="system-btn">دخول النظام القانوني</a>
            </div>

            <!-- النظام المحاسبي -->
            <div class="system-card accounting-system">
                <span class="system-icon">💰</span>
                <h3>النظام المحاسبي</h3>
                <p>نظام محاسبي متكامل مع دليل حسابات متقدم وتقارير مالية شاملة</p>
                <a href="/app/fms/?fm=accounting&cmd=dashboard" class="system-btn">دخول النظام المحاسبي</a>
            </div>

            <!-- نظام الموارد البشرية -->
            <div class="system-card hr-system">
                <span class="system-icon">👥</span>
                <h3>شؤون الموظفين</h3>
                <p>إدارة شاملة للموظفين والرواتب والحضور والانصراف</p>
                <a href="hr_menu.html" class="system-btn">دخول نظام الموارد البشرية</a>
            </div>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="stats-section">
            <h3 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">📊 إحصائيات سريعة</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number" id="total-cases">0</span>
                    <div class="stat-label">إجمالي القضايا</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="active-cases">0</span>
                    <div class="stat-label">القضايا النشطة</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-employees">0</span>
                    <div class="stat-label">إجمالي الموظفين</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-accounts">0</span>
                    <div class="stat-label">الحسابات المحاسبية</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="pending-follows">0</span>
                    <div class="stat-label">المتابعات المعلقة</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-clients">0</span>
                    <div class="stat-label">إجمالي العملاء</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 النظام المتكامل - جميع الحقوق محفوظة</p>
            <p>تم التطوير والدمج بنجاح ✨</p>
        </div>
    </div>

    <script>
        // تحميل الإحصائيات (يمكن ربطها بـ APIs حقيقية لاحقاً)
        document.addEventListener('DOMContentLoaded', function() {
            // إحصائيات وهمية للعرض
            document.getElementById('total-cases').textContent = '15';
            document.getElementById('active-cases').textContent = '8';
            document.getElementById('total-employees').textContent = '25';
            document.getElementById('total-accounts').textContent = '150';
            document.getElementById('pending-follows').textContent = '12';
            document.getElementById('total-clients').textContent = '45';

            // يمكن استبدالها بـ AJAX calls حقيقية:
            /*
            fetch('/api/stats/cases').then(r => r.json()).then(data => {
                document.getElementById('total-cases').textContent = data.total;
                document.getElementById('active-cases').textContent = data.active;
            });
            */
        });
    </script>
</body>
</html>
