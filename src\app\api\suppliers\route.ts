import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الموردين
export async function GET() {
  try {
    const result = await query(`
      SELECT
        s.*,
        ma.id as main_account_id,
        ma.account_code as sub_account_code,
        coa.account_code as parent_account_code,
        coa.account_name as parent_account_name,
        coa.current_balance as parent_account_balance,
        ma.balance as individual_balance
      FROM suppliers s
      LEFT JOIN main_accounts ma ON s.account_id = ma.id
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY s.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      suppliers: result.rows
    })
  } catch (error) {
    console.error('Error fetching suppliers:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الموردين' },
      { status: 500 }
    )
  }
}

// POST - إضافة مورد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      company_name,
      phone,
      email,
      address,
      tax_number,
      commercial_register,
      contact_person,
      payment_terms,
      credit_limit = 0,
      account_id
    } = body

    // التحقق من صحة البيانات
    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم المورد مطلوب' },
        { status: 400 }
      )
    }

    // إدراج المورد الجديد
    const result = await query(`
      INSERT INTO suppliers (
        name, company_name, phone, email, address, 
        tax_number, commercial_register, contact_person, 
        payment_terms, credit_limit, account_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      name, company_name, phone, email, address,
      tax_number, commercial_register, contact_person,
      payment_terms, credit_limit, account_id
    ])

    // إنشاء ربط تلقائي مع حساب تحكم الموردين
    if (result.rows[0]) {
      const supplierId = result.rows[0].id
      const defaultAccountId = await query(`
        SELECT id FROM chart_of_accounts WHERE account_code = '********' LIMIT 1
      `)

      if (defaultAccountId.rows.length > 0) {
        await query(`
          INSERT INTO account_links (
            main_account_id, linked_table, linked_record_id,
            sub_account_code, sub_account_name, is_active
          ) VALUES ($1, 'suppliers', $2, $3, $4, true)
        `, [
          defaultAccountId.rows[0].id,
          supplierId,
          'S' + String(supplierId).padStart(6, '0'),
          name + ' (مورد)'
        ])

        // تحديث رصيد الحساب الأب
        await query('SELECT update_parent_account_balance($1)', [defaultAccountId.rows[0].id])
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المورد بنجاح',
      supplier: result.rows[0]
    })

  } catch (error) {
    console.error('Error creating supplier:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المورد' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مورد
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      company_name,
      phone,
      email,
      address,
      tax_number,
      commercial_register,
      contact_person,
      payment_terms,
      credit_limit,
      current_balance,
      status,
      account_id
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المورد مطلوب' },
        { status: 400 }
      )
    }

    // تحديث بيانات المورد
    const result = await query(`
      UPDATE suppliers SET
        name = $1,
        company_name = $2,
        phone = $3,
        email = $4,
        address = $5,
        tax_number = $6,
        commercial_register = $7,
        contact_person = $8,
        payment_terms = $9,
        credit_limit = $10,
        current_balance = $11,
        status = $12,
        account_id = $13,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $14
      RETURNING *
    `, [
      name, company_name, phone, email, address,
      tax_number, commercial_register, contact_person,
      payment_terms, credit_limit, current_balance, status, account_id, id
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المورد غير موجود' },
        { status: 404 }
      )
    }

    // تحديث رصيد الحساب الأب إذا تغير الرصيد
    const accountLink = await query(`
      SELECT main_account_id FROM account_links 
      WHERE linked_table = 'suppliers' AND linked_record_id = $1
    `, [id])

    if (accountLink.rows.length > 0) {
      await query('SELECT update_parent_account_balance($1)', [accountLink.rows[0].main_account_id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المورد بنجاح',
      supplier: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating supplier:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المورد' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مورد
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المورد مطلوب' },
        { status: 400 }
      )
    }

    // الحصول على معرف الحساب الأب قبل الحذف
    const accountLink = await query(`
      SELECT main_account_id FROM account_links 
      WHERE linked_table = 'suppliers' AND linked_record_id = $1
    `, [id])

    // حذف ربط الحساب
    await query(`
      DELETE FROM account_links 
      WHERE linked_table = 'suppliers' AND linked_record_id = $1
    `, [id])

    // حذف المورد
    const result = await query('DELETE FROM suppliers WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المورد غير موجود' },
        { status: 404 }
      )
    }

    // تحديث رصيد الحساب الأب
    if (accountLink.rows.length > 0) {
      await query('SELECT update_parent_account_balance($1)', [accountLink.rows[0].main_account_id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المورد بنجاح'
    })

  } catch (error) {
    console.error('Error deleting supplier:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المورد' },
      { status: 500 }
    )
  }
}
