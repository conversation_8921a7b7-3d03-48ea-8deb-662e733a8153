#!/bin/bash

# Legal System Server Startup Script
# سكريبت بدء تشغيل خادم النظام القانوني

# تعيين متغيرات البيئة
export NODE_ENV=production
export PORT=7443
export DATABASE_URL=postgresql://postgres:yemen123@localhost:5432/mohammi

# الانتقال إلى مجلد المشروع
cd /home/<USER>/Downloads/legal-system

# التحقق من وجود node_modules
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# التحقق من وجود build
if [ ! -d ".next" ]; then
    echo "Building application..."
    npm run build
fi

# بدء الخادم
echo "Starting Legal System Server on port $PORT..."
npm start
