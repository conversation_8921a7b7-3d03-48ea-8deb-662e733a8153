{"version": 3, "sources": ["../../../src/server/use-cache/use-cache-wrapper.ts"], "sourcesContent": ["import type { DeepReadonly } from '../../shared/lib/deep-readonly'\n/* eslint-disable import/no-extraneous-dependencies */\nimport {\n  renderToReadableStream,\n  decodeReply,\n  decodeReplyFromAsyncIterable,\n  createTemporaryReferenceSet as createServerTemporaryReferenceSet,\n} from 'react-server-dom-webpack/server'\nimport {\n  createFromReadableStream,\n  encodeReply,\n  createTemporaryReferenceSet as createClientTemporaryReferenceSet,\n} from 'react-server-dom-webpack/client'\nimport { unstable_prerender as prerender } from 'react-server-dom-webpack/static'\n/* eslint-enable import/no-extraneous-dependencies */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport type {\n  PrerenderStoreModernClient,\n  PrerenderStoreModernRuntime,\n  PrivateUseCacheStore,\n  RequestStore,\n  RevalidateStore,\n  UseCacheStore,\n  WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  getHmrRefreshHash,\n  getRenderResumeDataCache,\n  getPrerenderResumeDataCache,\n  workUnitAsyncStorage,\n  getDraftModeProviderForCacheScope,\n  getCacheSignal,\n  isHmrRefresh,\n  getServerComponentsHmrCache,\n  getRuntimeStagePromise,\n} from '../app-render/work-unit-async-storage.external'\n\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\n\nimport type { ClientReferenceManifestForRsc } from '../../build/webpack/plugins/flight-manifest-plugin'\n\nimport {\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n} from '../app-render/encryption-utils'\nimport type { CacheEntry } from '../lib/cache-handlers/types'\nimport type { CacheSignal } from '../app-render/cache-signal'\nimport { decryptActionBoundArgs } from '../app-render/encryption'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getDigestForWellKnownError } from '../app-render/create-error-handler'\nimport { DYNAMIC_EXPIRE, RUNTIME_PREFETCH_DYNAMIC_STALE } from './constants'\nimport { getCacheHandler } from './handlers'\nimport { UseCacheTimeoutError } from './use-cache-errors'\nimport {\n  createHangingInputAbortSignal,\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n} from '../app-render/dynamic-rendering'\nimport {\n  makeErroringSearchParamsForUseCache,\n  type SearchParams,\n} from '../request/search-params'\nimport type { Params } from '../request/params'\nimport React from 'react'\nimport { createLazyResult, isResolvedLazyResult } from '../lib/lazy-result'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\nimport { isReactLargeShellError } from '../app-render/react-large-shell-error'\nimport type { CacheLife } from './cache-life'\n\ninterface PrivateCacheContext {\n  readonly kind: 'private'\n  readonly outerWorkUnitStore:\n    | RequestStore\n    | PrivateUseCacheStore\n    | PrerenderStoreModernRuntime\n}\n\ninterface PublicCacheContext {\n  readonly kind: 'public'\n  // TODO: We should probably forbid nesting \"use cache\" inside unstable_cache.\n  readonly outerWorkUnitStore:\n    | Exclude<WorkUnitStore, PrerenderStoreModernClient>\n    | undefined\n}\n\ntype CacheContext = PrivateCacheContext | PublicCacheContext\n\ntype CacheKeyParts =\n  | [buildId: string, id: string, args: unknown[]]\n  | [buildId: string, id: string, args: unknown[], hmrRefreshHash: string]\n\ninterface UseCacheInnerPageComponentProps {\n  params: Promise<Params>\n  searchParams?: Promise<SearchParams>\n}\n\nexport interface UseCachePageComponentProps {\n  params: Promise<Params>\n  searchParams: Promise<SearchParams>\n  $$isPageComponent: true\n}\n\nexport type UseCacheLayoutComponentProps = {\n  params: Promise<Params>\n  $$isLayoutComponent: true\n} & {\n  // The value type should be React.ReactNode. But such an index signature would\n  // be incompatible with the other two props.\n  [slot: string]: any\n}\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst debug = process.env.NEXT_PRIVATE_DEBUG_CACHE\n  ? console.debug.bind(console, 'use-cache:')\n  : undefined\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\nconst findSourceMapURL =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .findSourceMapURLDEV\n    : undefined\n\nfunction generateCacheEntry(\n  workStore: WorkStore,\n  cacheContext: CacheContext,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  sharedErrorStack: string | undefined\n) {\n  // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n  // generation cannot read anything from the context we're currently executing which\n  // might include request specific things like cookies() inside a React.cache().\n  // Note: It is important that we await at least once before this because it lets us\n  // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n  return workStore.runInCleanSnapshot(\n    generateCacheEntryWithRestoredWorkStore,\n    workStore,\n    cacheContext,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    sharedErrorStack\n  )\n}\n\nfunction generateCacheEntryWithRestoredWorkStore(\n  workStore: WorkStore,\n  cacheContext: CacheContext,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  sharedErrorStack: string | undefined\n) {\n  // Since we cleared the AsyncLocalStorage we need to restore the workStore.\n  // Note: We explicitly don't restore the RequestStore nor the PrerenderStore.\n  // We don't want any request specific information leaking an we don't want to create a\n  // bloated fake request mock for every cache call. So any feature that currently lives\n  // in RequestStore but should be available to Caches need to move to WorkStore.\n  // PrerenderStore is not needed inside the cache scope because the outer most one will\n  // be the one to report its result to the outer Prerender.\n  return workAsyncStorage.run(\n    workStore,\n    generateCacheEntryWithCacheContext,\n    workStore,\n    cacheContext,\n    clientReferenceManifest,\n    encodedArguments,\n    fn,\n    sharedErrorStack\n  )\n}\n\nfunction createUseCacheStore(\n  workStore: WorkStore,\n  cacheContext: CacheContext,\n  defaultCacheLife: Required<CacheLife>\n): UseCacheStore {\n  if (cacheContext.kind === 'private') {\n    const outerWorkUnitStore = cacheContext.outerWorkUnitStore\n\n    return {\n      type: 'private-cache',\n      phase: 'render',\n      implicitTags: outerWorkUnitStore?.implicitTags,\n      revalidate: defaultCacheLife.revalidate,\n      expire: defaultCacheLife.expire,\n      stale: defaultCacheLife.stale,\n      explicitRevalidate: undefined,\n      explicitExpire: undefined,\n      explicitStale: undefined,\n      tags: null,\n      hmrRefreshHash: getHmrRefreshHash(workStore, outerWorkUnitStore),\n      isHmrRefresh: isHmrRefresh(workStore, outerWorkUnitStore),\n      serverComponentsHmrCache: getServerComponentsHmrCache(\n        workStore,\n        outerWorkUnitStore\n      ),\n      forceRevalidate: shouldForceRevalidate(workStore, outerWorkUnitStore),\n      runtimeStagePromise: getRuntimeStagePromise(outerWorkUnitStore),\n      draftMode: getDraftModeProviderForCacheScope(\n        workStore,\n        outerWorkUnitStore\n      ),\n      rootParams: outerWorkUnitStore.rootParams,\n      cookies: outerWorkUnitStore.cookies,\n    }\n  } else {\n    let useCacheOrRequestStore: RequestStore | UseCacheStore | undefined\n    const outerWorkUnitStore = cacheContext.outerWorkUnitStore\n\n    if (outerWorkUnitStore) {\n      switch (outerWorkUnitStore?.type) {\n        case 'cache':\n        case 'private-cache':\n        case 'request':\n          useCacheOrRequestStore = outerWorkUnitStore\n          break\n        case 'prerender-runtime':\n        case 'prerender':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'unstable-cache':\n          break\n        default:\n          outerWorkUnitStore satisfies never\n      }\n    }\n\n    return {\n      type: 'cache',\n      phase: 'render',\n      implicitTags: outerWorkUnitStore?.implicitTags,\n      revalidate: defaultCacheLife.revalidate,\n      expire: defaultCacheLife.expire,\n      stale: defaultCacheLife.stale,\n      explicitRevalidate: undefined,\n      explicitExpire: undefined,\n      explicitStale: undefined,\n      tags: null,\n      hmrRefreshHash:\n        outerWorkUnitStore && getHmrRefreshHash(workStore, outerWorkUnitStore),\n      isHmrRefresh: useCacheOrRequestStore?.isHmrRefresh ?? false,\n      serverComponentsHmrCache:\n        useCacheOrRequestStore?.serverComponentsHmrCache,\n      forceRevalidate: shouldForceRevalidate(workStore, outerWorkUnitStore),\n      draftMode:\n        outerWorkUnitStore &&\n        getDraftModeProviderForCacheScope(workStore, outerWorkUnitStore),\n    }\n  }\n}\n\nfunction assertDefaultCacheLife(\n  defaultCacheLife: CacheLife | undefined\n): asserts defaultCacheLife is Required<CacheLife> {\n  if (\n    !defaultCacheLife ||\n    defaultCacheLife.revalidate == null ||\n    defaultCacheLife.expire == null ||\n    defaultCacheLife.stale == null\n  ) {\n    throw new InvariantError(\n      'A default cacheLife profile must always be provided.'\n    )\n  }\n}\n\nfunction generateCacheEntryWithCacheContext(\n  workStore: WorkStore,\n  cacheContext: CacheContext,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  sharedErrorStack: string | undefined\n) {\n  if (!workStore.cacheLifeProfiles) {\n    throw new InvariantError('cacheLifeProfiles should always be provided.')\n  }\n  const defaultCacheLife = workStore.cacheLifeProfiles['default']\n  assertDefaultCacheLife(defaultCacheLife)\n\n  // Initialize the Store for this Cache entry.\n  const cacheStore = createUseCacheStore(\n    workStore,\n    cacheContext,\n    defaultCacheLife\n  )\n\n  return workUnitAsyncStorage.run(cacheStore, () =>\n    dynamicAccessAsyncStorage.run(\n      { abortController: new AbortController() },\n      generateCacheEntryImpl,\n      workStore,\n      cacheContext,\n      cacheStore,\n      clientReferenceManifest,\n      encodedArguments,\n      fn,\n      sharedErrorStack\n    )\n  )\n}\n\nfunction propagateCacheLifeAndTagsToRevalidateStore(\n  revalidateStore: RevalidateStore,\n  entry: CacheEntry\n): void {\n  const outerTags = (revalidateStore.tags ??= [])\n\n  for (const tag of entry.tags) {\n    if (!outerTags.includes(tag)) {\n      outerTags.push(tag)\n    }\n  }\n\n  if (revalidateStore.stale > entry.stale) {\n    revalidateStore.stale = entry.stale\n  }\n\n  if (revalidateStore.revalidate > entry.revalidate) {\n    revalidateStore.revalidate = entry.revalidate\n  }\n\n  if (revalidateStore.expire > entry.expire) {\n    revalidateStore.expire = entry.expire\n  }\n}\n\nfunction propagateCacheLifeAndTags(\n  cacheContext: CacheContext,\n  entry: CacheEntry\n): void {\n  if (cacheContext.kind === 'private') {\n    switch (cacheContext.outerWorkUnitStore.type) {\n      case 'prerender-runtime':\n      case 'private-cache':\n        propagateCacheLifeAndTagsToRevalidateStore(\n          cacheContext.outerWorkUnitStore,\n          entry\n        )\n        break\n      case 'request':\n      case undefined:\n        break\n      default:\n        cacheContext.outerWorkUnitStore satisfies never\n    }\n  } else {\n    switch (cacheContext.outerWorkUnitStore?.type) {\n      case 'cache':\n      case 'private-cache':\n      case 'prerender':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        propagateCacheLifeAndTagsToRevalidateStore(\n          cacheContext.outerWorkUnitStore,\n          entry\n        )\n        break\n      case 'request':\n      case 'unstable-cache':\n      case undefined:\n        break\n      default:\n        cacheContext.outerWorkUnitStore satisfies never\n    }\n  }\n}\n\nasync function collectResult(\n  savedStream: ReadableStream,\n  workStore: WorkStore,\n  cacheContext: CacheContext,\n  innerCacheStore: UseCacheStore,\n  startTime: number,\n  errors: Array<unknown> // This is a live array that gets pushed into.\n): Promise<CacheEntry> {\n  // We create a buffered stream that collects all chunks until the end to\n  // ensure that RSC has finished rendering and therefore we have collected\n  // all tags. In the future the RSC API might allow for the equivalent of\n  // the allReady Promise that exists on SSR streams.\n  //\n  // If something errored or rejected anywhere in the render, we close\n  // the stream as errored. This lets a CacheHandler choose to save the\n  // partial result up until that point for future hits for a while to avoid\n  // unnecessary retries or not to retry. We use the end of the stream for\n  // this to avoid another complicated side-channel. A receiver has to consider\n  // that the stream might also error for other reasons anyway such as losing\n  // connection.\n\n  const buffer: any[] = []\n  const reader = savedStream.getReader()\n\n  try {\n    for (let entry; !(entry = await reader.read()).done; ) {\n      buffer.push(entry.value)\n    }\n  } catch (error) {\n    errors.push(error)\n  }\n\n  let idx = 0\n  const bufferStream = new ReadableStream({\n    pull(controller) {\n      if (workStore.invalidDynamicUsageError) {\n        controller.error(workStore.invalidDynamicUsageError)\n      } else if (idx < buffer.length) {\n        controller.enqueue(buffer[idx++])\n      } else if (errors.length > 0) {\n        // TODO: Should we use AggregateError here?\n        controller.error(errors[0])\n      } else {\n        controller.close()\n      }\n    },\n  })\n\n  const collectedTags = innerCacheStore.tags\n  // If cacheLife() was used to set an explicit revalidate time we use that.\n  // Otherwise, we use the lowest of all inner fetch()/unstable_cache() or nested \"use cache\".\n  // If they're lower than our default.\n  const collectedRevalidate =\n    innerCacheStore.explicitRevalidate !== undefined\n      ? innerCacheStore.explicitRevalidate\n      : innerCacheStore.revalidate\n  const collectedExpire =\n    innerCacheStore.explicitExpire !== undefined\n      ? innerCacheStore.explicitExpire\n      : innerCacheStore.expire\n  const collectedStale =\n    innerCacheStore.explicitStale !== undefined\n      ? innerCacheStore.explicitStale\n      : innerCacheStore.stale\n\n  const entry: CacheEntry = {\n    value: bufferStream,\n    timestamp: startTime,\n    revalidate: collectedRevalidate,\n    expire: collectedExpire,\n    stale: collectedStale,\n    tags: collectedTags === null ? [] : collectedTags,\n  }\n\n  // Propagate tags/revalidate to the parent context.\n  if (cacheContext) {\n    propagateCacheLifeAndTags(cacheContext, entry)\n  }\n\n  const cacheSignal = cacheContext.outerWorkUnitStore\n    ? getCacheSignal(cacheContext.outerWorkUnitStore)\n    : null\n\n  if (cacheSignal) {\n    cacheSignal.endRead()\n  }\n\n  return entry\n}\n\ntype GenerateCacheEntryResult =\n  | {\n      readonly type: 'cached'\n      readonly stream: ReadableStream\n      readonly pendingCacheEntry: Promise<CacheEntry>\n    }\n  | {\n      readonly type: 'prerender-dynamic'\n      readonly hangingPromise: Promise<never>\n    }\n\nasync function generateCacheEntryImpl(\n  workStore: WorkStore,\n  cacheContext: CacheContext,\n  innerCacheStore: UseCacheStore,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifestForRsc>,\n  encodedArguments: FormData | string,\n  fn: (...args: unknown[]) => Promise<unknown>,\n  sharedErrorStack: string | undefined\n): Promise<GenerateCacheEntryResult> {\n  const temporaryReferences = createServerTemporaryReferenceSet()\n  const outerWorkUnitStore = cacheContext.outerWorkUnitStore\n\n  const [, , args] =\n    typeof encodedArguments === 'string'\n      ? await decodeReply<CacheKeyParts>(\n          encodedArguments,\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n      : await decodeReplyFromAsyncIterable<CacheKeyParts>(\n          {\n            async *[Symbol.asyncIterator]() {\n              for (const entry of encodedArguments) {\n                yield entry\n              }\n\n              if (outerWorkUnitStore) {\n                switch (outerWorkUnitStore.type) {\n                  case 'prerender-runtime':\n                  case 'prerender':\n                    // The encoded arguments might contain hanging promises. In\n                    // this case we don't want to reject with \"Error: Connection\n                    // closed.\", so we intentionally keep the iterable alive.\n                    // This is similar to the halting trick that we do while\n                    // rendering.\n                    await new Promise<void>((resolve) => {\n                      if (outerWorkUnitStore.renderSignal.aborted) {\n                        resolve()\n                      } else {\n                        outerWorkUnitStore.renderSignal.addEventListener(\n                          'abort',\n                          () => resolve(),\n                          { once: true }\n                        )\n                      }\n                    })\n                    break\n                  case 'prerender-ppr':\n                  case 'prerender-legacy':\n                  case 'request':\n                  case 'cache':\n                  case 'private-cache':\n                  case 'unstable-cache':\n                    break\n                  default:\n                    outerWorkUnitStore satisfies never\n                }\n              }\n            },\n          },\n          getServerModuleMap(),\n          { temporaryReferences }\n        )\n\n  // Track the timestamp when we started computing the result.\n  const startTime = performance.timeOrigin + performance.now()\n\n  // Invoke the inner function to load a new result. We delay the invocation\n  // though, until React awaits the promise so that React's request store (ALS)\n  // is available when the function is invoked. This allows us, for example, to\n  // capture logs so that we can later replay them.\n  const resultPromise = createLazyResult(() => fn.apply(null, args))\n\n  let errors: Array<unknown> = []\n\n  // In the \"Cache\" environment, we only need to make sure that the error\n  // digests are handled correctly. Error formatting and reporting is not\n  // necessary here; the errors are encoded in the stream, and will be reported\n  // in the \"Server\" environment.\n  const handleError = (error: unknown): string | undefined => {\n    const digest = getDigestForWellKnownError(error)\n\n    if (digest) {\n      return digest\n    }\n\n    if (isReactLargeShellError(error)) {\n      // TODO: Aggregate\n      console.error(error)\n      return undefined\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      // TODO: For now we're also reporting the error here, because in\n      // production, the \"Server\" environment will only get the obfuscated\n      // error (created by the Flight Client in the cache wrapper).\n      console.error(error)\n    }\n\n    errors.push(error)\n  }\n\n  let stream: ReadableStream<Uint8Array>\n\n  switch (outerWorkUnitStore?.type) {\n    case 'prerender-runtime':\n    case 'prerender':\n      const timeoutAbortController = new AbortController()\n\n      // If we're prerendering, we give you 50 seconds to fill a cache entry.\n      // Otherwise we assume you stalled on hanging input and de-opt. This needs\n      // to be lower than just the general timeout of 60 seconds.\n      const timer = setTimeout(() => {\n        const error = new UseCacheTimeoutError()\n        if (sharedErrorStack) {\n          error.stack = error.name + ': ' + error.message + sharedErrorStack\n        }\n        workStore.invalidDynamicUsageError = error\n        timeoutAbortController.abort(error)\n      }, 50000)\n\n      const dynamicAccessAbortSignal =\n        dynamicAccessAsyncStorage.getStore()?.abortController.signal\n\n      const abortSignal = dynamicAccessAbortSignal\n        ? AbortSignal.any([\n            dynamicAccessAbortSignal,\n            outerWorkUnitStore.renderSignal,\n            timeoutAbortController.signal,\n          ])\n        : timeoutAbortController.signal\n\n      const { prelude } = await prerender(\n        resultPromise,\n        clientReferenceManifest.clientModules,\n        {\n          environmentName: 'Cache',\n          filterStackFrame,\n          signal: abortSignal,\n          temporaryReferences,\n          onError(error) {\n            if (abortSignal.aborted && abortSignal.reason === error) {\n              return undefined\n            }\n\n            return handleError(error)\n          },\n        }\n      )\n\n      clearTimeout(timer)\n\n      if (timeoutAbortController.signal.aborted) {\n        // When the timeout is reached we always error the stream. Even for\n        // fallback shell prerenders we don't want to return a hanging promise,\n        // which would allow the function to become a dynamic hole. Because that\n        // would mean that a non-empty shell could be generated which would be\n        // subject to revalidation, and we don't want to create long\n        // revalidation times.\n        stream = new ReadableStream({\n          start(controller) {\n            controller.error(timeoutAbortController.signal.reason)\n          },\n        })\n      } else if (dynamicAccessAbortSignal?.aborted) {\n        // If the prerender is aborted because of dynamic access (e.g. reading\n        // fallback params), we return a hanging promise. This essentially makes\n        // the \"use cache\" function dynamic.\n        const hangingPromise = makeHangingPromise<never>(\n          outerWorkUnitStore.renderSignal,\n          workStore.route,\n          abortSignal.reason\n        )\n\n        if (outerWorkUnitStore.cacheSignal) {\n          outerWorkUnitStore.cacheSignal.endRead()\n        }\n\n        return { type: 'prerender-dynamic', hangingPromise }\n      } else {\n        stream = prelude\n      }\n      break\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n    case undefined:\n      stream = renderToReadableStream(\n        resultPromise,\n        clientReferenceManifest.clientModules,\n        {\n          environmentName: 'Cache',\n          filterStackFrame,\n          temporaryReferences,\n          onError: handleError,\n        }\n      )\n      break\n    default:\n      return outerWorkUnitStore satisfies never\n  }\n\n  const [returnStream, savedStream] = stream.tee()\n\n  const pendingCacheEntry = collectResult(\n    savedStream,\n    workStore,\n    cacheContext,\n    innerCacheStore,\n    startTime,\n    errors\n  )\n\n  return {\n    type: 'cached',\n    // Return the stream as we're creating it. This means that if it ends up\n    // erroring we cannot return a stale-if-error version but it allows\n    // streaming back the result earlier.\n    stream: returnStream,\n    pendingCacheEntry,\n  }\n}\n\nfunction cloneCacheEntry(entry: CacheEntry): [CacheEntry, CacheEntry] {\n  const [streamA, streamB] = entry.value.tee()\n  entry.value = streamA\n  const clonedEntry: CacheEntry = {\n    value: streamB,\n    timestamp: entry.timestamp,\n    revalidate: entry.revalidate,\n    expire: entry.expire,\n    stale: entry.stale,\n    tags: entry.tags,\n  }\n  return [entry, clonedEntry]\n}\n\nasync function clonePendingCacheEntry(\n  pendingCacheEntry: Promise<CacheEntry>\n): Promise<[CacheEntry, CacheEntry]> {\n  const entry = await pendingCacheEntry\n  return cloneCacheEntry(entry)\n}\n\nasync function getNthCacheEntry(\n  split: Promise<[CacheEntry, CacheEntry]>,\n  i: number\n): Promise<CacheEntry> {\n  return (await split)[i]\n}\n\nasync function encodeFormData(formData: FormData): Promise<string> {\n  let result = ''\n  for (let [key, value] of formData) {\n    // We don't need this key to be serializable but from a security perspective it should not be\n    // possible to generate a string that looks the same from a different structure. To ensure this\n    // we need a delimeter between fields but just using a delimeter is not enough since a string\n    // might contain that delimeter. We use the length of each field as the delimeter to avoid\n    // escaping the values.\n    result += key.length.toString(16) + ':' + key\n    let stringValue\n    if (typeof value === 'string') {\n      stringValue = value\n    } else {\n      // The FormData might contain binary data that is not valid UTF-8 so this cache\n      // key may generate a UCS-2 string. Passing this to another service needs to be\n      // aware that the key might not be compatible.\n      const arrayBuffer = await value.arrayBuffer()\n      if (arrayBuffer.byteLength % 2 === 0) {\n        stringValue = String.fromCodePoint(...new Uint16Array(arrayBuffer))\n      } else {\n        stringValue =\n          String.fromCodePoint(\n            ...new Uint16Array(arrayBuffer, 0, (arrayBuffer.byteLength - 1) / 2)\n          ) +\n          String.fromCodePoint(\n            new Uint8Array(arrayBuffer, arrayBuffer.byteLength - 1, 1)[0]\n          )\n      }\n    }\n    result += stringValue.length.toString(16) + ':' + stringValue\n  }\n  return result\n}\n\nfunction createTrackedReadableStream(\n  stream: ReadableStream,\n  cacheSignal: CacheSignal\n) {\n  const reader = stream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      const { done, value } = await reader.read()\n      if (done) {\n        controller.close()\n        cacheSignal.endRead()\n      } else {\n        controller.enqueue(value)\n      }\n    },\n  })\n}\n\nfunction wrapAsInvalidDynamicUsageError(\n  error: Error,\n  sharedErrorStack: string | undefined,\n  workStore: WorkStore\n) {\n  if (sharedErrorStack) {\n    error.stack = error.name + ': ' + error.message + sharedErrorStack\n  }\n\n  workStore.invalidDynamicUsageError ??= error\n\n  return error\n}\n\nexport function cache(\n  kind: string,\n  id: string,\n  boundArgsLength: number,\n  originalFn: (...args: unknown[]) => Promise<unknown>\n) {\n  const isPrivate = kind === 'private'\n\n  // Private caches are currently only stored in the Resume Data Cache (RDC),\n  // and not in cache handlers.\n  const cacheHandler = isPrivate ? undefined : getCacheHandler(kind)\n\n  if (!isPrivate && !cacheHandler) {\n    throw new Error('Unknown cache handler: ' + kind)\n  }\n\n  // Capture a better error stack in this scope.\n  const sharedError = new Error()\n  Error.captureStackTrace(sharedError, cache)\n  const sharedErrorStack = sharedError.stack?.slice(\n    sharedError.stack.indexOf('\\n')\n  )\n\n  const name = originalFn.name\n  const cachedFn = {\n    [name]: async function (...args: any[]) {\n      const workStore = workAsyncStorage.getStore()\n      if (workStore === undefined) {\n        throw new Error(\n          '\"use cache\" cannot be used outside of App Router. Expected a WorkStore.'\n        )\n      }\n\n      let fn = originalFn\n\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      let cacheContext: CacheContext\n\n      if (isPrivate) {\n        const expression = '\"use cache: private\"'\n\n        switch (workUnitStore?.type) {\n          // \"use cache: private\" is dynamic in prerendering contexts.\n          case 'prerender':\n            return makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          case 'prerender-ppr':\n            return postponeWithTracking(\n              workStore.route,\n              expression,\n              workUnitStore.dynamicTracking\n            )\n          case 'prerender-legacy':\n            return throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              workUnitStore\n            )\n          case 'prerender-client':\n            throw new InvariantError(\n              `${expression} must not be used within a client component. Next.js should be preventing ${expression} from being allowed in client components statically, but did not in this case.`\n            )\n          case 'unstable-cache': {\n            throw wrapAsInvalidDynamicUsageError(\n              new Error(\n                // TODO: Add a link to an error documentation page when we have one.\n                `${expression} must not be used within \\`unstable_cache()\\`.`\n              ),\n              sharedErrorStack,\n              workStore\n            )\n          }\n          case 'cache': {\n            throw wrapAsInvalidDynamicUsageError(\n              new Error(\n                // TODO: Add a link to an error documentation page when we have one.\n                `${expression} must not be used within \"use cache\". It can only be nested inside of another ${expression}.`\n              ),\n              sharedErrorStack,\n              workStore\n            )\n          }\n          case 'request':\n          case 'prerender-runtime':\n          case 'private-cache':\n            cacheContext = {\n              kind: 'private',\n              outerWorkUnitStore: workUnitStore,\n            }\n            break\n          case undefined:\n            throw wrapAsInvalidDynamicUsageError(\n              new Error(\n                // TODO: Add a link to an error documentation page when we have one.\n                `${expression} cannot be used outside of a request context.`\n              ),\n              sharedErrorStack,\n              workStore\n            )\n          default:\n            workUnitStore satisfies never\n            // This is dead code, but without throwing an error here, TypeScript\n            // will assume that cacheContext is used before being assigned.\n            throw new InvariantError(`Unexpected work unit store.`)\n        }\n      } else {\n        switch (workUnitStore?.type) {\n          case 'prerender-client':\n            const expression = '\"use cache\"'\n            throw new InvariantError(\n              `${expression} must not be used within a client component. Next.js should be preventing ${expression} from being allowed in client components statically, but did not in this case.`\n            )\n          case 'prerender':\n          case 'prerender-runtime':\n          case 'prerender-ppr':\n          case 'prerender-legacy':\n          case 'request':\n          case 'cache':\n          case 'private-cache':\n          // TODO: We should probably forbid nesting \"use cache\" inside\n          // unstable_cache. (fallthrough)\n          case 'unstable-cache':\n          case undefined:\n            cacheContext = {\n              kind: 'public',\n              outerWorkUnitStore: workUnitStore,\n            }\n            break\n          default:\n            workUnitStore satisfies never\n            // This is dead code, but without throwing an error here, TypeScript\n            // will assume that cacheContext is used before being assigned.\n            throw new InvariantError(`Unexpected work unit store.`)\n        }\n      }\n\n      // Get the clientReferenceManifest while we're still in the outer Context.\n      // In case getClientReferenceManifestSingleton is implemented using AsyncLocalStorage.\n      const clientReferenceManifest = getClientReferenceManifestForRsc()\n\n      // Because the Action ID is not yet unique per implementation of that Action we can't\n      // safely reuse the results across builds yet. In the meantime we add the buildId to the\n      // arguments as a seed to ensure they're not reused. Remove this once Action IDs hash\n      // the implementation.\n      const buildId = workStore.buildId\n\n      // In dev mode, when the HMR refresh hash is set, we include it in the\n      // cache key. This ensures that cache entries are not reused when server\n      // components have been edited. This is a very coarse approach. But it's\n      // also only a temporary solution until Action IDs are unique per\n      // implementation. Remove this once Action IDs hash the implementation.\n      const hmrRefreshHash =\n        workUnitStore && getHmrRefreshHash(workStore, workUnitStore)\n\n      const hangingInputAbortSignal = workUnitStore\n        ? createHangingInputAbortSignal(workUnitStore)\n        : undefined\n\n      // In a runtime prerender, we have to make sure that APIs that would hang during a static prerender\n      // are resolved with a delay, in the runtime stage. Private caches are one of these.\n      if (cacheContext.kind === 'private') {\n        const runtimeStagePromise = getRuntimeStagePromise(\n          cacheContext.outerWorkUnitStore\n        )\n        if (runtimeStagePromise) {\n          await runtimeStagePromise\n        }\n      }\n\n      let isPageOrLayout = false\n\n      // For page and layout components, the cache function is overwritten,\n      // which allows us to apply special handling for params and searchParams.\n      // For pages and layouts we're using the outer params prop, and not the\n      // inner one that was serialized/deserialized. While it's not generally\n      // true for \"use cache\" args, in the case of `params` the inner and outer\n      // object are essentially equivalent, so this is safe to do (including\n      // fallback params that are hanging promises). It allows us to avoid\n      // waiting for the timeout, when prerendering a fallback shell of a cached\n      // page or layout that awaits params.\n      if (isPageComponent(args)) {\n        isPageOrLayout = true\n\n        const [{ params: outerParams, searchParams: outerSearchParams }] = args\n\n        const props: UseCacheInnerPageComponentProps = {\n          params: outerParams,\n          // Omit searchParams and $$isPageComponent.\n        }\n\n        if (isPrivate) {\n          // Private caches allow accessing search params. We need to include\n          // them in the serialized args and when generating the cache key.\n          props.searchParams = outerSearchParams\n        }\n\n        args = [props]\n\n        fn = {\n          [name]: async ({\n            params: _innerParams,\n            searchParams: innerSearchParams,\n          }: UseCacheInnerPageComponentProps) =>\n            originalFn.apply(null, [\n              {\n                params: outerParams,\n                searchParams:\n                  innerSearchParams ??\n                  // For public caches, search params are omitted from the cache\n                  // key (and the serialized args) to avoid mismatches between\n                  // prerendering and resuming a cached page that does not\n                  // access search params. This is also the reason why we're not\n                  // using a hanging promise for search params. For cached pages\n                  // that do access them, which is an invalid dynamic usage, we\n                  // need to ensure that an error is shown.\n                  makeErroringSearchParamsForUseCache(workStore),\n              },\n            ]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      } else if (isLayoutComponent(args)) {\n        isPageOrLayout = true\n\n        const [{ params: outerParams, $$isLayoutComponent, ...outerSlots }] =\n          args\n        // Overwrite the props to omit $$isLayoutComponent.\n        args = [{ params: outerParams, ...outerSlots }]\n\n        fn = {\n          [name]: async ({\n            params: _innerParams,\n            ...innerSlots\n          }: Omit<UseCacheLayoutComponentProps, '$$isLayoutComponent'>) =>\n            originalFn.apply(null, [{ params: outerParams, ...innerSlots }]),\n        }[name] as (...args: unknown[]) => Promise<unknown>\n      }\n\n      if (boundArgsLength > 0) {\n        if (args.length === 0) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive its encrypted bound arguments as the first argument.`\n          )\n        }\n\n        const encryptedBoundArgs = args.shift()\n        const boundArgs = await decryptActionBoundArgs(id, encryptedBoundArgs)\n\n        if (!Array.isArray(boundArgs)) {\n          throw new InvariantError(\n            `Expected the bound arguments of \"use cache\" function ${JSON.stringify(fn.name)} to deserialize into an array, got ${typeof boundArgs} instead.`\n          )\n        }\n\n        if (boundArgsLength !== boundArgs.length) {\n          throw new InvariantError(\n            `Expected the \"use cache\" function ${JSON.stringify(fn.name)} to receive ${boundArgsLength} bound arguments, got ${boundArgs.length} instead.`\n          )\n        }\n\n        args.unshift(boundArgs)\n      }\n\n      const temporaryReferences = createClientTemporaryReferenceSet()\n\n      // For private caches, which are allowed to read cookies, we still don't\n      // need to include the cookies in the cache key. This is because we don't\n      // store the cache entries in a cache handler, but only in the Resume Data\n      // Cache (RDC). Private caches are only used during dynamic requests and\n      // runtime prefetches. For dynamic requests, the RDC is immutable, so it\n      // does not include any private caches. For runtime prefetches, the RDC is\n      // mutable, but only lives as long as the request, so the key does not\n      // need to include cookies.\n      const cacheKeyParts: CacheKeyParts = hmrRefreshHash\n        ? [buildId, id, args, hmrRefreshHash]\n        : [buildId, id, args]\n\n      const encodeCacheKeyParts = () =>\n        encodeReply(cacheKeyParts, {\n          temporaryReferences,\n          signal: hangingInputAbortSignal,\n        })\n\n      let encodedCacheKeyParts: FormData | string\n\n      switch (workUnitStore?.type) {\n        case 'prerender-runtime':\n        // We're currently only using `dynamicAccessAsyncStorage` for params,\n        // which are always available in a runtime prerender, so they will never hang,\n        // effectively making the tracking below a no-op.\n        // However, a runtime prerender shares a lot of the semantics with a static prerender,\n        // and might need to follow this codepath in the future\n        // if we start using `dynamicAccessAsyncStorage` for other APIs.\n        //\n        // fallthrough\n        case 'prerender':\n          if (!isPageOrLayout) {\n            // If the \"use cache\" function is not a page or a layout, we need to\n            // track dynamic access already when encoding the arguments. If\n            // params are passed explicitly into a \"use cache\" function (as\n            // opposed to receiving them automatically in a page or layout), we\n            // assume that the params are also accessed. This allows us to abort\n            // early, and treat the function as dynamic, instead of waiting for\n            // the timeout to be reached.\n            const dynamicAccessAbortController = new AbortController()\n\n            encodedCacheKeyParts = await dynamicAccessAsyncStorage.run(\n              { abortController: dynamicAccessAbortController },\n              encodeCacheKeyParts\n            )\n\n            if (dynamicAccessAbortController.signal.aborted) {\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                dynamicAccessAbortController.signal.reason.message\n              )\n            }\n            break\n          }\n        // fallthrough\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'cache':\n        case 'private-cache':\n        case 'unstable-cache':\n        case undefined:\n          encodedCacheKeyParts = await encodeCacheKeyParts()\n          break\n        default:\n          return workUnitStore satisfies never\n      }\n\n      const serializedCacheKey =\n        typeof encodedCacheKeyParts === 'string'\n          ? // Fast path for the simple case for simple inputs. We let the CacheHandler\n            // Convert it to an ArrayBuffer if it wants to.\n            encodedCacheKeyParts\n          : await encodeFormData(encodedCacheKeyParts)\n\n      let stream: undefined | ReadableStream = undefined\n\n      // Get an immutable and mutable versions of the resume data cache.\n      const prerenderResumeDataCache = workUnitStore\n        ? getPrerenderResumeDataCache(workUnitStore)\n        : null\n      const renderResumeDataCache = workUnitStore\n        ? getRenderResumeDataCache(workUnitStore)\n        : null\n\n      if (renderResumeDataCache) {\n        const cacheSignal = workUnitStore ? getCacheSignal(workUnitStore) : null\n\n        if (cacheSignal) {\n          cacheSignal.beginRead()\n        }\n        const cachedEntry = renderResumeDataCache.cache.get(serializedCacheKey)\n        if (cachedEntry !== undefined) {\n          const existingEntry = await cachedEntry\n          propagateCacheLifeAndTags(cacheContext, existingEntry)\n\n          if (workUnitStore !== undefined && existingEntry !== undefined) {\n            if (\n              existingEntry.revalidate === 0 ||\n              existingEntry.expire < DYNAMIC_EXPIRE\n            ) {\n              switch (workUnitStore.type) {\n                case 'prerender':\n                  // In a Dynamic I/O prerender, if the cache entry has\n                  // revalidate: 0 or if the expire time is under 5 minutes, then\n                  // we consider this cache entry dynamic as it's not worth\n                  // generating static pages for such data. It's better to leave a\n                  // PPR hole that can be filled in dynamically with a potentially\n                  // cached entry.\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                  }\n                  return makeHangingPromise(\n                    workUnitStore.renderSignal,\n                    workStore.route,\n                    'dynamic \"use cache\"'\n                  )\n                case 'prerender-runtime': {\n                  // In a runtime prerender, we have to make sure that APIs that would hang during a static prerender\n                  // are resolved with a delay, in the runtime stage.\n                  if (workUnitStore.runtimeStagePromise) {\n                    await workUnitStore.runtimeStagePromise\n                  }\n                  break\n                }\n                case 'prerender-ppr':\n                case 'prerender-legacy':\n                case 'request':\n                case 'cache':\n                case 'private-cache':\n                case 'unstable-cache':\n                  break\n                default:\n                  workUnitStore satisfies never\n              }\n            }\n\n            if (existingEntry.stale < RUNTIME_PREFETCH_DYNAMIC_STALE) {\n              switch (workUnitStore.type) {\n                case 'prerender-runtime':\n                  // In a runtime prerender, if the cache entry will become stale in less then 30 seconds,\n                  // we consider this cache entry dynamic as it's not worth prefetching.\n                  // It's better to leave a PPR hole that can be filled in dynamically\n                  // with a potentially cached entry.\n                  if (cacheSignal) {\n                    cacheSignal.endRead()\n                  }\n                  return makeHangingPromise(\n                    workUnitStore.renderSignal,\n                    workStore.route,\n                    'dynamic \"use cache\"'\n                  )\n                case 'prerender':\n                case 'prerender-ppr':\n                case 'prerender-legacy':\n                case 'request':\n                case 'cache':\n                case 'private-cache':\n                case 'unstable-cache':\n                  break\n                default:\n                  workUnitStore satisfies never\n              }\n            }\n          }\n\n          const [streamA, streamB] = existingEntry.value.tee()\n          existingEntry.value = streamB\n\n          if (cacheSignal) {\n            // When we have a cacheSignal we need to block on reading the cache\n            // entry before ending the read.\n            stream = createTrackedReadableStream(streamA, cacheSignal)\n          } else {\n            stream = streamA\n          }\n        } else {\n          if (cacheSignal) {\n            cacheSignal.endRead()\n          }\n\n          if (workUnitStore) {\n            switch (workUnitStore.type) {\n              case 'prerender':\n                // If `allowEmptyStaticShell` is true, and thus a prefilled\n                // resume data cache was provided, then a cache miss means that\n                // params were part of the cache key. In this case, we can make\n                // this cache function a dynamic hole in the shell (or produce\n                // an empty shell if there's no parent suspense boundary).\n                // Currently, this also includes layouts and pages that don't\n                // read params, which will be improved when we implement\n                // NAR-136. Otherwise, we assume that if params are passed\n                // explicitly into a \"use cache\" function, that the params are\n                // also accessed. This allows us to abort early, and treat the\n                // function as dynamic, instead of waiting for the timeout to be\n                // reached. Compared to the instrumentation-based params bailout\n                // we do here, this also covers the case where params are\n                // transformed with an async function, before being passed into\n                // the \"use cache\" function, which escapes the instrumentation.\n                if (workUnitStore.allowEmptyStaticShell) {\n                  return makeHangingPromise(\n                    workUnitStore.renderSignal,\n                    workStore.route,\n                    'dynamic \"use cache\"'\n                  )\n                }\n                break\n              case 'prerender-runtime':\n              case 'prerender-ppr':\n              case 'prerender-legacy':\n              case 'request':\n              case 'cache':\n              case 'private-cache':\n              case 'unstable-cache':\n                break\n              default:\n                workUnitStore satisfies never\n            }\n          }\n        }\n      }\n\n      if (stream === undefined) {\n        const cacheSignal = workUnitStore ? getCacheSignal(workUnitStore) : null\n        if (cacheSignal) {\n          // Either the cache handler or the generation can be using I/O at this point.\n          // We need to track when they start and when they complete.\n          cacheSignal.beginRead()\n        }\n\n        const lazyRefreshTags = workStore.refreshTagsByCacheKind.get(kind)\n\n        if (lazyRefreshTags && !isResolvedLazyResult(lazyRefreshTags)) {\n          await lazyRefreshTags\n        }\n\n        let entry: CacheEntry | undefined\n\n        // We ignore existing cache entries when force revalidating.\n        if (cacheHandler && !shouldForceRevalidate(workStore, workUnitStore)) {\n          entry = await cacheHandler.get(\n            serializedCacheKey,\n            workUnitStore?.implicitTags?.tags ?? []\n          )\n        }\n\n        if (entry) {\n          const implicitTags = workUnitStore?.implicitTags?.tags ?? []\n          let implicitTagsExpiration = 0\n\n          if (workUnitStore?.implicitTags) {\n            const lazyExpiration =\n              workUnitStore.implicitTags.expirationsByCacheKind.get(kind)\n\n            if (lazyExpiration) {\n              const expiration = isResolvedLazyResult(lazyExpiration)\n                ? lazyExpiration.value\n                : await lazyExpiration\n\n              // If a cache handler returns an expiration time of Infinity, it\n              // signals to Next.js that it handles checking cache entries for\n              // staleness based on the expiration of the implicit tags passed\n              // into the `get` method. In this case, we keep the default of 0,\n              // which means that the implicit tags are not considered expired.\n              if (expiration < Infinity) {\n                implicitTagsExpiration = expiration\n              }\n            }\n          }\n\n          if (\n            shouldDiscardCacheEntry(\n              entry,\n              workStore,\n              workUnitStore,\n              implicitTags,\n              implicitTagsExpiration\n            )\n          ) {\n            debug?.('discarding stale entry', serializedCacheKey)\n            entry = undefined\n          }\n        }\n\n        const currentTime = performance.timeOrigin + performance.now()\n        if (\n          workUnitStore !== undefined &&\n          entry !== undefined &&\n          (entry.revalidate === 0 || entry.expire < DYNAMIC_EXPIRE)\n        ) {\n          switch (workUnitStore.type) {\n            case 'prerender':\n              // In a Dynamic I/O prerender, if the cache entry has revalidate:\n              // 0 or if the expire time is under 5 minutes, then we consider\n              // this cache entry dynamic as it's not worth generating static\n              // pages for such data. It's better to leave a PPR hole that can\n              // be filled in dynamically with a potentially cached entry.\n              if (cacheSignal) {\n                cacheSignal.endRead()\n              }\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                'dynamic \"use cache\"'\n              )\n            case 'prerender-runtime':\n              // In a runtime prerender, we have to make sure that APIs that would hang during a static prerender\n              // are resolved with a delay, in the runtime stage.\n              if (workUnitStore.runtimeStagePromise) {\n                await workUnitStore.runtimeStagePromise\n              }\n              break\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n            case 'request':\n            case 'cache':\n            case 'private-cache':\n            case 'unstable-cache':\n              break\n            default:\n              workUnitStore satisfies never\n          }\n        }\n\n        if (\n          entry === undefined ||\n          currentTime > entry.timestamp + entry.expire * 1000 ||\n          (workStore.isStaticGeneration &&\n            currentTime > entry.timestamp + entry.revalidate * 1000)\n        ) {\n          // Miss. Generate a new result.\n\n          // If the cache entry is stale and we're prerendering, we don't want to use the\n          // stale entry since it would unnecessarily need to shorten the lifetime of the\n          // prerender. We're not time constrained here so we can re-generated it now.\n\n          // We need to run this inside a clean AsyncLocalStorage snapshot so that the cache\n          // generation cannot read anything from the context we're currently executing which\n          // might include request specific things like cookies() inside a React.cache().\n          // Note: It is important that we await at least once before this because it lets us\n          // pop out of any stack specific contexts as well - aka \"Sync\" Local Storage.\n\n          if (entry) {\n            if (currentTime > entry.timestamp + entry.expire * 1000) {\n              debug?.('entry is expired', serializedCacheKey)\n            }\n\n            if (\n              workStore.isStaticGeneration &&\n              currentTime > entry.timestamp + entry.revalidate * 1000\n            ) {\n              debug?.('static generation, entry is stale', serializedCacheKey)\n            }\n          }\n\n          const result = await generateCacheEntry(\n            workStore,\n            cacheContext,\n            clientReferenceManifest,\n            encodedCacheKeyParts,\n            fn,\n            sharedErrorStack\n          )\n\n          if (result.type === 'prerender-dynamic') {\n            return result.hangingPromise\n          }\n\n          const { stream: newStream, pendingCacheEntry } = result\n\n          // When draft mode is enabled, we must not save the cache entry.\n          if (!workStore.isDraftMode) {\n            let savedCacheEntry\n\n            if (prerenderResumeDataCache) {\n              // Create a clone that goes into the cache scope memory cache.\n              const split = clonePendingCacheEntry(pendingCacheEntry)\n              savedCacheEntry = getNthCacheEntry(split, 0)\n              prerenderResumeDataCache.cache.set(\n                serializedCacheKey,\n                getNthCacheEntry(split, 1)\n              )\n            } else {\n              savedCacheEntry = pendingCacheEntry\n            }\n\n            if (cacheHandler) {\n              const promise = cacheHandler.set(\n                serializedCacheKey,\n                savedCacheEntry\n              )\n\n              workStore.pendingRevalidateWrites ??= []\n              workStore.pendingRevalidateWrites.push(promise)\n            }\n          }\n\n          stream = newStream\n        } else {\n          // If we have an entry at this point, this can't be a private cache\n          // entry.\n          if (cacheContext.kind === 'private') {\n            throw new InvariantError(\n              `A private cache entry must not be retrieved from the cache handler.`\n            )\n          }\n\n          propagateCacheLifeAndTags(cacheContext, entry)\n\n          // We want to return this stream, even if it's stale.\n          stream = entry.value\n\n          // If we have a cache scope, we need to clone the entry and set it on\n          // the inner cache scope.\n          if (prerenderResumeDataCache) {\n            const [entryLeft, entryRight] = cloneCacheEntry(entry)\n            if (cacheSignal) {\n              stream = createTrackedReadableStream(entryLeft.value, cacheSignal)\n            } else {\n              stream = entryLeft.value\n            }\n\n            prerenderResumeDataCache.cache.set(\n              serializedCacheKey,\n              Promise.resolve(entryRight)\n            )\n          } else {\n            // If we're not regenerating we need to signal that we've finished\n            // putting the entry into the cache scope at this point. Otherwise we do\n            // that inside generateCacheEntry.\n            cacheSignal?.endRead()\n          }\n\n          if (currentTime > entry.timestamp + entry.revalidate * 1000) {\n            // If this is stale, and we're not in a prerender (i.e. this is\n            // dynamic render), then we should warm up the cache with a fresh\n            // revalidated entry.\n            const result = await generateCacheEntry(\n              workStore,\n              // This is not running within the context of this unit.\n              { kind: cacheContext.kind, outerWorkUnitStore: undefined },\n              clientReferenceManifest,\n              encodedCacheKeyParts,\n              fn,\n              sharedErrorStack\n            )\n\n            if (result.type === 'cached') {\n              const { stream: ignoredStream, pendingCacheEntry } = result\n              let savedCacheEntry: Promise<CacheEntry>\n\n              if (prerenderResumeDataCache) {\n                const split = clonePendingCacheEntry(pendingCacheEntry)\n                savedCacheEntry = getNthCacheEntry(split, 0)\n                prerenderResumeDataCache.cache.set(\n                  serializedCacheKey,\n                  getNthCacheEntry(split, 1)\n                )\n              } else {\n                savedCacheEntry = pendingCacheEntry\n              }\n\n              if (cacheHandler) {\n                const promise = cacheHandler.set(\n                  serializedCacheKey,\n                  savedCacheEntry\n                )\n\n                workStore.pendingRevalidateWrites ??= []\n                workStore.pendingRevalidateWrites.push(promise)\n              }\n\n              await ignoredStream.cancel()\n            }\n          }\n        }\n      }\n\n      // Logs are replayed even if it's a hit - to ensure we see them on the client eventually.\n      // If we didn't then the client wouldn't see the logs if it was seeded from a prewarm that\n      // never made it to the client. However, this also means that you see logs even when the\n      // cached function isn't actually re-executed. We should instead ensure prewarms always\n      // make it to the client. Another issue is that this will cause double logging in the\n      // server terminal. Once while generating the cache entry and once when replaying it on\n      // the server, which is required to pick it up for replaying again on the client.\n      const replayConsoleLogs = true\n\n      const serverConsumerManifest = {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n        // which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime\n          ? clientReferenceManifest.edgeRscModuleMapping\n          : clientReferenceManifest.rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      }\n\n      return createFromReadableStream(stream, {\n        findSourceMapURL,\n        serverConsumerManifest,\n        temporaryReferences,\n        replayConsoleLogs,\n        environmentName: 'Cache',\n      })\n    },\n  }[name]\n\n  return React.cache(cachedFn)\n}\n\nfunction isPageComponent(\n  args: any[]\n): args is [UseCachePageComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCachePageComponentProps).$$isPageComponent\n  )\n}\n\nfunction isLayoutComponent(\n  args: any[]\n): args is [UseCacheLayoutComponentProps, undefined] {\n  if (args.length !== 2) {\n    return false\n  }\n\n  const [props, ref] = args\n\n  return (\n    ref === undefined && // server components receive an undefined ref arg\n    props !== null &&\n    typeof props === 'object' &&\n    (props as UseCacheLayoutComponentProps).$$isLayoutComponent\n  )\n}\n\nfunction shouldForceRevalidate(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore | undefined\n): boolean {\n  if (workStore.isOnDemandRevalidate || workStore.isDraftMode) {\n    return true\n  }\n\n  if (workStore.dev && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        return workUnitStore.headers.get('cache-control') === 'no-cache'\n      case 'cache':\n      case 'private-cache':\n        return workUnitStore.forceRevalidate\n      case 'prerender-runtime':\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  return false\n}\n\nfunction shouldDiscardCacheEntry(\n  entry: CacheEntry,\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore | undefined,\n  implicitTags: string[],\n  implicitTagsExpiration: number\n): boolean {\n  // If the cache entry was created before any of the implicit tags were\n  // revalidated last, we need to discard it.\n  if (entry.timestamp <= implicitTagsExpiration) {\n    debug?.(\n      'entry was created at',\n      entry.timestamp,\n      'before implicit tags were revalidated at',\n      implicitTagsExpiration\n    )\n\n    return true\n  }\n\n  // During prerendering, we ignore recently revalidated tags. In dev mode, we\n  // can assume that the dynamic dev rendering will have discarded and recreated\n  // the affected cache entries, and we don't want to discard those again during\n  // the prerender validation. During build-time prerendering, there will never\n  // be any pending revalidated tags.\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n        return false\n      case 'prerender-runtime':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If the cache entry contains revalidated tags that the cache handler might\n  // not know about yet, we need to discard it.\n  if (entry.tags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  // Finally, if any of the implicit tags have been revalidated recently, we\n  // also need to discard the cache entry.\n  if (implicitTags.some((tag) => isRecentlyRevalidatedTag(tag, workStore))) {\n    return true\n  }\n\n  return false\n}\n\nfunction isRecentlyRevalidatedTag(tag: string, workStore: WorkStore): boolean {\n  const { previouslyRevalidatedTags, pendingRevalidatedTags } = workStore\n\n  // Was the tag previously revalidated (e.g. by a redirecting server action)?\n  if (previouslyRevalidatedTags.includes(tag)) {\n    debug?.('tag', tag, 'was previously revalidated')\n\n    return true\n  }\n\n  // It could also have been revalidated by the currently running server action.\n  // In this case the revalidation might not have been fully propagated by a\n  // remote cache handler yet, so we read it from the pending tags in the work\n  // store.\n  if (pendingRevalidatedTags?.includes(tag)) {\n    debug?.('tag', tag, 'was just revalidated')\n\n    return true\n  }\n\n  return false\n}\n"], "names": ["cache", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "debug", "NEXT_PRIVATE_DEBUG_CACHE", "console", "bind", "undefined", "filterStackFrame", "NODE_ENV", "require", "filterStackFrameDEV", "findSourceMapURL", "findSourceMapURLDEV", "generateCacheEntry", "workStore", "cacheContext", "clientReferenceManifest", "encodedArguments", "fn", "sharedErrorStack", "runInCleanSnapshot", "generateCacheEntryWithRestoredWorkStore", "workAsyncStorage", "run", "generateCacheEntryWithCacheContext", "createUseCacheStore", "defaultCacheLife", "kind", "outerWorkUnitStore", "type", "phase", "implicitTags", "revalidate", "expire", "stale", "explicitRevalidate", "explicitExpire", "explicitStale", "tags", "hmrRefreshHash", "getHmrRefreshHash", "isHmrRefresh", "serverComponentsHmrCache", "getServerComponentsHmrCache", "forceRevalidate", "shouldForceRevalidate", "runtimeStagePromise", "getRuntimeStagePromise", "draftMode", "getDraftModeProviderForCacheScope", "rootParams", "cookies", "useCacheOrRequestStore", "assertDefaultCacheLife", "InvariantError", "cacheLifeProfiles", "cacheStore", "workUnitAsyncStorage", "dynamicAccessAsyncStorage", "abortController", "AbortController", "generateCacheEntryImpl", "propagateCacheLifeAndTagsToRevalidateStore", "revalidateStore", "entry", "outerTags", "tag", "includes", "push", "propagateCacheLifeAndTags", "collectResult", "savedStream", "innerCacheStore", "startTime", "errors", "buffer", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "done", "value", "error", "idx", "bufferStream", "ReadableStream", "pull", "controller", "invalidDynamicUsageError", "length", "enqueue", "close", "collectedTags", "collectedRevalidate", "collectedExpire", "collectedStale", "timestamp", "cacheSignal", "getCacheSignal", "endRead", "temporaryReferences", "createServerTemporaryReferenceSet", "args", "decodeReply", "getServerModuleMap", "decodeReplyFromAsyncIterable", "Symbol", "asyncIterator", "Promise", "resolve", "renderSignal", "aborted", "addEventListener", "once", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "resultPromise", "createLazyResult", "apply", "handleError", "digest", "getDigestForWellKnownError", "isReactLargeShellError", "stream", "timeoutAbortController", "timer", "setTimeout", "UseCacheTimeoutError", "stack", "name", "message", "abort", "dynamicAccessAbortSignal", "getStore", "signal", "abortSignal", "AbortSignal", "any", "prelude", "prerender", "clientModules", "environmentName", "onError", "reason", "clearTimeout", "start", "hanging<PERSON>romise", "makeHangingPromise", "route", "renderToReadableStream", "returnStream", "tee", "pendingCacheEntry", "cloneCacheEntry", "streamA", "streamB", "clonedEntry", "clonePendingCacheEntry", "getNthCacheEntry", "split", "i", "encodeFormData", "formData", "result", "key", "toString", "stringValue", "arrayBuffer", "byteLength", "String", "fromCodePoint", "Uint16Array", "Uint8Array", "createTrackedReadableStream", "wrapAsInvalidDynamicUsageError", "id", "boundArgs<PERSON><PERSON>th", "originalFn", "sharedError", "isPrivate", "cache<PERSON><PERSON><PERSON>", "getCache<PERSON><PERSON><PERSON>", "Error", "captureStackTrace", "slice", "indexOf", "cachedFn", "workUnitStore", "expression", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "getClientReferenceManifestForRsc", "buildId", "hangingInputAbortSignal", "createHangingInputAbortSignal", "isPageOrLayout", "isPageComponent", "params", "outerParams", "searchParams", "outerSearchParams", "props", "_innerParams", "innerSearchParams", "makeErroringSearchParamsForUseCache", "isLayoutComponent", "$$isLayoutComponent", "outerSlots", "innerSlots", "JSON", "stringify", "encryptedBoundArgs", "shift", "boundArgs", "decryptActionBoundArgs", "Array", "isArray", "unshift", "createClientTemporaryReferenceSet", "cacheKeyParts", "encodeCacheKeyParts", "encodeReply", "encodedCacheKeyParts", "dynamicAccessAbortController", "serialized<PERSON>ache<PERSON>ey", "prerenderResumeDataCache", "getPrerenderResumeDataCache", "renderResumeDataCache", "getRenderResumeDataCache", "beginRead", "cachedEntry", "get", "existingEntry", "DYNAMIC_EXPIRE", "RUNTIME_PREFETCH_DYNAMIC_STALE", "allowEmptyStaticShell", "lazyRefreshTags", "refreshTagsByCacheKind", "isResolvedLazyResult", "implicitTagsExpiration", "lazyExpiration", "expirationsByCacheKind", "expiration", "Infinity", "shouldDiscardCacheEntry", "currentTime", "isStaticGeneration", "newStream", "isDraftMode", "savedCacheEntry", "set", "promise", "pendingRevalidateWrites", "entryLeft", "entryRight", "ignoredStream", "cancel", "replayConsoleLogs", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "serverModuleMap", "createFromReadableStream", "React", "ref", "$$isPageComponent", "isOnDemandRevalidate", "dev", "headers", "some", "isRecentlyRevalidatedTag", "previouslyRevalidatedTags", "pendingRevalidatedTags"], "mappings": ";;;;+BAgyBgBA;;;eAAAA;;;wBAzxBT;wBAKA;wBACyC;0CAIf;8CAoB1B;uCAE4B;iCAO5B;4BAGgC;gCACR;oCACY;2BACoB;0BAC/B;gCACK;kCAK9B;8BAIA;8DAEW;4BACqC;mDACb;sCACH;;;;;;AA6CvC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,QAAQH,QAAQC,GAAG,CAACG,wBAAwB,GAC9CC,QAAQF,KAAK,CAACG,IAAI,CAACD,SAAS,gBAC5BE;AAEJ,MAAMC,mBACJR,QAAQC,GAAG,CAACQ,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBJ;AACN,MAAMK,mBACJZ,QAAQC,GAAG,CAACQ,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNG,mBAAmB,GACtBN;AAEN,SAASO,mBACPC,SAAoB,EACpBC,YAA0B,EAC1BC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,gBAAoC;IAEpC,kFAAkF;IAClF,mFAAmF;IACnF,+EAA+E;IAC/E,mFAAmF;IACnF,6EAA6E;IAC7E,OAAOL,UAAUM,kBAAkB,CACjCC,yCACAP,WACAC,cACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASE,wCACPP,SAAoB,EACpBC,YAA0B,EAC1BC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,gBAAoC;IAEpC,2EAA2E;IAC3E,6EAA6E;IAC7E,sFAAsF;IACtF,sFAAsF;IACtF,+EAA+E;IAC/E,sFAAsF;IACtF,0DAA0D;IAC1D,OAAOG,0CAAgB,CAACC,GAAG,CACzBT,WACAU,oCACAV,WACAC,cACAC,yBACAC,kBACAC,IACAC;AAEJ;AAEA,SAASM,oBACPX,SAAoB,EACpBC,YAA0B,EAC1BW,gBAAqC;IAErC,IAAIX,aAAaY,IAAI,KAAK,WAAW;QACnC,MAAMC,qBAAqBb,aAAaa,kBAAkB;QAE1D,OAAO;YACLC,MAAM;YACNC,OAAO;YACPC,YAAY,EAAEH,sCAAAA,mBAAoBG,YAAY;YAC9CC,YAAYN,iBAAiBM,UAAU;YACvCC,QAAQP,iBAAiBO,MAAM;YAC/BC,OAAOR,iBAAiBQ,KAAK;YAC7BC,oBAAoB7B;YACpB8B,gBAAgB9B;YAChB+B,eAAe/B;YACfgC,MAAM;YACNC,gBAAgBC,IAAAA,+CAAiB,EAAC1B,WAAWc;YAC7Ca,cAAcA,IAAAA,0CAAY,EAAC3B,WAAWc;YACtCc,0BAA0BC,IAAAA,yDAA2B,EACnD7B,WACAc;YAEFgB,iBAAiBC,sBAAsB/B,WAAWc;YAClDkB,qBAAqBC,IAAAA,oDAAsB,EAACnB;YAC5CoB,WAAWC,IAAAA,+DAAiC,EAC1CnC,WACAc;YAEFsB,YAAYtB,mBAAmBsB,UAAU;YACzCC,SAASvB,mBAAmBuB,OAAO;QACrC;IACF,OAAO;QACL,IAAIC;QACJ,MAAMxB,qBAAqBb,aAAaa,kBAAkB;QAE1D,IAAIA,oBAAoB;YACtB,OAAQA,sCAAAA,mBAAoBC,IAAI;gBAC9B,KAAK;gBACL,KAAK;gBACL,KAAK;oBACHuB,yBAAyBxB;oBACzB;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF;oBACEA;YACJ;QACF;QAEA,OAAO;YACLC,MAAM;YACNC,OAAO;YACPC,YAAY,EAAEH,sCAAAA,mBAAoBG,YAAY;YAC9CC,YAAYN,iBAAiBM,UAAU;YACvCC,QAAQP,iBAAiBO,MAAM;YAC/BC,OAAOR,iBAAiBQ,KAAK;YAC7BC,oBAAoB7B;YACpB8B,gBAAgB9B;YAChB+B,eAAe/B;YACfgC,MAAM;YACNC,gBACEX,sBAAsBY,IAAAA,+CAAiB,EAAC1B,WAAWc;YACrDa,cAAcW,CAAAA,0CAAAA,uBAAwBX,YAAY,KAAI;YACtDC,wBAAwB,EACtBU,0CAAAA,uBAAwBV,wBAAwB;YAClDE,iBAAiBC,sBAAsB/B,WAAWc;YAClDoB,WACEpB,sBACAqB,IAAAA,+DAAiC,EAACnC,WAAWc;QACjD;IACF;AACF;AAEA,SAASyB,uBACP3B,gBAAuC;IAEvC,IACE,CAACA,oBACDA,iBAAiBM,UAAU,IAAI,QAC/BN,iBAAiBO,MAAM,IAAI,QAC3BP,iBAAiBQ,KAAK,IAAI,MAC1B;QACA,MAAM,qBAEL,CAFK,IAAIoB,8BAAc,CACtB,yDADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEA,SAAS9B,mCACPV,SAAoB,EACpBC,YAA0B,EAC1BC,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,gBAAoC;IAEpC,IAAI,CAACL,UAAUyC,iBAAiB,EAAE;QAChC,MAAM,qBAAkE,CAAlE,IAAID,8BAAc,CAAC,iDAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAiE;IACzE;IACA,MAAM5B,mBAAmBZ,UAAUyC,iBAAiB,CAAC,UAAU;IAC/DF,uBAAuB3B;IAEvB,6CAA6C;IAC7C,MAAM8B,aAAa/B,oBACjBX,WACAC,cACAW;IAGF,OAAO+B,kDAAoB,CAAClC,GAAG,CAACiC,YAAY,IAC1CE,4DAAyB,CAACnC,GAAG,CAC3B;YAAEoC,iBAAiB,IAAIC;QAAkB,GACzCC,wBACA/C,WACAC,cACAyC,YACAxC,yBACAC,kBACAC,IACAC;AAGN;AAEA,SAAS2C,2CACPC,eAAgC,EAChCC,KAAiB;IAEjB,MAAMC,YAAaF,gBAAgBzB,IAAI,KAAK,EAAE;IAE9C,KAAK,MAAM4B,OAAOF,MAAM1B,IAAI,CAAE;QAC5B,IAAI,CAAC2B,UAAUE,QAAQ,CAACD,MAAM;YAC5BD,UAAUG,IAAI,CAACF;QACjB;IACF;IAEA,IAAIH,gBAAgB7B,KAAK,GAAG8B,MAAM9B,KAAK,EAAE;QACvC6B,gBAAgB7B,KAAK,GAAG8B,MAAM9B,KAAK;IACrC;IAEA,IAAI6B,gBAAgB/B,UAAU,GAAGgC,MAAMhC,UAAU,EAAE;QACjD+B,gBAAgB/B,UAAU,GAAGgC,MAAMhC,UAAU;IAC/C;IAEA,IAAI+B,gBAAgB9B,MAAM,GAAG+B,MAAM/B,MAAM,EAAE;QACzC8B,gBAAgB9B,MAAM,GAAG+B,MAAM/B,MAAM;IACvC;AACF;AAEA,SAASoC,0BACPtD,YAA0B,EAC1BiD,KAAiB;IAEjB,IAAIjD,aAAaY,IAAI,KAAK,WAAW;QACnC,OAAQZ,aAAaa,kBAAkB,CAACC,IAAI;YAC1C,KAAK;YACL,KAAK;gBACHiC,2CACE/C,aAAaa,kBAAkB,EAC/BoC;gBAEF;YACF,KAAK;YACL,KAAK1D;gBACH;YACF;gBACES,aAAaa,kBAAkB;QACnC;IACF,OAAO;YACGb;QAAR,QAAQA,mCAAAA,aAAaa,kBAAkB,qBAA/Bb,iCAAiCc,IAAI;YAC3C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACHiC,2CACE/C,aAAaa,kBAAkB,EAC/BoC;gBAEF;YACF,KAAK;YACL,KAAK;YACL,KAAK1D;gBACH;YACF;gBACES,aAAaa,kBAAkB;QACnC;IACF;AACF;AAEA,eAAe0C,cACbC,WAA2B,EAC3BzD,SAAoB,EACpBC,YAA0B,EAC1ByD,eAA8B,EAC9BC,SAAiB,EACjBC,MAAsB;IAEtB,wEAAwE;IACxE,yEAAyE;IACzE,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,oEAAoE;IACpE,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,2EAA2E;IAC3E,cAAc;IAEd,MAAMC,SAAgB,EAAE;IACxB,MAAMC,SAASL,YAAYM,SAAS;IAEpC,IAAI;QACF,IAAK,IAAIb,OAAO,CAAC,AAACA,CAAAA,QAAQ,MAAMY,OAAOE,IAAI,EAAC,EAAGC,IAAI,EAAI;YACrDJ,OAAOP,IAAI,CAACJ,MAAMgB,KAAK;QACzB;IACF,EAAE,OAAOC,OAAO;QACdP,OAAON,IAAI,CAACa;IACd;IAEA,IAAIC,MAAM;IACV,MAAMC,eAAe,IAAIC,eAAe;QACtCC,MAAKC,UAAU;YACb,IAAIxE,UAAUyE,wBAAwB,EAAE;gBACtCD,WAAWL,KAAK,CAACnE,UAAUyE,wBAAwB;YACrD,OAAO,IAAIL,MAAMP,OAAOa,MAAM,EAAE;gBAC9BF,WAAWG,OAAO,CAACd,MAAM,CAACO,MAAM;YAClC,OAAO,IAAIR,OAAOc,MAAM,GAAG,GAAG;gBAC5B,2CAA2C;gBAC3CF,WAAWL,KAAK,CAACP,MAAM,CAAC,EAAE;YAC5B,OAAO;gBACLY,WAAWI,KAAK;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgBnB,gBAAgBlC,IAAI;IAC1C,0EAA0E;IAC1E,4FAA4F;IAC5F,qCAAqC;IACrC,MAAMsD,sBACJpB,gBAAgBrC,kBAAkB,KAAK7B,YACnCkE,gBAAgBrC,kBAAkB,GAClCqC,gBAAgBxC,UAAU;IAChC,MAAM6D,kBACJrB,gBAAgBpC,cAAc,KAAK9B,YAC/BkE,gBAAgBpC,cAAc,GAC9BoC,gBAAgBvC,MAAM;IAC5B,MAAM6D,iBACJtB,gBAAgBnC,aAAa,KAAK/B,YAC9BkE,gBAAgBnC,aAAa,GAC7BmC,gBAAgBtC,KAAK;IAE3B,MAAM8B,QAAoB;QACxBgB,OAAOG;QACPY,WAAWtB;QACXzC,YAAY4D;QACZ3D,QAAQ4D;QACR3D,OAAO4D;QACPxD,MAAMqD,kBAAkB,OAAO,EAAE,GAAGA;IACtC;IAEA,mDAAmD;IACnD,IAAI5E,cAAc;QAChBsD,0BAA0BtD,cAAciD;IAC1C;IAEA,MAAMgC,cAAcjF,aAAaa,kBAAkB,GAC/CqE,IAAAA,4CAAc,EAAClF,aAAaa,kBAAkB,IAC9C;IAEJ,IAAIoE,aAAa;QACfA,YAAYE,OAAO;IACrB;IAEA,OAAOlC;AACT;AAaA,eAAeH,uBACb/C,SAAoB,EACpBC,YAA0B,EAC1ByD,eAA8B,EAC9BxD,uBAAoE,EACpEC,gBAAmC,EACnCC,EAA4C,EAC5CC,gBAAoC;IAEpC,MAAMgF,sBAAsBC,IAAAA,mCAAiC;IAC7D,MAAMxE,qBAAqBb,aAAaa,kBAAkB;IAE1D,MAAM,KAAKyE,KAAK,GACd,OAAOpF,qBAAqB,WACxB,MAAMqF,IAAAA,mBAAW,EACfrF,kBACAsF,IAAAA,mCAAkB,KAClB;QAAEJ;IAAoB,KAExB,MAAMK,IAAAA,oCAA4B,EAChC;QACE,OAAO,CAACC,OAAOC,aAAa,CAAC;YAC3B,KAAK,MAAM1C,SAAS/C,iBAAkB;gBACpC,MAAM+C;YACR;YAEA,IAAIpC,oBAAoB;gBACtB,OAAQA,mBAAmBC,IAAI;oBAC7B,KAAK;oBACL,KAAK;wBACH,2DAA2D;wBAC3D,4DAA4D;wBAC5D,yDAAyD;wBACzD,wDAAwD;wBACxD,aAAa;wBACb,MAAM,IAAI8E,QAAc,CAACC;4BACvB,IAAIhF,mBAAmBiF,YAAY,CAACC,OAAO,EAAE;gCAC3CF;4BACF,OAAO;gCACLhF,mBAAmBiF,YAAY,CAACE,gBAAgB,CAC9C,SACA,IAAMH,WACN;oCAAEI,MAAM;gCAAK;4BAEjB;wBACF;wBACA;oBACF,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH;oBACF;wBACEpF;gBACJ;YACF;QACF;IACF,GACA2E,IAAAA,mCAAkB,KAClB;QAAEJ;IAAoB;IAG9B,4DAA4D;IAC5D,MAAM1B,YAAYwC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;IAE1D,0EAA0E;IAC1E,6EAA6E;IAC7E,6EAA6E;IAC7E,iDAAiD;IACjD,MAAMC,gBAAgBC,IAAAA,4BAAgB,EAAC,IAAMnG,GAAGoG,KAAK,CAAC,MAAMjB;IAE5D,IAAI3B,SAAyB,EAAE;IAE/B,uEAAuE;IACvE,uEAAuE;IACvE,6EAA6E;IAC7E,+BAA+B;IAC/B,MAAM6C,cAAc,CAACtC;QACnB,MAAMuC,SAASC,IAAAA,8CAA0B,EAACxC;QAE1C,IAAIuC,QAAQ;YACV,OAAOA;QACT;QAEA,IAAIE,IAAAA,4CAAsB,EAACzC,QAAQ;YACjC,kBAAkB;YAClB7E,QAAQ6E,KAAK,CAACA;YACd,OAAO3E;QACT;QAEA,IAAIP,QAAQC,GAAG,CAACQ,QAAQ,KAAK,eAAe;YAC1C,gEAAgE;YAChE,oEAAoE;YACpE,6DAA6D;YAC7DJ,QAAQ6E,KAAK,CAACA;QAChB;QAEAP,OAAON,IAAI,CAACa;IACd;IAEA,IAAI0C;IAEJ,OAAQ/F,sCAAAA,mBAAoBC,IAAI;QAC9B,KAAK;QACL,KAAK;gBAgBD6B;YAfF,MAAMkE,yBAAyB,IAAIhE;YAEnC,uEAAuE;YACvE,0EAA0E;YAC1E,2DAA2D;YAC3D,MAAMiE,QAAQC,WAAW;gBACvB,MAAM7C,QAAQ,IAAI8C,oCAAoB;gBACtC,IAAI5G,kBAAkB;oBACpB8D,MAAM+C,KAAK,GAAG/C,MAAMgD,IAAI,GAAG,OAAOhD,MAAMiD,OAAO,GAAG/G;gBACpD;gBACAL,UAAUyE,wBAAwB,GAAGN;gBACrC2C,uBAAuBO,KAAK,CAAClD;YAC/B,GAAG;YAEH,MAAMmD,4BACJ1E,sCAAAA,4DAAyB,CAAC2E,QAAQ,uBAAlC3E,oCAAsCC,eAAe,CAAC2E,MAAM;YAE9D,MAAMC,cAAcH,2BAChBI,YAAYC,GAAG,CAAC;gBACdL;gBACAxG,mBAAmBiF,YAAY;gBAC/Be,uBAAuBU,MAAM;aAC9B,IACDV,uBAAuBU,MAAM;YAEjC,MAAM,EAAEI,OAAO,EAAE,GAAG,MAAMC,IAAAA,0BAAS,EACjCvB,eACApG,wBAAwB4H,aAAa,EACrC;gBACEC,iBAAiB;gBACjBtI;gBACA+H,QAAQC;gBACRpC;gBACA2C,SAAQ7D,KAAK;oBACX,IAAIsD,YAAYzB,OAAO,IAAIyB,YAAYQ,MAAM,KAAK9D,OAAO;wBACvD,OAAO3E;oBACT;oBAEA,OAAOiH,YAAYtC;gBACrB;YACF;YAGF+D,aAAanB;YAEb,IAAID,uBAAuBU,MAAM,CAACxB,OAAO,EAAE;gBACzC,mEAAmE;gBACnE,uEAAuE;gBACvE,wEAAwE;gBACxE,sEAAsE;gBACtE,4DAA4D;gBAC5D,sBAAsB;gBACtBa,SAAS,IAAIvC,eAAe;oBAC1B6D,OAAM3D,UAAU;wBACdA,WAAWL,KAAK,CAAC2C,uBAAuBU,MAAM,CAACS,MAAM;oBACvD;gBACF;YACF,OAAO,IAAIX,4CAAAA,yBAA0BtB,OAAO,EAAE;gBAC5C,sEAAsE;gBACtE,wEAAwE;gBACxE,oCAAoC;gBACpC,MAAMoC,iBAAiBC,IAAAA,yCAAkB,EACvCvH,mBAAmBiF,YAAY,EAC/B/F,UAAUsI,KAAK,EACfb,YAAYQ,MAAM;gBAGpB,IAAInH,mBAAmBoE,WAAW,EAAE;oBAClCpE,mBAAmBoE,WAAW,CAACE,OAAO;gBACxC;gBAEA,OAAO;oBAAErE,MAAM;oBAAqBqH;gBAAe;YACrD,OAAO;gBACLvB,SAASe;YACX;YACA;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAKpI;YACHqH,SAAS0B,IAAAA,8BAAsB,EAC7BjC,eACApG,wBAAwB4H,aAAa,EACrC;gBACEC,iBAAiB;gBACjBtI;gBACA4F;gBACA2C,SAASvB;YACX;YAEF;QACF;YACE,OAAO3F;IACX;IAEA,MAAM,CAAC0H,cAAc/E,YAAY,GAAGoD,OAAO4B,GAAG;IAE9C,MAAMC,oBAAoBlF,cACxBC,aACAzD,WACAC,cACAyD,iBACAC,WACAC;IAGF,OAAO;QACL7C,MAAM;QACN,wEAAwE;QACxE,mEAAmE;QACnE,qCAAqC;QACrC8F,QAAQ2B;QACRE;IACF;AACF;AAEA,SAASC,gBAAgBzF,KAAiB;IACxC,MAAM,CAAC0F,SAASC,QAAQ,GAAG3F,MAAMgB,KAAK,CAACuE,GAAG;IAC1CvF,MAAMgB,KAAK,GAAG0E;IACd,MAAME,cAA0B;QAC9B5E,OAAO2E;QACP5D,WAAW/B,MAAM+B,SAAS;QAC1B/D,YAAYgC,MAAMhC,UAAU;QAC5BC,QAAQ+B,MAAM/B,MAAM;QACpBC,OAAO8B,MAAM9B,KAAK;QAClBI,MAAM0B,MAAM1B,IAAI;IAClB;IACA,OAAO;QAAC0B;QAAO4F;KAAY;AAC7B;AAEA,eAAeC,uBACbL,iBAAsC;IAEtC,MAAMxF,QAAQ,MAAMwF;IACpB,OAAOC,gBAAgBzF;AACzB;AAEA,eAAe8F,iBACbC,KAAwC,EACxCC,CAAS;IAET,OAAO,AAAC,CAAA,MAAMD,KAAI,CAAE,CAACC,EAAE;AACzB;AAEA,eAAeC,eAAeC,QAAkB;IAC9C,IAAIC,SAAS;IACb,KAAK,IAAI,CAACC,KAAKpF,MAAM,IAAIkF,SAAU;QACjC,6FAA6F;QAC7F,+FAA+F;QAC/F,6FAA6F;QAC7F,0FAA0F;QAC1F,uBAAuB;QACvBC,UAAUC,IAAI5E,MAAM,CAAC6E,QAAQ,CAAC,MAAM,MAAMD;QAC1C,IAAIE;QACJ,IAAI,OAAOtF,UAAU,UAAU;YAC7BsF,cAActF;QAChB,OAAO;YACL,+EAA+E;YAC/E,+EAA+E;YAC/E,8CAA8C;YAC9C,MAAMuF,cAAc,MAAMvF,MAAMuF,WAAW;YAC3C,IAAIA,YAAYC,UAAU,GAAG,MAAM,GAAG;gBACpCF,cAAcG,OAAOC,aAAa,IAAI,IAAIC,YAAYJ;YACxD,OAAO;gBACLD,cACEG,OAAOC,aAAa,IACf,IAAIC,YAAYJ,aAAa,GAAG,AAACA,CAAAA,YAAYC,UAAU,GAAG,CAAA,IAAK,MAEpEC,OAAOC,aAAa,CAClB,IAAIE,WAAWL,aAAaA,YAAYC,UAAU,GAAG,GAAG,EAAE,CAAC,EAAE;YAEnE;QACF;QACAL,UAAUG,YAAY9E,MAAM,CAAC6E,QAAQ,CAAC,MAAM,MAAMC;IACpD;IACA,OAAOH;AACT;AAEA,SAASU,4BACPlD,MAAsB,EACtB3B,WAAwB;IAExB,MAAMpB,SAAS+C,OAAO9C,SAAS;IAC/B,OAAO,IAAIO,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAM,EAAEP,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOE,IAAI;YACzC,IAAIC,MAAM;gBACRO,WAAWI,KAAK;gBAChBM,YAAYE,OAAO;YACrB,OAAO;gBACLZ,WAAWG,OAAO,CAACT;YACrB;QACF;IACF;AACF;AAEA,SAAS8F,+BACP7F,KAAY,EACZ9D,gBAAoC,EACpCL,SAAoB;IAEpB,IAAIK,kBAAkB;QACpB8D,MAAM+C,KAAK,GAAG/C,MAAMgD,IAAI,GAAG,OAAOhD,MAAMiD,OAAO,GAAG/G;IACpD;IAEAL,UAAUyE,wBAAwB,KAAKN;IAEvC,OAAOA;AACT;AAEO,SAASpF,MACd8B,IAAY,EACZoJ,EAAU,EACVC,eAAuB,EACvBC,UAAoD;QAe3BC;IAbzB,MAAMC,YAAYxJ,SAAS;IAE3B,2EAA2E;IAC3E,6BAA6B;IAC7B,MAAMyJ,eAAeD,YAAY7K,YAAY+K,IAAAA,yBAAe,EAAC1J;IAE7D,IAAI,CAACwJ,aAAa,CAACC,cAAc;QAC/B,MAAM,qBAA2C,CAA3C,IAAIE,MAAM,4BAA4B3J,OAAtC,qBAAA;mBAAA;wBAAA;0BAAA;QAA0C;IAClD;IAEA,8CAA8C;IAC9C,MAAMuJ,cAAc,IAAII;IACxBA,MAAMC,iBAAiB,CAACL,aAAarL;IACrC,MAAMsB,oBAAmB+J,qBAAAA,YAAYlD,KAAK,qBAAjBkD,mBAAmBM,KAAK,CAC/CN,YAAYlD,KAAK,CAACyD,OAAO,CAAC;IAG5B,MAAMxD,OAAOgD,WAAWhD,IAAI;IAC5B,MAAMyD,WAAW;QACf,CAACzD,KAAK,EAAE,eAAgB,GAAG5B,IAAW;YACpC,MAAMvF,YAAYQ,0CAAgB,CAAC+G,QAAQ;YAC3C,IAAIvH,cAAcR,WAAW;gBAC3B,MAAM,qBAEL,CAFK,IAAIgL,MACR,4EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIpK,KAAK+J;YAET,MAAMU,gBAAgBlI,kDAAoB,CAAC4E,QAAQ;YAEnD,IAAItH;YAEJ,IAAIoK,WAAW;gBACb,MAAMS,aAAa;gBAEnB,OAAQD,iCAAAA,cAAe9J,IAAI;oBACzB,4DAA4D;oBAC5D,KAAK;wBACH,OAAOsH,IAAAA,yCAAkB,EACvBwC,cAAc9E,YAAY,EAC1B/F,UAAUsI,KAAK,EACfwC;oBAEJ,KAAK;wBACH,OAAOC,IAAAA,sCAAoB,EACzB/K,UAAUsI,KAAK,EACfwC,YACAD,cAAcG,eAAe;oBAEjC,KAAK;wBACH,OAAOC,IAAAA,kDAAgC,EACrCH,YACA9K,WACA6K;oBAEJ,KAAK;wBACH,MAAM,qBAEL,CAFK,IAAIrI,8BAAc,CACtB,GAAGsI,WAAW,0EAA0E,EAAEA,WAAW,8EAA8E,CAAC,GADhL,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF,KAAK;wBAAkB;4BACrB,MAAMd,+BACJ,qBAGC,CAHD,IAAIQ,MACF,oEAAoE;4BACpE,GAAGM,WAAW,8CAA8C,CAAC,GAF/D,qBAAA;uCAAA;4CAAA;8CAAA;4BAGA,IACAzK,kBACAL;wBAEJ;oBACA,KAAK;wBAAS;4BACZ,MAAMgK,+BACJ,qBAGC,CAHD,IAAIQ,MACF,oEAAoE;4BACpE,GAAGM,WAAW,8EAA8E,EAAEA,WAAW,CAAC,CAAC,GAF7G,qBAAA;uCAAA;4CAAA;8CAAA;4BAGA,IACAzK,kBACAL;wBAEJ;oBACA,KAAK;oBACL,KAAK;oBACL,KAAK;wBACHC,eAAe;4BACbY,MAAM;4BACNC,oBAAoB+J;wBACtB;wBACA;oBACF,KAAKrL;wBACH,MAAMwK,+BACJ,qBAGC,CAHD,IAAIQ,MACF,oEAAoE;wBACpE,GAAGM,WAAW,6CAA6C,CAAC,GAF9D,qBAAA;mCAAA;wCAAA;0CAAA;wBAGA,IACAzK,kBACAL;oBAEJ;wBACE6K;wBACA,oEAAoE;wBACpE,+DAA+D;wBAC/D,MAAM,qBAAiD,CAAjD,IAAIrI,8BAAc,CAAC,CAAC,2BAA2B,CAAC,GAAhD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAgD;gBAC1D;YACF,OAAO;gBACL,OAAQqI,iCAAAA,cAAe9J,IAAI;oBACzB,KAAK;wBACH,MAAM+J,aAAa;wBACnB,MAAM,qBAEL,CAFK,IAAItI,8BAAc,CACtB,GAAGsI,WAAW,0EAA0E,EAAEA,WAAW,8EAA8E,CAAC,GADhL,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,6DAA6D;oBAC7D,gCAAgC;oBAChC,KAAK;oBACL,KAAKtL;wBACHS,eAAe;4BACbY,MAAM;4BACNC,oBAAoB+J;wBACtB;wBACA;oBACF;wBACEA;wBACA,oEAAoE;wBACpE,+DAA+D;wBAC/D,MAAM,qBAAiD,CAAjD,IAAIrI,8BAAc,CAAC,CAAC,2BAA2B,CAAC,GAAhD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAgD;gBAC1D;YACF;YAEA,0EAA0E;YAC1E,sFAAsF;YACtF,MAAMtC,0BAA0BgL,IAAAA,iDAAgC;YAEhE,qFAAqF;YACrF,wFAAwF;YACxF,qFAAqF;YACrF,sBAAsB;YACtB,MAAMC,UAAUnL,UAAUmL,OAAO;YAEjC,sEAAsE;YACtE,wEAAwE;YACxE,wEAAwE;YACxE,iEAAiE;YACjE,uEAAuE;YACvE,MAAM1J,iBACJoJ,iBAAiBnJ,IAAAA,+CAAiB,EAAC1B,WAAW6K;YAEhD,MAAMO,0BAA0BP,gBAC5BQ,IAAAA,+CAA6B,EAACR,iBAC9BrL;YAEJ,mGAAmG;YACnG,oFAAoF;YACpF,IAAIS,aAAaY,IAAI,KAAK,WAAW;gBACnC,MAAMmB,sBAAsBC,IAAAA,oDAAsB,EAChDhC,aAAaa,kBAAkB;gBAEjC,IAAIkB,qBAAqB;oBACvB,MAAMA;gBACR;YACF;YAEA,IAAIsJ,iBAAiB;YAErB,qEAAqE;YACrE,yEAAyE;YACzE,uEAAuE;YACvE,uEAAuE;YACvE,yEAAyE;YACzE,sEAAsE;YACtE,oEAAoE;YACpE,0EAA0E;YAC1E,qCAAqC;YACrC,IAAIC,gBAAgBhG,OAAO;gBACzB+F,iBAAiB;gBAEjB,MAAM,CAAC,EAAEE,QAAQC,WAAW,EAAEC,cAAcC,iBAAiB,EAAE,CAAC,GAAGpG;gBAEnE,MAAMqG,QAAyC;oBAC7CJ,QAAQC;gBAEV;gBAEA,IAAIpB,WAAW;oBACb,mEAAmE;oBACnE,iEAAiE;oBACjEuB,MAAMF,YAAY,GAAGC;gBACvB;gBAEApG,OAAO;oBAACqG;iBAAM;gBAEdxL,KAAK,CAAA;oBACH,CAAC+G,KAAK,EAAE,OAAO,EACbqE,QAAQK,YAAY,EACpBH,cAAcI,iBAAiB,EACC,GAChC3B,WAAW3D,KAAK,CAAC,MAAM;4BACrB;gCACEgF,QAAQC;gCACRC,cACEI,qBACA,8DAA8D;gCAC9D,4DAA4D;gCAC5D,wDAAwD;gCACxD,8DAA8D;gCAC9D,8DAA8D;gCAC9D,6DAA6D;gCAC7D,yCAAyC;gCACzCC,IAAAA,iDAAmC,EAAC/L;4BACxC;yBACD;gBACL,CAAA,CAAC,CAACmH,KAAK;YACT,OAAO,IAAI6E,kBAAkBzG,OAAO;gBAClC+F,iBAAiB;gBAEjB,MAAM,CAAC,EAAEE,QAAQC,WAAW,EAAEQ,mBAAmB,EAAE,GAAGC,YAAY,CAAC,GACjE3G;gBACF,mDAAmD;gBACnDA,OAAO;oBAAC;wBAAEiG,QAAQC;wBAAa,GAAGS,UAAU;oBAAC;iBAAE;gBAE/C9L,KAAK,CAAA;oBACH,CAAC+G,KAAK,EAAE,OAAO,EACbqE,QAAQK,YAAY,EACpB,GAAGM,YACuD,GAC1DhC,WAAW3D,KAAK,CAAC,MAAM;4BAAC;gCAAEgF,QAAQC;gCAAa,GAAGU,UAAU;4BAAC;yBAAE;gBACnE,CAAA,CAAC,CAAChF,KAAK;YACT;YAEA,IAAI+C,kBAAkB,GAAG;gBACvB,IAAI3E,KAAKb,MAAM,KAAK,GAAG;oBACrB,MAAM,qBAEL,CAFK,IAAIlC,8BAAc,CACtB,CAAC,kCAAkC,EAAE4J,KAAKC,SAAS,CAACjM,GAAG+G,IAAI,EAAE,gEAAgE,CAAC,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMmF,qBAAqB/G,KAAKgH,KAAK;gBACrC,MAAMC,YAAY,MAAMC,IAAAA,kCAAsB,EAACxC,IAAIqC;gBAEnD,IAAI,CAACI,MAAMC,OAAO,CAACH,YAAY;oBAC7B,MAAM,qBAEL,CAFK,IAAIhK,8BAAc,CACtB,CAAC,qDAAqD,EAAE4J,KAAKC,SAAS,CAACjM,GAAG+G,IAAI,EAAE,mCAAmC,EAAE,OAAOqF,UAAU,SAAS,CAAC,GAD5I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAItC,oBAAoBsC,UAAU9H,MAAM,EAAE;oBACxC,MAAM,qBAEL,CAFK,IAAIlC,8BAAc,CACtB,CAAC,kCAAkC,EAAE4J,KAAKC,SAAS,CAACjM,GAAG+G,IAAI,EAAE,YAAY,EAAE+C,gBAAgB,sBAAsB,EAAEsC,UAAU9H,MAAM,CAAC,SAAS,CAAC,GAD1I,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEAa,KAAKqH,OAAO,CAACJ;YACf;YAEA,MAAMnH,sBAAsBwH,IAAAA,mCAAiC;YAE7D,wEAAwE;YACxE,yEAAyE;YACzE,0EAA0E;YAC1E,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,sEAAsE;YACtE,2BAA2B;YAC3B,MAAMC,gBAA+BrL,iBACjC;gBAAC0J;gBAASlB;gBAAI1E;gBAAM9D;aAAe,GACnC;gBAAC0J;gBAASlB;gBAAI1E;aAAK;YAEvB,MAAMwH,sBAAsB,IAC1BC,IAAAA,mBAAW,EAACF,eAAe;oBACzBzH;oBACAmC,QAAQ4D;gBACV;YAEF,IAAI6B;YAEJ,OAAQpC,iCAAAA,cAAe9J,IAAI;gBACzB,KAAK;gBACL,qEAAqE;gBACrE,8EAA8E;gBAC9E,iDAAiD;gBACjD,sFAAsF;gBACtF,uDAAuD;gBACvD,gEAAgE;gBAChE,EAAE;gBACF,cAAc;gBACd,KAAK;oBACH,IAAI,CAACuK,gBAAgB;wBACnB,oEAAoE;wBACpE,+DAA+D;wBAC/D,+DAA+D;wBAC/D,mEAAmE;wBACnE,oEAAoE;wBACpE,mEAAmE;wBACnE,6BAA6B;wBAC7B,MAAM4B,+BAA+B,IAAIpK;wBAEzCmK,uBAAuB,MAAMrK,4DAAyB,CAACnC,GAAG,CACxD;4BAAEoC,iBAAiBqK;wBAA6B,GAChDH;wBAGF,IAAIG,6BAA6B1F,MAAM,CAACxB,OAAO,EAAE;4BAC/C,OAAOqC,IAAAA,yCAAkB,EACvBwC,cAAc9E,YAAY,EAC1B/F,UAAUsI,KAAK,EACf4E,6BAA6B1F,MAAM,CAACS,MAAM,CAACb,OAAO;wBAEtD;wBACA;oBACF;gBACF,cAAc;gBACd,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK5H;oBACHyN,uBAAuB,MAAMF;oBAC7B;gBACF;oBACE,OAAOlC;YACX;YAEA,MAAMsC,qBACJ,OAAOF,yBAAyB,WAE5B,+CAA+C;YAC/CA,uBACA,MAAM9D,eAAe8D;YAE3B,IAAIpG,SAAqCrH;YAEzC,kEAAkE;YAClE,MAAM4N,2BAA2BvC,gBAC7BwC,IAAAA,yDAA2B,EAACxC,iBAC5B;YACJ,MAAMyC,wBAAwBzC,gBAC1B0C,IAAAA,sDAAwB,EAAC1C,iBACzB;YAEJ,IAAIyC,uBAAuB;gBACzB,MAAMpI,cAAc2F,gBAAgB1F,IAAAA,4CAAc,EAAC0F,iBAAiB;gBAEpE,IAAI3F,aAAa;oBACfA,YAAYsI,SAAS;gBACvB;gBACA,MAAMC,cAAcH,sBAAsBvO,KAAK,CAAC2O,GAAG,CAACP;gBACpD,IAAIM,gBAAgBjO,WAAW;oBAC7B,MAAMmO,gBAAgB,MAAMF;oBAC5BlK,0BAA0BtD,cAAc0N;oBAExC,IAAI9C,kBAAkBrL,aAAamO,kBAAkBnO,WAAW;wBAC9D,IACEmO,cAAczM,UAAU,KAAK,KAC7ByM,cAAcxM,MAAM,GAAGyM,yBAAc,EACrC;4BACA,OAAQ/C,cAAc9J,IAAI;gCACxB,KAAK;oCACH,qDAAqD;oCACrD,+DAA+D;oCAC/D,yDAAyD;oCACzD,gEAAgE;oCAChE,gEAAgE;oCAChE,gBAAgB;oCAChB,IAAImE,aAAa;wCACfA,YAAYE,OAAO;oCACrB;oCACA,OAAOiD,IAAAA,yCAAkB,EACvBwC,cAAc9E,YAAY,EAC1B/F,UAAUsI,KAAK,EACf;gCAEJ,KAAK;oCAAqB;wCACxB,mGAAmG;wCACnG,mDAAmD;wCACnD,IAAIuC,cAAc7I,mBAAmB,EAAE;4CACrC,MAAM6I,cAAc7I,mBAAmB;wCACzC;wCACA;oCACF;gCACA,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;oCACH;gCACF;oCACE6I;4BACJ;wBACF;wBAEA,IAAI8C,cAAcvM,KAAK,GAAGyM,yCAA8B,EAAE;4BACxD,OAAQhD,cAAc9J,IAAI;gCACxB,KAAK;oCACH,wFAAwF;oCACxF,sEAAsE;oCACtE,oEAAoE;oCACpE,mCAAmC;oCACnC,IAAImE,aAAa;wCACfA,YAAYE,OAAO;oCACrB;oCACA,OAAOiD,IAAAA,yCAAkB,EACvBwC,cAAc9E,YAAY,EAC1B/F,UAAUsI,KAAK,EACf;gCAEJ,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;oCACH;gCACF;oCACEuC;4BACJ;wBACF;oBACF;oBAEA,MAAM,CAACjC,SAASC,QAAQ,GAAG8E,cAAczJ,KAAK,CAACuE,GAAG;oBAClDkF,cAAczJ,KAAK,GAAG2E;oBAEtB,IAAI3D,aAAa;wBACf,mEAAmE;wBACnE,gCAAgC;wBAChC2B,SAASkD,4BAA4BnB,SAAS1D;oBAChD,OAAO;wBACL2B,SAAS+B;oBACX;gBACF,OAAO;oBACL,IAAI1D,aAAa;wBACfA,YAAYE,OAAO;oBACrB;oBAEA,IAAIyF,eAAe;wBACjB,OAAQA,cAAc9J,IAAI;4BACxB,KAAK;gCACH,2DAA2D;gCAC3D,+DAA+D;gCAC/D,+DAA+D;gCAC/D,8DAA8D;gCAC9D,0DAA0D;gCAC1D,6DAA6D;gCAC7D,wDAAwD;gCACxD,0DAA0D;gCAC1D,8DAA8D;gCAC9D,8DAA8D;gCAC9D,gEAAgE;gCAChE,gEAAgE;gCAChE,yDAAyD;gCACzD,+DAA+D;gCAC/D,+DAA+D;gCAC/D,IAAI8J,cAAciD,qBAAqB,EAAE;oCACvC,OAAOzF,IAAAA,yCAAkB,EACvBwC,cAAc9E,YAAY,EAC1B/F,UAAUsI,KAAK,EACf;gCAEJ;gCACA;4BACF,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;4BACL,KAAK;gCACH;4BACF;gCACEuC;wBACJ;oBACF;gBACF;YACF;YAEA,IAAIhE,WAAWrH,WAAW;gBACxB,MAAM0F,cAAc2F,gBAAgB1F,IAAAA,4CAAc,EAAC0F,iBAAiB;gBACpE,IAAI3F,aAAa;oBACf,6EAA6E;oBAC7E,2DAA2D;oBAC3DA,YAAYsI,SAAS;gBACvB;gBAEA,MAAMO,kBAAkB/N,UAAUgO,sBAAsB,CAACN,GAAG,CAAC7M;gBAE7D,IAAIkN,mBAAmB,CAACE,IAAAA,gCAAoB,EAACF,kBAAkB;oBAC7D,MAAMA;gBACR;gBAEA,IAAI7K;gBAEJ,4DAA4D;gBAC5D,IAAIoH,gBAAgB,CAACvI,sBAAsB/B,WAAW6K,gBAAgB;wBAGlEA;oBAFF3H,QAAQ,MAAMoH,aAAaoD,GAAG,CAC5BP,oBACAtC,CAAAA,kCAAAA,8BAAAA,cAAe5J,YAAY,qBAA3B4J,4BAA6BrJ,IAAI,KAAI,EAAE;gBAE3C;gBAEA,IAAI0B,OAAO;wBACY2H;oBAArB,MAAM5J,eAAe4J,CAAAA,kCAAAA,+BAAAA,cAAe5J,YAAY,qBAA3B4J,6BAA6BrJ,IAAI,KAAI,EAAE;oBAC5D,IAAI0M,yBAAyB;oBAE7B,IAAIrD,iCAAAA,cAAe5J,YAAY,EAAE;wBAC/B,MAAMkN,iBACJtD,cAAc5J,YAAY,CAACmN,sBAAsB,CAACV,GAAG,CAAC7M;wBAExD,IAAIsN,gBAAgB;4BAClB,MAAME,aAAaJ,IAAAA,gCAAoB,EAACE,kBACpCA,eAAejK,KAAK,GACpB,MAAMiK;4BAEV,gEAAgE;4BAChE,gEAAgE;4BAChE,gEAAgE;4BAChE,iEAAiE;4BACjE,iEAAiE;4BACjE,IAAIE,aAAaC,UAAU;gCACzBJ,yBAAyBG;4BAC3B;wBACF;oBACF;oBAEA,IACEE,wBACErL,OACAlD,WACA6K,eACA5J,cACAiN,yBAEF;wBACA9O,yBAAAA,MAAQ,0BAA0B+N;wBAClCjK,QAAQ1D;oBACV;gBACF;gBAEA,MAAMgP,cAAcrI,YAAYC,UAAU,GAAGD,YAAYE,GAAG;gBAC5D,IACEwE,kBAAkBrL,aAClB0D,UAAU1D,aACT0D,CAAAA,MAAMhC,UAAU,KAAK,KAAKgC,MAAM/B,MAAM,GAAGyM,yBAAc,AAAD,GACvD;oBACA,OAAQ/C,cAAc9J,IAAI;wBACxB,KAAK;4BACH,iEAAiE;4BACjE,+DAA+D;4BAC/D,+DAA+D;4BAC/D,gEAAgE;4BAChE,4DAA4D;4BAC5D,IAAImE,aAAa;gCACfA,YAAYE,OAAO;4BACrB;4BACA,OAAOiD,IAAAA,yCAAkB,EACvBwC,cAAc9E,YAAY,EAC1B/F,UAAUsI,KAAK,EACf;wBAEJ,KAAK;4BACH,mGAAmG;4BACnG,mDAAmD;4BACnD,IAAIuC,cAAc7I,mBAAmB,EAAE;gCACrC,MAAM6I,cAAc7I,mBAAmB;4BACzC;4BACA;wBACF,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH;wBACF;4BACE6I;oBACJ;gBACF;gBAEA,IACE3H,UAAU1D,aACVgP,cAActL,MAAM+B,SAAS,GAAG/B,MAAM/B,MAAM,GAAG,QAC9CnB,UAAUyO,kBAAkB,IAC3BD,cAActL,MAAM+B,SAAS,GAAG/B,MAAMhC,UAAU,GAAG,MACrD;oBACA,+BAA+B;oBAE/B,+EAA+E;oBAC/E,+EAA+E;oBAC/E,4EAA4E;oBAE5E,kFAAkF;oBAClF,mFAAmF;oBACnF,+EAA+E;oBAC/E,mFAAmF;oBACnF,6EAA6E;oBAE7E,IAAIgC,OAAO;wBACT,IAAIsL,cAActL,MAAM+B,SAAS,GAAG/B,MAAM/B,MAAM,GAAG,MAAM;4BACvD/B,yBAAAA,MAAQ,oBAAoB+N;wBAC9B;wBAEA,IACEnN,UAAUyO,kBAAkB,IAC5BD,cAActL,MAAM+B,SAAS,GAAG/B,MAAMhC,UAAU,GAAG,MACnD;4BACA9B,yBAAAA,MAAQ,qCAAqC+N;wBAC/C;oBACF;oBAEA,MAAM9D,SAAS,MAAMtJ,mBACnBC,WACAC,cACAC,yBACA+M,sBACA7M,IACAC;oBAGF,IAAIgJ,OAAOtI,IAAI,KAAK,qBAAqB;wBACvC,OAAOsI,OAAOjB,cAAc;oBAC9B;oBAEA,MAAM,EAAEvB,QAAQ6H,SAAS,EAAEhG,iBAAiB,EAAE,GAAGW;oBAEjD,gEAAgE;oBAChE,IAAI,CAACrJ,UAAU2O,WAAW,EAAE;wBAC1B,IAAIC;wBAEJ,IAAIxB,0BAA0B;4BAC5B,8DAA8D;4BAC9D,MAAMnE,QAAQF,uBAAuBL;4BACrCkG,kBAAkB5F,iBAAiBC,OAAO;4BAC1CmE,yBAAyBrO,KAAK,CAAC8P,GAAG,CAChC1B,oBACAnE,iBAAiBC,OAAO;wBAE5B,OAAO;4BACL2F,kBAAkBlG;wBACpB;wBAEA,IAAI4B,cAAc;4BAChB,MAAMwE,UAAUxE,aAAauE,GAAG,CAC9B1B,oBACAyB;4BAGF5O,UAAU+O,uBAAuB,KAAK,EAAE;4BACxC/O,UAAU+O,uBAAuB,CAACzL,IAAI,CAACwL;wBACzC;oBACF;oBAEAjI,SAAS6H;gBACX,OAAO;oBACL,mEAAmE;oBACnE,SAAS;oBACT,IAAIzO,aAAaY,IAAI,KAAK,WAAW;wBACnC,MAAM,qBAEL,CAFK,IAAI2B,8BAAc,CACtB,CAAC,mEAAmE,CAAC,GADjE,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAe,0BAA0BtD,cAAciD;oBAExC,qDAAqD;oBACrD2D,SAAS3D,MAAMgB,KAAK;oBAEpB,qEAAqE;oBACrE,yBAAyB;oBACzB,IAAIkJ,0BAA0B;wBAC5B,MAAM,CAAC4B,WAAWC,WAAW,GAAGtG,gBAAgBzF;wBAChD,IAAIgC,aAAa;4BACf2B,SAASkD,4BAA4BiF,UAAU9K,KAAK,EAAEgB;wBACxD,OAAO;4BACL2B,SAASmI,UAAU9K,KAAK;wBAC1B;wBAEAkJ,yBAAyBrO,KAAK,CAAC8P,GAAG,CAChC1B,oBACAtH,QAAQC,OAAO,CAACmJ;oBAEpB,OAAO;wBACL,kEAAkE;wBAClE,wEAAwE;wBACxE,kCAAkC;wBAClC/J,+BAAAA,YAAaE,OAAO;oBACtB;oBAEA,IAAIoJ,cAActL,MAAM+B,SAAS,GAAG/B,MAAMhC,UAAU,GAAG,MAAM;wBAC3D,+DAA+D;wBAC/D,iEAAiE;wBACjE,qBAAqB;wBACrB,MAAMmI,SAAS,MAAMtJ,mBACnBC,WACA,uDAAuD;wBACvD;4BAAEa,MAAMZ,aAAaY,IAAI;4BAAEC,oBAAoBtB;wBAAU,GACzDU,yBACA+M,sBACA7M,IACAC;wBAGF,IAAIgJ,OAAOtI,IAAI,KAAK,UAAU;4BAC5B,MAAM,EAAE8F,QAAQqI,aAAa,EAAExG,iBAAiB,EAAE,GAAGW;4BACrD,IAAIuF;4BAEJ,IAAIxB,0BAA0B;gCAC5B,MAAMnE,QAAQF,uBAAuBL;gCACrCkG,kBAAkB5F,iBAAiBC,OAAO;gCAC1CmE,yBAAyBrO,KAAK,CAAC8P,GAAG,CAChC1B,oBACAnE,iBAAiBC,OAAO;4BAE5B,OAAO;gCACL2F,kBAAkBlG;4BACpB;4BAEA,IAAI4B,cAAc;gCAChB,MAAMwE,UAAUxE,aAAauE,GAAG,CAC9B1B,oBACAyB;gCAGF5O,UAAU+O,uBAAuB,KAAK,EAAE;gCACxC/O,UAAU+O,uBAAuB,CAACzL,IAAI,CAACwL;4BACzC;4BAEA,MAAMI,cAAcC,MAAM;wBAC5B;oBACF;gBACF;YACF;YAEA,yFAAyF;YACzF,0FAA0F;YAC1F,wFAAwF;YACxF,uFAAuF;YACvF,qFAAqF;YACrF,uFAAuF;YACvF,iFAAiF;YACjF,MAAMC,oBAAoB;YAE1B,MAAMC,yBAAyB;gBAC7B,2FAA2F;gBAC3F,yFAAyF;gBACzF,+CAA+C;gBAC/CC,eAAe;gBACfC,WAAWvQ,gBACPkB,wBAAwBsP,oBAAoB,GAC5CtP,wBAAwBuP,gBAAgB;gBAC5CC,iBAAiBjK,IAAAA,mCAAkB;YACrC;YAEA,OAAOkK,IAAAA,gCAAwB,EAAC9I,QAAQ;gBACtChH;gBACAwP;gBACAhK;gBACA+J;gBACArH,iBAAiB;YACnB;QACF;IACF,CAAC,CAACZ,KAAK;IAEP,OAAOyI,cAAK,CAAC7Q,KAAK,CAAC6L;AACrB;AAEA,SAASW,gBACPhG,IAAW;IAEX,IAAIA,KAAKb,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAACkH,OAAOiE,IAAI,GAAGtK;IAErB,OACEsK,QAAQrQ,aAAa,iDAAiD;IACtEoM,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAqCkE,iBAAiB;AAE3D;AAEA,SAAS9D,kBACPzG,IAAW;IAEX,IAAIA,KAAKb,MAAM,KAAK,GAAG;QACrB,OAAO;IACT;IAEA,MAAM,CAACkH,OAAOiE,IAAI,GAAGtK;IAErB,OACEsK,QAAQrQ,aAAa,iDAAiD;IACtEoM,UAAU,QACV,OAAOA,UAAU,YACjB,AAACA,MAAuCK,mBAAmB;AAE/D;AAEA,SAASlK,sBACP/B,SAAoB,EACpB6K,aAAwC;IAExC,IAAI7K,UAAU+P,oBAAoB,IAAI/P,UAAU2O,WAAW,EAAE;QAC3D,OAAO;IACT;IAEA,IAAI3O,UAAUgQ,GAAG,IAAInF,eAAe;QAClC,OAAQA,cAAc9J,IAAI;YACxB,KAAK;gBACH,OAAO8J,cAAcoF,OAAO,CAACvC,GAAG,CAAC,qBAAqB;YACxD,KAAK;YACL,KAAK;gBACH,OAAO7C,cAAc/I,eAAe;YACtC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE+I;QACJ;IACF;IAEA,OAAO;AACT;AAEA,SAAS0D,wBACPrL,KAAiB,EACjBlD,SAAoB,EACpB6K,aAAwC,EACxC5J,YAAsB,EACtBiN,sBAA8B;IAE9B,sEAAsE;IACtE,2CAA2C;IAC3C,IAAIhL,MAAM+B,SAAS,IAAIiJ,wBAAwB;QAC7C9O,yBAAAA,MACE,wBACA8D,MAAM+B,SAAS,EACf,4CACAiJ;QAGF,OAAO;IACT;IAEA,4EAA4E;IAC5E,8EAA8E;IAC9E,8EAA8E;IAC9E,6EAA6E;IAC7E,mCAAmC;IACnC,IAAIrD,eAAe;QACjB,OAAQA,cAAc9J,IAAI;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE8J;QACJ;IACF;IAEA,4EAA4E;IAC5E,6CAA6C;IAC7C,IAAI3H,MAAM1B,IAAI,CAAC0O,IAAI,CAAC,CAAC9M,MAAQ+M,yBAAyB/M,KAAKpD,aAAa;QACtE,OAAO;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IACxC,IAAIiB,aAAaiP,IAAI,CAAC,CAAC9M,MAAQ+M,yBAAyB/M,KAAKpD,aAAa;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASmQ,yBAAyB/M,GAAW,EAAEpD,SAAoB;IACjE,MAAM,EAAEoQ,yBAAyB,EAAEC,sBAAsB,EAAE,GAAGrQ;IAE9D,4EAA4E;IAC5E,IAAIoQ,0BAA0B/M,QAAQ,CAACD,MAAM;QAC3ChE,yBAAAA,MAAQ,OAAOgE,KAAK;QAEpB,OAAO;IACT;IAEA,8EAA8E;IAC9E,0EAA0E;IAC1E,4EAA4E;IAC5E,SAAS;IACT,IAAIiN,0CAAAA,uBAAwBhN,QAAQ,CAACD,MAAM;QACzChE,yBAAAA,MAAQ,OAAOgE,KAAK;QAEpB,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0]}