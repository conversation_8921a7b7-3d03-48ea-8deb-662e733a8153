import { query } from '@/lib/db'

interface LinkingResult {
  success: boolean
  message: string
  details: {
    clients_updated: number
    employees_updated: number
    suppliers_updated: number
  }
}

/**
 * تطبيق ربط الحسابات الفرعية عند ربط حساب رئيسي بدليل الحسابات
 */
export async function applyAccountLinking(mainAccountId: number, chartAccountId: number): Promise<LinkingResult> {
  try {
    const result: LinkingResult = {
      success: false,
      message: '',
      details: {
        clients_updated: 0,
        employees_updated: 0,
        suppliers_updated: 0
      }
    }

    // جلب بيانات الحساب الرئيسي والحساب من دليل الحسابات
    const accountInfo = await query(`
      SELECT 
        ma.account_name as main_account_name,
        coa.account_code,
        coa.account_name as chart_account_name,
        coa.id as chart_account_id
      FROM main_accounts ma
      JOIN chart_of_accounts coa ON coa.id = $1
      WHERE ma.id = $2
    `, [chartAccountId, mainAccountId])

    if (accountInfo.rows.length === 0) {
      return {
        success: false,
        message: 'لم يتم العثور على بيانات الحساب',
        details: { clients_updated: 0, employees_updated: 0, suppliers_updated: 0 }
      }
    }

    const account = accountInfo.rows[0]
    console.log(`🔗 تطبيق ربط الحساب: ${account.main_account_name} -> ${account.account_code}`)

    // تحديد نوع الحساب وتطبيق الربط المناسب
    switch (account.main_account_name) {
      case 'حسابات العملاء':
        result.details.clients_updated = await linkClientsToAccount(account.chart_account_id, account.account_code)
        break
        
      case 'حسابات الموظفين':
        result.details.employees_updated = await linkEmployeesToAccount(account.chart_account_id, account.account_code)
        break
        
      case 'حسابات الموردين':
        result.details.suppliers_updated = await linkSuppliersToAccount(account.chart_account_id, account.account_code)
        break
        
      default:
        // للحسابات الأخرى، لا نطبق ربط تلقائي
        break
    }

    const totalUpdated = result.details.clients_updated + result.details.employees_updated + result.details.suppliers_updated

    result.success = true
    result.message = `تم تطبيق الربط بنجاح. تم تحديث ${totalUpdated} سجل`

    return result

  } catch (error) {
    console.error('خطأ في تطبيق ربط الحسابات:', error)
    return {
      success: false,
      message: 'حدث خطأ في تطبيق ربط الحسابات',
      details: { clients_updated: 0, employees_updated: 0, suppliers_updated: 0 }
    }
  }
}

/**
 * ربط العملاء بالحساب الأب
 */
async function linkClientsToAccount(parentAccountId: number, parentAccountCode: string): Promise<number> {
  try {
    // جلب جميع العملاء النشطين
    const clients = await query(`
      SELECT id, name 
      FROM clients 
      WHERE status = 'active'
      ORDER BY id
    `)

    let updatedCount = 0

    for (const client of clients.rows) {
      try {
        // إنشاء كود حساب فرعي للعميل
        const subAccountCode = `${parentAccountCode}${String(client.id).padStart(3, '0')}`
        
        // التحقق من وجود الحساب الفرعي
        let subAccountResult = await query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [subAccountCode])

        let subAccountId: number

        if (subAccountResult.rows.length === 0) {
          // إنشاء الحساب الفرعي إذا لم يكن موجوداً
          const newAccountResult = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'أصول', 'مدين', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `, [
            subAccountCode,
            `حساب العميل: ${client.name}`,
            parentAccountId
          ])
          
          subAccountId = newAccountResult.rows[0].id
          console.log(`✅ تم إنشاء حساب فرعي: ${subAccountCode} - ${client.name}`)
        } else {
          subAccountId = subAccountResult.rows[0].id
          console.log(`🔄 تم العثور على حساب فرعي: ${subAccountCode} - ${client.name}`)
        }

        // ربط العميل بالحساب الفرعي
        await query(`
          UPDATE clients 
          SET account_id = $1
          WHERE id = $2
        `, [subAccountId, client.id])

        updatedCount++

      } catch (error) {
        console.error(`خطأ في ربط العميل ${client.name}:`, error)
      }
    }

    console.log(`✅ تم ربط ${updatedCount} عميل بالحساب الأب`)
    return updatedCount

  } catch (error) {
    console.error('خطأ في ربط العملاء:', error)
    return 0
  }
}

/**
 * ربط الموظفين بالحساب الأب
 */
async function linkEmployeesToAccount(parentAccountId: number, parentAccountCode: string): Promise<number> {
  try {
    // جلب جميع الموظفين النشطين
    const employees = await query(`
      SELECT id, name 
      FROM employees 
      WHERE status = 'active'
      ORDER BY id
    `)

    let updatedCount = 0

    for (const employee of employees.rows) {
      try {
        // إنشاء كود حساب فرعي للموظف
        const subAccountCode = `${parentAccountCode}${String(employee.id).padStart(3, '0')}`
        
        // التحقق من وجود الحساب الفرعي
        let subAccountResult = await query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [subAccountCode])

        let subAccountId: number

        if (subAccountResult.rows.length === 0) {
          // إنشاء الحساب الفرعي إذا لم يكن موجوداً
          const newAccountResult = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'مصروفات', 'مدين', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `, [
            subAccountCode,
            `حساب الموظف: ${employee.name}`,
            parentAccountId
          ])
          
          subAccountId = newAccountResult.rows[0].id
          console.log(`✅ تم إنشاء حساب فرعي: ${subAccountCode} - ${employee.name}`)
        } else {
          subAccountId = subAccountResult.rows[0].id
          console.log(`🔄 تم العثور على حساب فرعي: ${subAccountCode} - ${employee.name}`)
        }

        // ربط الموظف بالحساب الفرعي
        await query(`
          UPDATE employees 
          SET account_id = $1
          WHERE id = $2
        `, [subAccountId, employee.id])

        updatedCount++

      } catch (error) {
        console.error(`خطأ في ربط الموظف ${employee.name}:`, error)
      }
    }

    console.log(`✅ تم ربط ${updatedCount} موظف بالحساب الأب`)
    return updatedCount

  } catch (error) {
    console.error('خطأ في ربط الموظفين:', error)
    return 0
  }
}

/**
 * ربط الموردين بالحساب الأب
 */
async function linkSuppliersToAccount(parentAccountId: number, parentAccountCode: string): Promise<number> {
  try {
    // جلب جميع الموردين النشطين (إذا كان جدول الموردين موجود)
    const suppliersResult = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `)

    if (suppliersResult.rows.length === 0) {
      console.log('⚠️ جدول الموردين غير موجود')
      return 0
    }

    const suppliers = await query(`
      SELECT id, name 
      FROM suppliers 
      WHERE status = 'active'
      ORDER BY id
    `)

    let updatedCount = 0

    for (const supplier of suppliers.rows) {
      try {
        // إنشاء كود حساب فرعي للمورد
        const subAccountCode = `${parentAccountCode}${String(supplier.id).padStart(3, '0')}`
        
        // التحقق من وجود الحساب الفرعي
        let subAccountResult = await query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [subAccountCode])

        let subAccountId: number

        if (subAccountResult.rows.length === 0) {
          // إنشاء الحساب الفرعي إذا لم يكن موجوداً
          const newAccountResult = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'خصوم', 'دائن', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `, [
            subAccountCode,
            `حساب المورد: ${supplier.name}`,
            parentAccountId
          ])
          
          subAccountId = newAccountResult.rows[0].id
          console.log(`✅ تم إنشاء حساب فرعي: ${subAccountCode} - ${supplier.name}`)
        } else {
          subAccountId = subAccountResult.rows[0].id
          console.log(`🔄 تم العثور على حساب فرعي: ${subAccountCode} - ${supplier.name}`)
        }

        // ربط المورد بالحساب الفرعي
        await query(`
          UPDATE suppliers 
          SET account_id = $1
          WHERE id = $2
        `, [subAccountId, supplier.id])

        updatedCount++

      } catch (error) {
        console.error(`خطأ في ربط المورد ${supplier.name}:`, error)
      }
    }

    console.log(`✅ تم ربط ${updatedCount} مورد بالحساب الأب`)
    return updatedCount

  } catch (error) {
    console.error('خطأ في ربط الموردين:', error)
    return 0
  }
}
