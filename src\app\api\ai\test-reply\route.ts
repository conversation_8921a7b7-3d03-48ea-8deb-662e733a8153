import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// POST - اختبار الرد التلقائي المبسط
export async function POST(request: NextRequest) {
  try {
    const { conversationId, messageText, senderType } = await request.json()

    console.log('🤖 Test Auto-Reply triggered:', { conversationId, messageText, senderType })

    // تجاهل الرسائل من المستخدمين (الإدارة)
    if (senderType === 'user') {
      return NextResponse.json({
        success: true,
        shouldReply: false,
        reason: 'message_from_admin'
      })
    }

    // رد تجريبي بسيط
    const testResponse = `مرحباً! هذا رد تجريبي من المساعد الذكي.

رسالتك: "${messageText}"

أنا هنا لمساعدتك في:
• الاستفسارات القانونية العامة
• معلومات عن خدمات المكتب
• توجيهك للمحامي المناسب

للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.

---
🤖 المساعد الذكي للمكتب (وضع الاختبار)`

    // حفظ الرد في قاعدة البيانات
    try {
      await query(`
        INSERT INTO messages 
        (conversation_id, sender_type, sender_id, message_text, message_type, created_at)
        VALUES ($1, 'ai', 0, $2, 'text', CURRENT_TIMESTAMP)
      `, [conversationId, testResponse])
      
      console.log('✅ AI message saved to database')
    } catch (dbError) {
      console.error('❌ Error saving AI message:', dbError)
    }

    return NextResponse.json({
      success: true,
      shouldReply: true,
      response: testResponse,
      model: 'test-model',
      delay: 0
    })

  } catch (error) {
    console.error('❌ Error in test auto-reply:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في الرد التجريبي' },
      { status: 500 }
    )
  }
}