#!/bin/bash

# سكريبت فحص حالة النظام القانوني
# Legal System Status Script

echo "📊 حالة النظام القانوني"
echo "========================"

PID_FILE="/home/<USER>/legal-system.pid"
LOG_FILE="/home/<USER>/legal-system.log"

# فحص ملف PID
if [ -f "$PID_FILE" ]; then
    SERVER_PID=$(cat "$PID_FILE")
    echo "📄 ملف PID موجود: $SERVER_PID"
    
    # فحص العملية
    if ps -p $SERVER_PID > /dev/null 2>&1; then
        echo "✅ النظام يعمل (PID: $SERVER_PID)"
        
        # معلومات العملية
        echo ""
        echo "📈 معلومات العملية:"
        ps -p $SERVER_PID -o pid,ppid,cmd,etime,%cpu,%mem
        
    else
        echo "❌ العملية غير موجودة (PID: $SERVER_PID)"
        echo "🧹 ملف PID قديم، يجب إزالته"
    fi
else
    echo "📄 لا يوجد ملف PID"
fi

echo ""
echo "🌐 فحص المنفذ 7443:"
PORT_CHECK=$(lsof -Pi :7443 -sTCP:LISTEN 2>/dev/null)

if [ -n "$PORT_CHECK" ]; then
    echo "✅ المنفذ 7443 مستخدم:"
    echo "$PORT_CHECK"
else
    echo "❌ المنفذ 7443 غير مستخدم"
fi

echo ""
echo "🔗 فحص الاتصال:"
if curl -s --connect-timeout 5 http://localhost:7443 > /dev/null 2>&1; then
    echo "✅ الخادم يستجيب على http://localhost:7443"
else
    echo "❌ الخادم لا يستجيب على http://localhost:7443"
fi

echo ""
echo "💾 استخدام الموارد:"
echo "العمليات المرتبطة بـ Node.js/npm:"
ps aux | grep -E "(node|npm)" | grep -v grep | head -5

echo ""
echo "📋 آخر سطور من السجل:"
if [ -f "$LOG_FILE" ]; then
    echo "ملف السجل: $LOG_FILE"
    echo "------------------------"
    tail -10 "$LOG_FILE"
else
    echo "❌ ملف السجل غير موجود: $LOG_FILE"
fi

echo ""
echo "🎛️ أوامر الإدارة:"
echo "=================="
echo "▶️  بدء النظام:        ./scripts/start-legal-system.sh"
echo "⏹️  إيقاف النظام:      ./scripts/stop-legal-system.sh"
echo "🔄 إعادة تشغيل:        ./scripts/restart-legal-system.sh"
echo "📋 عرض السجلات:       tail -f $LOG_FILE"

echo ""
echo "🔗 الروابط:"
echo "============"
echo "🏠 الصفحة الرئيسية:   http://localhost:7443"
echo "🔐 تسجيل الدخول:      http://localhost:7443/login"
echo "👤 المدير:            admin / ana8080"
