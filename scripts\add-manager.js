const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function addManagerAndUser() {
  const client = await pool.connect();
  
  try {
    console.log('👤 إضافة الموظف محمد الحاشدي وإنشاء مستخدم مدير...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. إضافة محافظة صنعاء إذا لم تكن موجودة
    console.log('🏛️ إضافة محافظة صنعاء...');

    // التحقق من وجود المحافظة أولاً
    let governorateResult = await client.query(`
      SELECT id, name FROM governorates WHERE name = 'صنعاء'
    `);

    let governorateId;
    if (governorateResult.rows.length > 0) {
      governorateId = governorateResult.rows[0].id;
      console.log(`✅ محافظة صنعاء موجودة - معرف: ${governorateId}`);
    } else {
      // إضافة المحافظة إذا لم تكن موجودة
      governorateResult = await client.query(`
        INSERT INTO governorates (name, code, created_date)
        VALUES ('صنعاء', 'SAN', CURRENT_DATE)
        RETURNING id, name
      `);
      governorateId = governorateResult.rows[0].id;
      console.log(`✅ تم إضافة محافظة صنعاء - معرف: ${governorateId}`);
    }

    // 2. إضافة الموظف محمد الحاشدي
    console.log('\n📝 إضافة الموظف محمد الحاشدي...');
    
    const employeeResult = await client.query(`
      INSERT INTO employees (
        name,
        position,
        department,
        phone,
        email,
        id_number,
        address,
        salary,
        hire_date,
        employee_number,
        status,
        governorate_id,
        created_date
      ) VALUES (
        'محمد الحاشدي',
        'مدير',
        'الإدارة العامة',
        '777123456',
        '<EMAIL>',
        '123456789',
        'صنعاء، اليمن',
        50000,
        CURRENT_DATE,
        'EMP001',
        'active',
        $1,
        CURRENT_DATE
      ) RETURNING id, name, position, employee_number
    `, [governorateId]);

    const newEmployee = employeeResult.rows[0];
    console.log('✅ تم إضافة الموظف بنجاح:');
    console.log(`   🆔 معرف الموظف: ${newEmployee.id}`);
    console.log(`   👤 الاسم: ${newEmployee.name}`);
    console.log(`   💼 المنصب: ${newEmployee.position}`);
    console.log(`   🔢 رقم الموظف: ${newEmployee.employee_number}`);

    // 3. إنشاء دور المدير إذا لم يكن موجوداً
    console.log('\n🔐 إنشاء دور المدير...');
    
    const roleResult = await client.query(`
      INSERT INTO user_roles (role_name, display_name, permissions, is_active, created_date)
      VALUES ('manager', 'مدير', ARRAY['all'], true, NOW())
      ON CONFLICT (role_name) DO UPDATE SET
        display_name = EXCLUDED.display_name,
        permissions = EXCLUDED.permissions,
        is_active = EXCLUDED.is_active
      RETURNING id, role_name, display_name
    `);

    let managerRoleId;
    if (roleResult.rows.length > 0) {
      managerRoleId = roleResult.rows[0].id;
      console.log('✅ تم إنشاء/تحديث دور المدير');
      console.log(`   🆔 معرف الدور: ${managerRoleId}`);
      console.log(`   📋 اسم الدور: ${roleResult.rows[0].role_name}`);
      console.log(`   🏷️  الاسم المعروض: ${roleResult.rows[0].display_name}`);
    } else {
      // الحصول على معرف الدور الموجود
      const existingRole = await client.query(`
        SELECT id FROM user_roles WHERE role_name = 'manager'
      `);
      managerRoleId = existingRole.rows[0].id;
      console.log('✅ دور المدير موجود بالفعل');
    }

    // 4. تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash('manager123', 10);

    // 5. إنشاء المستخدم المدير
    console.log('\n👨‍💼 إنشاء مستخدم مدير...');
    
    const userResult = await client.query(`
      INSERT INTO users (
        username, 
        email, 
        password_hash, 
        role, 
        permissions,
        user_type,
        status,
        employee_id,
        is_active, 
        created_date
      ) VALUES (
        'mohamed.alhashedi', 
        '<EMAIL>', 
        $1, 
        'manager',
        ARRAY['all'],
        'manager',
        'active',
        $2,
        true, 
        CURRENT_DATE
      ) RETURNING id, username, email, role, employee_id
    `, [hashedPassword, newEmployee.id]);

    const newUser = userResult.rows[0];

    // 6. إنشاء حساب مالي للموظف
    console.log('\n💰 إنشاء حساب مالي للموظف...');

    await client.query(`
      INSERT INTO chart_of_accounts (
        account_code,
        account_name,
        account_type,
        account_nature,
        account_level,
        parent_id,
        allow_transactions,
        is_active,
        created_date
      ) VALUES (
        '2101001',
        'حساب الموظف - محمد الحاشدي',
        'مصروفات',
        'مدين',
        4,
        NULL,
        true,
        true,
        CURRENT_DATE
      )
    `);

    // 7. ربط الموظف بالحساب المالي
    await client.query(`
      INSERT INTO account_links (
        main_account_id,
        linked_table,
        linked_record_id,
        sub_account_code,
        sub_account_name,
        is_active,
        created_date
      ) VALUES (
        (SELECT id FROM chart_of_accounts WHERE account_code = '2101001'),
        'employees',
        $1,
        '2101001',
        'حساب الموظف - محمد الحاشدي',
        true,
        CURRENT_DATE
      )
    `, [newEmployee.id]);

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم إنشاء الموظف والمستخدم بنجاح!');
    console.log('================================');
    console.log('📋 معلومات الموظف:');
    console.log(`   👤 الاسم: ${newEmployee.name}`);
    console.log(`   💼 المنصب: ${newEmployee.position}`);
    console.log(`   🔢 رقم الموظف: ${newEmployee.employee_number}`);
    console.log(`   🆔 معرف الموظف: ${newEmployee.id}`);
    
    console.log('\n🔐 معلومات المستخدم:');
    console.log(`   👤 اسم المستخدم: ${newUser.username}`);
    console.log(`   📧 البريد الإلكتروني: ${newUser.email}`);
    console.log(`   🔑 كلمة المرور: manager123`);
    console.log(`   👨‍💼 الدور: ${newUser.role}`);
    console.log(`   🆔 معرف المستخدم: ${newUser.id}`);
    console.log(`   🔗 مرتبط بالموظف: ${newUser.employee_id}`);
    
    console.log('\n💰 الحساب المالي:');
    console.log(`   🏦 رمز الحساب: 2101001`);
    console.log(`   📝 اسم الحساب: حساب الموظف - محمد الحاشدي`);
    
    console.log('\n================================');
    console.log('⚠️  تذكر تغيير كلمة المرور بعد تسجيل الدخول الأول!');
    console.log('🔐 الصلاحيات: جميع الصلاحيات (all)');

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إنشاء الموظف والمستخدم:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addManagerAndUser().catch(console.error);
