// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
}


// Validate ..\src\app\login\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\login\\page.js")
  handler satisfies AppPageConfig<"/login">
}

// Validate ..\src\app\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ..\src\app\api\auth\users\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\auth\\users\\route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/users">
}

// Validate ..\src\app\api\case-distribution\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\case-distribution\\route.js")
  handler satisfies RouteHandlerConfig<"/api/case-distribution">
}

// Validate ..\src\app\api\chart-of-accounts-new\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts-new\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts-new/[id]">
}

// Validate ..\src\app\api\chart-of-accounts-new\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts-new\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts-new">
}

// Validate ..\src\app\api\chart-of-accounts\link-tables\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\link-tables\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts/link-tables">
}

// Validate ..\src\app\api\chart-of-accounts\main\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\main\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts/main">
}

// Validate ..\src\app\api\chart-of-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts">
}

// Validate ..\src\app\api\employees\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\employees\\route.js")
  handler satisfies RouteHandlerConfig<"/api/employees">
}

// Validate ..\src\app\api\follows\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows">
}

// Validate ..\src\app\api\follows\service-allocation\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\service-allocation\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows/service-allocation">
}

// Validate ..\src\app\api\follows\user-issues\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\user-issues\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows/user-issues">
}

// Validate ..\src\app\api\journal-entries-new\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\journal-entries-new\\route.js")
  handler satisfies RouteHandlerConfig<"/api/journal-entries-new">
}

// Validate ..\src\app\api\migrate-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\migrate-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/migrate-accounts">
}

// Validate ..\src\app\api\reset-database\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\reset-database\\route.js")
  handler satisfies RouteHandlerConfig<"/api/reset-database">
}

// Validate ..\src\app\api\settings\announcements\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\settings\\announcements\\route.js")
  handler satisfies RouteHandlerConfig<"/api/settings/announcements">
}

// Validate ..\src\app\api\test-connection\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-connection\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-connection">
}





// Validate ..\src\app\layout.tsx
{
  const handler = {} as typeof import("..\\src\\app\\layout.js")
  handler satisfies LayoutConfig<"/">
}
