// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
}


// Validate ..\src\app\accounting\account-linking\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\account-linking\\page.js")
  handler satisfies AppPageConfig<"/accounting/account-linking">
}

// Validate ..\src\app\accounting\chart-of-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\chart-of-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/chart-of-accounts">
}

// Validate ..\src\app\accounting\default-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\default-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/default-accounts">
}

// Validate ..\src\app\accounting\link-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\link-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/link-accounts">
}

// Validate ..\src\app\accounting\main-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\main-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/main-accounts">
}

// Validate ..\src\app\accounting\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\page.js")
  handler satisfies AppPageConfig<"/accounting">
}

// Validate ..\src\app\accounting\payment-vouchers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\payment-vouchers\\page.js")
  handler satisfies AppPageConfig<"/accounting/payment-vouchers">
}

// Validate ..\src\app\accounting\receipt-vouchers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\receipt-vouchers\\page.js")
  handler satisfies AppPageConfig<"/accounting/receipt-vouchers">
}

// Validate ..\src\app\accounting\reports\account-statement\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\reports\\account-statement\\page.js")
  handler satisfies AppPageConfig<"/accounting/reports/account-statement">
}

// Validate ..\src\app\accounting\reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\reports\\page.js")
  handler satisfies AppPageConfig<"/accounting/reports">
}

// Validate ..\src\app\branches\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\branches\\page.js")
  handler satisfies AppPageConfig<"/branches">
}

// Validate ..\src\app\case-distribution\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\case-distribution\\page.js")
  handler satisfies AppPageConfig<"/case-distribution">
}

// Validate ..\src\app\case-reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\case-reports\\page.js")
  handler satisfies AppPageConfig<"/case-reports">
}

// Validate ..\src\app\client-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\client-accounts\\page.js")
  handler satisfies AppPageConfig<"/client-accounts">
}

// Validate ..\src\app\client-login\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\client-login\\page.js")
  handler satisfies AppPageConfig<"/client-login">
}

// Validate ..\src\app\client-portal\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\client-portal\\page.js")
  handler satisfies AppPageConfig<"/client-portal">
}

// Validate ..\src\app\clients\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\clients\\page.js")
  handler satisfies AppPageConfig<"/clients">
}

// Validate ..\src\app\company\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\company\\page.js")
  handler satisfies AppPageConfig<"/company">
}

// Validate ..\src\app\courts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\courts\\page.js")
  handler satisfies AppPageConfig<"/courts">
}

// Validate ..\src\app\dashboard\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\dashboard\\page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ..\src\app\documents\archive\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\documents\\archive\\page.js")
  handler satisfies AppPageConfig<"/documents/archive">
}

// Validate ..\src\app\documents\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\documents\\page.js")
  handler satisfies AppPageConfig<"/documents">
}

// Validate ..\src\app\documents\upload\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\documents\\upload\\page.js")
  handler satisfies AppPageConfig<"/documents/upload">
}

// Validate ..\src\app\employee-reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\employee-reports\\page.js")
  handler satisfies AppPageConfig<"/employee-reports">
}

// Validate ..\src\app\employees\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\employees\\page.js")
  handler satisfies AppPageConfig<"/employees">
}

// Validate ..\src\app\financial-reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\financial-reports\\page.js")
  handler satisfies AppPageConfig<"/financial-reports">
}

// Validate ..\src\app\follows\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\follows\\page.js")
  handler satisfies AppPageConfig<"/follows">
}

// Validate ..\src\app\governorates\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\governorates\\page.js")
  handler satisfies AppPageConfig<"/governorates">
}

// Validate ..\src\app\invoices\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\invoices\\page.js")
  handler satisfies AppPageConfig<"/invoices">
}

// Validate ..\src\app\issue-types\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\issue-types\\page.js")
  handler satisfies AppPageConfig<"/issue-types">
}

// Validate ..\src\app\issues\new\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\issues\\new\\page.js")
  handler satisfies AppPageConfig<"/issues/new">
}

// Validate ..\src\app\issues\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\issues\\page.js")
  handler satisfies AppPageConfig<"/issues">
}

// Validate ..\src\app\journal-entries-new\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\journal-entries-new\\page.js")
  handler satisfies AppPageConfig<"/journal-entries-new">
}

// Validate ..\src\app\lawyer-earnings\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\lawyer-earnings\\page.js")
  handler satisfies AppPageConfig<"/lawyer-earnings">
}

// Validate ..\src\app\login\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\login\\page.js")
  handler satisfies AppPageConfig<"/login">
}

// Validate ..\src\app\movements\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\movements\\page.js")
  handler satisfies AppPageConfig<"/movements">
}

// Validate ..\src\app\opening-balances\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\opening-balances\\page.js")
  handler satisfies AppPageConfig<"/opening-balances">
}

// Validate ..\src\app\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ..\src\app\payment-vouchers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\payment-vouchers\\page.js")
  handler satisfies AppPageConfig<"/payment-vouchers">
}

// Validate ..\src\app\percentages\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\percentages\\page.js")
  handler satisfies AppPageConfig<"/percentages">
}

// Validate ..\src\app\receipt-vouchers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\receipt-vouchers\\page.js")
  handler satisfies AppPageConfig<"/receipt-vouchers">
}

// Validate ..\src\app\reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\reports\\page.js")
  handler satisfies AppPageConfig<"/reports">
}

// Validate ..\src\app\services\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\services\\page.js")
  handler satisfies AppPageConfig<"/services">
}

// Validate ..\src\app\settings\announcements\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\settings\\announcements\\page.js")
  handler satisfies AppPageConfig<"/settings/announcements">
}

// Validate ..\src\app\settings\cost-centers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\settings\\cost-centers\\page.js")
  handler satisfies AppPageConfig<"/settings/cost-centers">
}

// Validate ..\src\app\settings\navigation-pages\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\settings\\navigation-pages\\page.js")
  handler satisfies AppPageConfig<"/settings/navigation-pages">
}

// Validate ..\src\app\setup-integrated\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\setup-integrated\\page.js")
  handler satisfies AppPageConfig<"/setup-integrated">
}

// Validate ..\src\app\setup-simple\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\setup-simple\\page.js")
  handler satisfies AppPageConfig<"/setup-simple">
}

// Validate ..\src\app\setup\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\setup\\page.js")
  handler satisfies AppPageConfig<"/setup">
}

// Validate ..\src\app\test-db\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\test-db\\page.js")
  handler satisfies AppPageConfig<"/test-db">
}

// Validate ..\src\app\test-issue-select\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\test-issue-select\\page.js")
  handler satisfies AppPageConfig<"/test-issue-select">
}

// Validate ..\src\app\time-tracking\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\time-tracking\\page.js")
  handler satisfies AppPageConfig<"/time-tracking">
}

// Validate ..\src\app\trial-balance\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\trial-balance\\page.js")
  handler satisfies AppPageConfig<"/trial-balance">
}

// Validate ..\src\app\under-construction\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\under-construction\\page.js")
  handler satisfies AppPageConfig<"/under-construction">
}

// Validate ..\src\app\users\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\users\\page.js")
  handler satisfies AppPageConfig<"/users">
}

// Validate ..\src\app\api\account-linking-settings\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\account-linking-settings\\route.js")
  handler satisfies RouteHandlerConfig<"/api/account-linking-settings">
}

// Validate ..\src\app\api\account-linking\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\account-linking\\route.js")
  handler satisfies RouteHandlerConfig<"/api/account-linking">
}

// Validate ..\src\app\api\accounting\account-linking\create-system-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\account-linking\\create-system-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/account-linking/create-system-accounts">
}

// Validate ..\src\app\api\accounting\account-linking\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\account-linking\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/account-linking">
}

// Validate ..\src\app\api\accounting\chart-of-accounts\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\chart-of-accounts\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/chart-of-accounts/[id]">
}

// Validate ..\src\app\api\accounting\chart-of-accounts\link\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\chart-of-accounts\\link\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/chart-of-accounts/link">
}

// Validate ..\src\app\api\accounting\chart-of-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\chart-of-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/chart-of-accounts">
}

// Validate ..\src\app\api\accounting\currencies\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\currencies\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/currencies">
}

// Validate ..\src\app\api\accounting\main-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\main-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/main-accounts">
}

// Validate ..\src\app\api\accounting\payment-methods\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\payment-methods\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/payment-methods">
}

// Validate ..\src\app\api\accounting\projects\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\projects\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/projects">
}

// Validate ..\src\app\api\accounting\vouchers\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\vouchers\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/vouchers">
}

// Validate ..\src\app\api\auth\users\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\auth\\users\\route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/users">
}

// Validate ..\src\app\api\auto-link-account\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\auto-link-account\\route.js")
  handler satisfies RouteHandlerConfig<"/api/auto-link-account">
}

// Validate ..\src\app\api\branches\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\branches\\route.js")
  handler satisfies RouteHandlerConfig<"/api/branches">
}

// Validate ..\src\app\api\case-distribution\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\case-distribution\\route.js")
  handler satisfies RouteHandlerConfig<"/api/case-distribution">
}

// Validate ..\src\app\api\chart-of-accounts-new\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts-new\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts-new/[id]">
}

// Validate ..\src\app\api\chart-of-accounts-new\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts-new\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts-new">
}

// Validate ..\src\app\api\chart-of-accounts\link-tables\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\link-tables\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts/link-tables">
}

// Validate ..\src\app\api\chart-of-accounts\main\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\main\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts/main">
}

// Validate ..\src\app\api\chart-of-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts">
}

// Validate ..\src\app\api\chat\conversations\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chat\\conversations\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chat/conversations">
}

// Validate ..\src\app\api\chat\messages\read\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chat\\messages\\read\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chat/messages/read">
}

// Validate ..\src\app\api\chat\messages\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chat\\messages\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chat/messages">
}

// Validate ..\src\app\api\client-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\client-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/client-accounts">
}

// Validate ..\src\app\api\clients\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\clients\\route.js")
  handler satisfies RouteHandlerConfig<"/api/clients">
}

// Validate ..\src\app\api\companies\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\companies\\route.js")
  handler satisfies RouteHandlerConfig<"/api/companies">
}

// Validate ..\src\app\api\cost-centers\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\cost-centers\\route.js")
  handler satisfies RouteHandlerConfig<"/api/cost-centers">
}

// Validate ..\src\app\api\courts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\courts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/courts">
}

// Validate ..\src\app\api\currencies\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\currencies\\route.js")
  handler satisfies RouteHandlerConfig<"/api/currencies">
}

// Validate ..\src\app\api\employees\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\employees\\route.js")
  handler satisfies RouteHandlerConfig<"/api/employees">
}

// Validate ..\src\app\api\follows\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows">
}

// Validate ..\src\app\api\follows\service-allocation\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\service-allocation\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows/service-allocation">
}

// Validate ..\src\app\api\follows\user-issues\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\user-issues\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows/user-issues">
}

// Validate ..\src\app\api\governorates\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\governorates\\route.js")
  handler satisfies RouteHandlerConfig<"/api/governorates">
}

// Validate ..\src\app\api\hearings\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\hearings\\route.js")
  handler satisfies RouteHandlerConfig<"/api/hearings">
}

// Validate ..\src\app\api\init\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\init\\route.js")
  handler satisfies RouteHandlerConfig<"/api/init">
}

// Validate ..\src\app\api\invoices\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\invoices\\route.js")
  handler satisfies RouteHandlerConfig<"/api/invoices">
}

// Validate ..\src\app\api\issue-types\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issue-types\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issue-types">
}

// Validate ..\src\app\api\issues\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issues\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issues/[id]">
}

// Validate ..\src\app\api\issues\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issues\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issues">
}

// Validate ..\src\app\api\issues\undistributed\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issues\\undistributed\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issues/undistributed">
}

// Validate ..\src\app\api\journal-entries-new\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\journal-entries-new\\route.js")
  handler satisfies RouteHandlerConfig<"/api/journal-entries-new">
}

// Validate ..\src\app\api\lawyer-earnings\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\lawyer-earnings\\route.js")
  handler satisfies RouteHandlerConfig<"/api/lawyer-earnings">
}

// Validate ..\src\app\api\legal-files\stats\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\legal-files\\stats\\route.js")
  handler satisfies RouteHandlerConfig<"/api/legal-files/stats">
}

// Validate ..\src\app\api\lineages\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\lineages\\route.js")
  handler satisfies RouteHandlerConfig<"/api/lineages">
}

// Validate ..\src\app\api\main-accounts\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\main-accounts\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/main-accounts/[id]">
}

// Validate ..\src\app\api\main-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\main-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/main-accounts">
}

// Validate ..\src\app\api\migrate-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\migrate-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/migrate-accounts">
}

// Validate ..\src\app\api\movements\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\movements\\route.js")
  handler satisfies RouteHandlerConfig<"/api/movements">
}

// Validate ..\src\app\api\navigation-pages\all\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\navigation-pages\\all\\route.js")
  handler satisfies RouteHandlerConfig<"/api/navigation-pages/all">
}

// Validate ..\src\app\api\notifications\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\notifications\\route.js")
  handler satisfies RouteHandlerConfig<"/api/notifications">
}

// Validate ..\src\app\api\opening-balances\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\opening-balances\\route.js")
  handler satisfies RouteHandlerConfig<"/api/opening-balances">
}

// Validate ..\src\app\api\payment-vouchers\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\payment-vouchers\\route.js")
  handler satisfies RouteHandlerConfig<"/api/payment-vouchers">
}

// Validate ..\src\app\api\percentages\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\percentages\\route.js")
  handler satisfies RouteHandlerConfig<"/api/percentages">
}

// Validate ..\src\app\api\receipt-vouchers\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\receipt-vouchers\\route.js")
  handler satisfies RouteHandlerConfig<"/api/receipt-vouchers">
}

// Validate ..\src\app\api\reports\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\reports\\route.js")
  handler satisfies RouteHandlerConfig<"/api/reports">
}

// Validate ..\src\app\api\reset-database\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\reset-database\\route.js")
  handler satisfies RouteHandlerConfig<"/api/reset-database">
}

// Validate ..\src\app\api\seed-data\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\seed-data\\route.js")
  handler satisfies RouteHandlerConfig<"/api/seed-data">
}

// Validate ..\src\app\api\seed-users\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\seed-users\\route.js")
  handler satisfies RouteHandlerConfig<"/api/seed-users">
}

// Validate ..\src\app\api\services\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\services\\route.js")
  handler satisfies RouteHandlerConfig<"/api/services">
}

// Validate ..\src\app\api\settings\announcements\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\settings\\announcements\\route.js")
  handler satisfies RouteHandlerConfig<"/api/settings/announcements">
}

// Validate ..\src\app\api\setup-integrated-ledgersmb\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\setup-integrated-ledgersmb\\route.js")
  handler satisfies RouteHandlerConfig<"/api/setup-integrated-ledgersmb">
}

// Validate ..\src\app\api\setup-simple\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\setup-simple\\route.js")
  handler satisfies RouteHandlerConfig<"/api/setup-simple">
}

// Validate ..\src\app\api\setup-step-by-step\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\setup-step-by-step\\route.js")
  handler satisfies RouteHandlerConfig<"/api/setup-step-by-step">
}

// Validate ..\src\app\api\simple-receipt\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\simple-receipt\\route.js")
  handler satisfies RouteHandlerConfig<"/api/simple-receipt">
}

// Validate ..\src\app\api\suppliers\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\suppliers\\route.js")
  handler satisfies RouteHandlerConfig<"/api/suppliers">
}

// Validate ..\src\app\api\table-stats\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\table-stats\\route.js")
  handler satisfies RouteHandlerConfig<"/api/table-stats">
}

// Validate ..\src\app\api\test-connection\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-connection\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-connection">
}

// Validate ..\src\app\api\test-database-exists\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-database-exists\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-database-exists">
}

// Validate ..\src\app\api\test-db\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-db\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-db">
}

// Validate ..\src\app\api\test-operations\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-operations\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-operations">
}

// Validate ..\src\app\api\test-tables\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-tables\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-tables">
}

// Validate ..\src\app\api\time-tracking\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\time-tracking\\route.js")
  handler satisfies RouteHandlerConfig<"/api/time-tracking">
}

// Validate ..\src\app\api\user-roles\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\user-roles\\route.js")
  handler satisfies RouteHandlerConfig<"/api/user-roles">
}

// Validate ..\src\app\api\users\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\users\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/users/[id]">
}

// Validate ..\src\app\api\users\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\users\\route.js")
  handler satisfies RouteHandlerConfig<"/api/users">
}





// Validate ..\src\app\layout.tsx
{
  const handler = {} as typeof import("..\\src\\app\\layout.js")
  handler satisfies LayoutConfig<"/">
}
