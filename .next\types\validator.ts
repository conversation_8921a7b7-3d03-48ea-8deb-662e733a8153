// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response> | Response | Promise<void> | void
}


// Validate ..\src\app\accounting\account-linking\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\account-linking\\page.js")
  handler satisfies AppPageConfig<"/accounting/account-linking">
}

// Validate ..\src\app\accounting\chart-of-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\chart-of-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/chart-of-accounts">
}

// Validate ..\src\app\accounting\default-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\default-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/default-accounts">
}

// Validate ..\src\app\accounting\link-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\link-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/link-accounts">
}

// Validate ..\src\app\accounting\main-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\main-accounts\\page.js")
  handler satisfies AppPageConfig<"/accounting/main-accounts">
}

// Validate ..\src\app\accounting\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\page.js")
  handler satisfies AppPageConfig<"/accounting">
}

// Validate ..\src\app\accounting\payment-vouchers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\payment-vouchers\\page.js")
  handler satisfies AppPageConfig<"/accounting/payment-vouchers">
}

// Validate ..\src\app\accounting\receipt-vouchers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\receipt-vouchers\\page.js")
  handler satisfies AppPageConfig<"/accounting/receipt-vouchers">
}

// Validate ..\src\app\accounting\reports\account-statement\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\reports\\account-statement\\page.js")
  handler satisfies AppPageConfig<"/accounting/reports/account-statement">
}

// Validate ..\src\app\accounting\reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\accounting\\reports\\page.js")
  handler satisfies AppPageConfig<"/accounting/reports">
}

// Validate ..\src\app\branches\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\branches\\page.js")
  handler satisfies AppPageConfig<"/branches">
}

// Validate ..\src\app\case-distribution\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\case-distribution\\page.js")
  handler satisfies AppPageConfig<"/case-distribution">
}

// Validate ..\src\app\case-reports\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\case-reports\\page.js")
  handler satisfies AppPageConfig<"/case-reports">
}

// Validate ..\src\app\client-accounts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\client-accounts\\page.js")
  handler satisfies AppPageConfig<"/client-accounts">
}

// Validate ..\src\app\client-login\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\client-login\\page.js")
  handler satisfies AppPageConfig<"/client-login">
}

// Validate ..\src\app\client-portal\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\client-portal\\page.js")
  handler satisfies AppPageConfig<"/client-portal">
}

// Validate ..\src\app\company\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\company\\page.js")
  handler satisfies AppPageConfig<"/company">
}

// Validate ..\src\app\courts\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\courts\\page.js")
  handler satisfies AppPageConfig<"/courts">
}

// Validate ..\src\app\dashboard\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\dashboard\\page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ..\src\app\employees\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\employees\\page.js")
  handler satisfies AppPageConfig<"/employees">
}

// Validate ..\src\app\follows\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\follows\\page.js")
  handler satisfies AppPageConfig<"/follows">
}

// Validate ..\src\app\governorates\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\governorates\\page.js")
  handler satisfies AppPageConfig<"/governorates">
}

// Validate ..\src\app\issue-types\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\issue-types\\page.js")
  handler satisfies AppPageConfig<"/issue-types">
}

// Validate ..\src\app\issues\new\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\issues\\new\\page.js")
  handler satisfies AppPageConfig<"/issues/new">
}

// Validate ..\src\app\issues\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\issues\\page.js")
  handler satisfies AppPageConfig<"/issues">
}

// Validate ..\src\app\journal-entries-new\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\journal-entries-new\\page.js")
  handler satisfies AppPageConfig<"/journal-entries-new">
}

// Validate ..\src\app\lawyer-earnings\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\lawyer-earnings\\page.js")
  handler satisfies AppPageConfig<"/lawyer-earnings">
}

// Validate ..\src\app\login\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\login\\page.js")
  handler satisfies AppPageConfig<"/login">
}

// Validate ..\src\app\movements\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\movements\\page.js")
  handler satisfies AppPageConfig<"/movements">
}

// Validate ..\src\app\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ..\src\app\percentages\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\percentages\\page.js")
  handler satisfies AppPageConfig<"/percentages">
}

// Validate ..\src\app\services\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\services\\page.js")
  handler satisfies AppPageConfig<"/services">
}

// Validate ..\src\app\settings\announcements\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\settings\\announcements\\page.js")
  handler satisfies AppPageConfig<"/settings/announcements">
}

// Validate ..\src\app\settings\cost-centers\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\settings\\cost-centers\\page.js")
  handler satisfies AppPageConfig<"/settings/cost-centers">
}

// Validate ..\src\app\settings\navigation-pages\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\settings\\navigation-pages\\page.js")
  handler satisfies AppPageConfig<"/settings/navigation-pages">
}

// Validate ..\src\app\setup-simple\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\setup-simple\\page.js")
  handler satisfies AppPageConfig<"/setup-simple">
}

// Validate ..\src\app\setup\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\setup\\page.js")
  handler satisfies AppPageConfig<"/setup">
}

// Validate ..\src\app\test-db\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\test-db\\page.js")
  handler satisfies AppPageConfig<"/test-db">
}

// Validate ..\src\app\time-tracking\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\time-tracking\\page.js")
  handler satisfies AppPageConfig<"/time-tracking">
}

// Validate ..\src\app\under-construction\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\under-construction\\page.js")
  handler satisfies AppPageConfig<"/under-construction">
}

// Validate ..\src\app\users\page.tsx
{
  const handler = {} as typeof import("..\\src\\app\\users\\page.js")
  handler satisfies AppPageConfig<"/users">
}

// Validate ..\src\app\api\accounting\account-linking\create-system-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\account-linking\\create-system-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/account-linking/create-system-accounts">
}

// Validate ..\src\app\api\accounting\account-linking\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\account-linking\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/account-linking">
}

// Validate ..\src\app\api\accounting\chart-of-accounts\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\chart-of-accounts\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/chart-of-accounts/[id]">
}

// Validate ..\src\app\api\accounting\chart-of-accounts\link\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\chart-of-accounts\\link\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/chart-of-accounts/link">
}

// Validate ..\src\app\api\accounting\chart-of-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\chart-of-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/chart-of-accounts">
}

// Validate ..\src\app\api\accounting\currencies\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\currencies\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/currencies">
}

// Validate ..\src\app\api\accounting\main-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\main-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/main-accounts">
}

// Validate ..\src\app\api\accounting\payment-methods\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\payment-methods\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/payment-methods">
}

// Validate ..\src\app\api\accounting\projects\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\projects\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/projects">
}

// Validate ..\src\app\api\accounting\vouchers\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\accounting\\vouchers\\route.js")
  handler satisfies RouteHandlerConfig<"/api/accounting/vouchers">
}

// Validate ..\src\app\api\auth\users\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\auth\\users\\route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/users">
}

// Validate ..\src\app\api\case-distribution\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\case-distribution\\route.js")
  handler satisfies RouteHandlerConfig<"/api/case-distribution">
}

// Validate ..\src\app\api\chart-of-accounts-new\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts-new\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts-new/[id]">
}

// Validate ..\src\app\api\chart-of-accounts-new\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts-new\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts-new">
}

// Validate ..\src\app\api\chart-of-accounts\link-tables\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\link-tables\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts/link-tables">
}

// Validate ..\src\app\api\chart-of-accounts\main\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\main\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts/main">
}

// Validate ..\src\app\api\chart-of-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\chart-of-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/chart-of-accounts">
}

// Validate ..\src\app\api\clients\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\clients\\route.js")
  handler satisfies RouteHandlerConfig<"/api/clients">
}

// Validate ..\src\app\api\courts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\courts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/courts">
}

// Validate ..\src\app\api\employees\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\employees\\route.js")
  handler satisfies RouteHandlerConfig<"/api/employees">
}

// Validate ..\src\app\api\follows\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows">
}

// Validate ..\src\app\api\follows\service-allocation\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\service-allocation\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows/service-allocation">
}

// Validate ..\src\app\api\follows\user-issues\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\follows\\user-issues\\route.js")
  handler satisfies RouteHandlerConfig<"/api/follows/user-issues">
}

// Validate ..\src\app\api\hearings\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\hearings\\route.js")
  handler satisfies RouteHandlerConfig<"/api/hearings">
}

// Validate ..\src\app\api\issues\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issues\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issues/[id]">
}

// Validate ..\src\app\api\issues\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issues\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issues">
}

// Validate ..\src\app\api\issues\undistributed\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\issues\\undistributed\\route.js")
  handler satisfies RouteHandlerConfig<"/api/issues/undistributed">
}

// Validate ..\src\app\api\journal-entries-new\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\journal-entries-new\\route.js")
  handler satisfies RouteHandlerConfig<"/api/journal-entries-new">
}

// Validate ..\src\app\api\migrate-accounts\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\migrate-accounts\\route.js")
  handler satisfies RouteHandlerConfig<"/api/migrate-accounts">
}

// Validate ..\src\app\api\reset-database\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\reset-database\\route.js")
  handler satisfies RouteHandlerConfig<"/api/reset-database">
}

// Validate ..\src\app\api\services\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\services\\route.js")
  handler satisfies RouteHandlerConfig<"/api/services">
}

// Validate ..\src\app\api\settings\announcements\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\settings\\announcements\\route.js")
  handler satisfies RouteHandlerConfig<"/api/settings/announcements">
}

// Validate ..\src\app\api\test-connection\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\test-connection\\route.js")
  handler satisfies RouteHandlerConfig<"/api/test-connection">
}

// Validate ..\src\app\api\users\[id]\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\users\\[id]\\route.js")
  handler satisfies RouteHandlerConfig<"/api/users/[id]">
}

// Validate ..\src\app\api\users\route.ts
{
  const handler = {} as typeof import("..\\src\\app\\api\\users\\route.js")
  handler satisfies RouteHandlerConfig<"/api/users">
}





// Validate ..\src\app\layout.tsx
{
  const handler = {} as typeof import("..\\src\\app\\layout.js")
  handler satisfies LayoutConfig<"/">
}
