const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function cleanupSubAccounts() {
  const client = await pool.connect();
  
  try {
    console.log('🧹 تنظيف الحسابات الفرعية القديمة...\n');

    await client.query('BEGIN');

    // 1. العثور على الحسابات الفرعية المنشأة تلقائياً
    const subAccounts = await client.query(`
      SELECT id, account_code, account_name, parent_id
      FROM chart_of_accounts 
      WHERE account_level = 5 
      AND (account_name LIKE '%حساب العميل:%' 
           OR account_name LIKE '%حساب الموظف:%' 
           OR account_name LIKE '%حساب المورد:%')
      ORDER BY account_code
    `);

    console.log(`🔍 تم العثور على ${subAccounts.rows.length} حساب فرعي للحذف`);

    if (subAccounts.rows.length === 0) {
      console.log('✅ لا توجد حسابات فرعية للحذف');
      await client.query('COMMIT');
      return;
    }

    // 2. تحديث العملاء والموظفين للإشارة إلى الحساب الأب
    console.log('\n🔄 تحديث ربط العملاء والموظفين...');

    for (const subAccount of subAccounts.rows) {
      try {
        // الحصول على الحساب الأب
        const parentAccount = await client.query(
          'SELECT id, account_code FROM chart_of_accounts WHERE id = $1',
          [subAccount.parent_id]
        );

        if (parentAccount.rows.length > 0) {
          const parent = parentAccount.rows[0];

          // تحديث الموظفين
          const employeeUpdate = await client.query(
            'UPDATE employees SET account_id = $1 WHERE account_id = $2',
            [parent.id, subAccount.id]
          );

          // تحديث العملاء
          const clientUpdate = await client.query(
            'UPDATE clients SET account_id = $1 WHERE account_id = $2',
            [parent.id, subAccount.id]
          );

          // تحديث الموردين (إذا وجد)
          let supplierUpdate = { rowCount: 0 };
          try {
            supplierUpdate = await client.query(
              'UPDATE suppliers SET account_id = $1 WHERE account_id = $2',
              [parent.id, subAccount.id]
            );
          } catch (error) {
            // جدول الموردين قد لا يكون موجود
          }

          const totalUpdated = (employeeUpdate.rowCount || 0) + (clientUpdate.rowCount || 0) + (supplierUpdate.rowCount || 0);

          if (totalUpdated > 0) {
            console.log(`  ✅ ${subAccount.account_code}: تم تحديث ${totalUpdated} سجل للإشارة إلى ${parent.account_code}`);
          }
        }

      } catch (error) {
        console.error(`  ❌ خطأ في تحديث ${subAccount.account_code}:`, error.message);
      }
    }

    // 3. حذف الحسابات الفرعية
    console.log('\n🗑️ حذف الحسابات الفرعية...');

    let deletedCount = 0;
    for (const subAccount of subAccounts.rows) {
      try {
        await client.query('DELETE FROM chart_of_accounts WHERE id = $1', [subAccount.id]);
        deletedCount++;
        console.log(`  ✅ تم حذف: ${subAccount.account_code} - ${subAccount.account_name}`);
      } catch (error) {
        console.error(`  ❌ خطأ في حذف ${subAccount.account_code}:`, error.message);
      }
    }

    await client.query('COMMIT');

    console.log('\n🎉 تم تنظيف الحسابات الفرعية بنجاح!');
    console.log('================================');
    console.log(`🗑️ تم حذف ${deletedCount} حساب فرعي`);
    console.log(`🔗 تم تحديث الربط للإشارة إلى الحسابات الأب`);

    // 4. فحص النتائج النهائية
    console.log('\n📊 فحص النتائج النهائية...');

    const employeeCheck = await client.query(`
      SELECT e.name, coa.account_code, coa.account_name 
      FROM employees e 
      JOIN chart_of_accounts coa ON e.account_id = coa.id 
      WHERE e.status = 'active' 
      LIMIT 3
    `);

    console.log('\n👨‍💼 أمثلة على ربط الموظفين:');
    employeeCheck.rows.forEach(emp => {
      console.log(`  - ${emp.name} → ${emp.account_code} (${emp.account_name})`);
    });

    const clientCheck = await client.query(`
      SELECT c.name, coa.account_code, coa.account_name 
      FROM clients c 
      JOIN chart_of_accounts coa ON c.account_id = coa.id 
      WHERE c.status = 'active' 
      LIMIT 3
    `);

    if (clientCheck.rows.length > 0) {
      console.log('\n👥 أمثلة على ربط العملاء:');
      clientCheck.rows.forEach(client => {
        console.log(`  - ${client.name} → ${client.account_code} (${client.account_name})`);
      });
    }

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تنظيف الحسابات الفرعية:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

cleanupSubAccounts().catch(console.error);
