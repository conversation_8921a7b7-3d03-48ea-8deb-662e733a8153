'use client'

import { useState, useEffect } from 'react'

export default function DashboardPage() {
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // جلب بيانات المستخدم من localStorage
    const userSession = localStorage.getItem('userSession')
    if (userSession) {
      setUser(JSON.parse(userSession))
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('userSession')
    document.cookie = 'userSession=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT'
    window.location.href = '/login'
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f3f4f6',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '30px',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '30px',
          borderBottom: '1px solid #e5e7eb',
          paddingBottom: '20px'
        }}>
          <div>
            <h1 style={{ 
              fontSize: '28px',
              fontWeight: 'bold',
              color: '#1f2937',
              margin: '0 0 5px 0'
            }}>
              لوحة التحكم
            </h1>
            <p style={{ 
              color: '#6b7280',
              margin: 0
            }}>
              مرحباً {user?.name || user?.username} - نظام الإدارة القانونية
            </p>
          </div>
          <button
            onClick={handleLogout}
            style={{
              padding: '10px 20px',
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            تسجيل الخروج
          </button>
        </div>

        {/* Stats Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <div style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '20px',
            borderRadius: '8px'
          }}>
            <h3 style={{ margin: '0 0 10px 0' }}>إجمالي القضايا</h3>
            <p style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>156</p>
          </div>
          
          <div style={{
            backgroundColor: '#10b981',
            color: 'white',
            padding: '20px',
            borderRadius: '8px'
          }}>
            <h3 style={{ margin: '0 0 10px 0' }}>الموكلين النشطين</h3>
            <p style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>89</p>
          </div>
          
          <div style={{
            backgroundColor: '#8b5cf6',
            color: 'white',
            padding: '20px',
            borderRadius: '8px'
          }}>
            <h3 style={{ margin: '0 0 10px 0' }}>المعاملات المالية</h3>
            <p style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>2,340</p>
          </div>
          
          <div style={{
            backgroundColor: '#f59e0b',
            color: 'white',
            padding: '20px',
            borderRadius: '8px'
          }}>
            <h3 style={{ margin: '0 0 10px 0' }}>القضايا المكتملة</h3>
            <p style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>78</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div style={{
          backgroundColor: '#f9fafb',
          padding: '20px',
          borderRadius: '8px'
        }}>
          <h2 style={{ 
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#1f2937',
            marginBottom: '15px'
          }}>
            الإجراءات السريعة
          </h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '15px'
          }}>
            <button style={{
              padding: '15px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              textAlign: 'center'
            }}>
              إضافة قضية جديدة
            </button>
            
            <button style={{
              padding: '15px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              textAlign: 'center'
            }}>
              إدارة الموكلين
            </button>
            
            <button style={{
              padding: '15px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              textAlign: 'center'
            }}>
              التقارير المالية
            </button>
            
            <button style={{
              padding: '15px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              cursor: 'pointer',
              textAlign: 'center'
            }}>
              إعدادات النظام
            </button>
          </div>
        </div>

        {/* Recent Activity */}
        <div style={{ marginTop: '30px' }}>
          <h2 style={{ 
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#1f2937',
            marginBottom: '15px'
          }}>
            النشاطات الأخيرة
          </h2>
          
          <div style={{
            backgroundColor: '#f9fafb',
            padding: '20px',
            borderRadius: '8px'
          }}>
            <p style={{ color: '#6b7280', textAlign: 'center' }}>
              لا توجد نشاطات حديثة
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
