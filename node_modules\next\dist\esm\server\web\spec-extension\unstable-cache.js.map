{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "sourcesContent": ["import type { IncrementalCache } from '../../lib/incremental-cache'\n\nimport { CACHE_ONE_YEAR } from '../../../lib/constants'\nimport { validateRevalidate, validateTags } from '../../lib/patch-fetch'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../../app-render/work-async-storage.external'\nimport {\n  getCacheSignal,\n  getDraftModeProviderForCacheScope,\n  workUnitAsyncStorage,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../../response-cache'\nimport type {\n  UnstableCacheStore,\n  WorkUnitStore,\n} from '../../app-render/work-unit-async-storage.external'\n\ntype Callback = (...args: any[]) => Promise<any>\n\nlet noStoreFetchIdx = 0\n\nasync function cacheNewResult<T>(\n  result: T,\n  incrementalCache: IncrementalCache,\n  cacheKey: string,\n  tags: string[],\n  revalidate: number | false | undefined,\n  fetchIdx: number,\n  fetchUrl: string\n): Promise<unknown> {\n  await incrementalCache.set(\n    cacheKey,\n    {\n      kind: CachedRouteKind.FETCH,\n      data: {\n        headers: {},\n        // TODO: handle non-JSON values?\n        body: JSON.stringify(result),\n        status: 200,\n        url: '',\n      } satisfies CachedFetchData,\n      revalidate: typeof revalidate !== 'number' ? CACHE_ONE_YEAR : revalidate,\n    },\n    { fetchCache: true, tags, fetchIdx, fetchUrl }\n  )\n  return\n}\n\n/**\n * This function allows you to cache the results of expensive operations, like database queries, and reuse them across multiple requests.\n *\n * Read more: [Next.js Docs: `unstable_cache`](https://nextjs.org/docs/app/api-reference/functions/unstable_cache)\n */\nexport function unstable_cache<T extends Callback>(\n  cb: T,\n  keyParts?: string[],\n  options: {\n    /**\n     * The revalidation interval in seconds.\n     */\n    revalidate?: number | false\n    tags?: string[]\n  } = {}\n): T {\n  if (options.revalidate === 0) {\n    throw new Error(\n      `Invariant revalidate: 0 can not be passed to unstable_cache(), must be \"false\" or \"> 0\" ${cb.toString()}`\n    )\n  }\n\n  // Validate the tags provided are valid\n  const tags = options.tags\n    ? validateTags(options.tags, `unstable_cache ${cb.toString()}`)\n    : []\n\n  // Validate the revalidate options\n  validateRevalidate(\n    options.revalidate,\n    `unstable_cache ${cb.name || cb.toString()}`\n  )\n\n  // Stash the fixed part of the key at construction time. The invocation key will combine\n  // the fixed key with the arguments when actually called\n  // @TODO if cb.toString() is long we should hash it\n  // @TODO come up with a collision-free way to combine keyParts\n  // @TODO consider validating the keyParts are all strings. TS can't provide runtime guarantees\n  // and the error produced by accidentally using something that cannot be safely coerced is likely\n  // hard to debug\n  const fixedKey = `${cb.toString()}-${\n    Array.isArray(keyParts) && keyParts.join(',')\n  }`\n\n  const cachedCb = async (...args: any[]) => {\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // We must be able to find the incremental cache otherwise we throw\n    const maybeIncrementalCache:\n      | import('../../lib/incremental-cache').IncrementalCache\n      | undefined =\n      workStore?.incrementalCache || (globalThis as any).__incrementalCache\n\n    if (!maybeIncrementalCache) {\n      throw new Error(\n        `Invariant: incrementalCache missing in unstable_cache ${cb.toString()}`\n      )\n    }\n    const incrementalCache = maybeIncrementalCache\n\n    const cacheSignal = workUnitStore ? getCacheSignal(workUnitStore) : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n    try {\n      // If there's no request store, we aren't in a request (or we're not in\n      // app router) and if there's no static generation store, we aren't in app\n      // router. Default to an empty pathname and search params when there's no\n      // request store or static generation store available.\n      const fetchUrlPrefix =\n        workStore && workUnitStore\n          ? getFetchUrlPrefix(workStore, workUnitStore)\n          : ''\n\n      // Construct the complete cache key for this function invocation\n      // @TODO stringify is likely not safe here. We will coerce undefined to null which will make\n      // the keyspace smaller than the execution space\n      const invocationKey = `${fixedKey}-${JSON.stringify(args)}`\n      const cacheKey = await incrementalCache.generateCacheKey(invocationKey)\n      // $urlWithPath,$sortedQueryStringKeys,$hashOfEveryThingElse\n      const fetchUrl = `unstable_cache ${fetchUrlPrefix} ${cb.name ? ` ${cb.name}` : cacheKey}`\n      const fetchIdx =\n        (workStore ? workStore.nextFetchId : noStoreFetchIdx) ?? 1\n\n      const implicitTags = workUnitStore?.implicitTags\n\n      const innerCacheStore: UnstableCacheStore = {\n        type: 'unstable-cache',\n        phase: 'render',\n        implicitTags,\n        draftMode:\n          workUnitStore &&\n          workStore &&\n          getDraftModeProviderForCacheScope(workStore, workUnitStore),\n      }\n\n      if (workStore) {\n        workStore.nextFetchId = fetchIdx + 1\n\n        // We are in an App Router context. We try to return the cached entry if it exists and is valid\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        let isNestedUnstableCache = false\n\n        if (workUnitStore) {\n          switch (workUnitStore.type) {\n            case 'cache':\n            case 'private-cache':\n            case 'prerender':\n            case 'prerender-runtime':\n            case 'prerender-ppr':\n            case 'prerender-legacy':\n              // We update the store's revalidate property if the option.revalidate is a higher precedence\n              // options.revalidate === undefined doesn't affect timing.\n              // options.revalidate === false doesn't shrink timing. it stays at the maximum.\n              if (typeof options.revalidate === 'number') {\n                if (workUnitStore.revalidate < options.revalidate) {\n                  // The store is already revalidating on a shorter time interval, leave it alone\n                } else {\n                  workUnitStore.revalidate = options.revalidate\n                }\n              }\n\n              // We need to accumulate the tags for this invocation within the store\n              const collectedTags = workUnitStore.tags\n              if (collectedTags === null) {\n                workUnitStore.tags = tags.slice()\n              } else {\n                for (const tag of tags) {\n                  // @TODO refactor tags to be a set to avoid this O(n) lookup\n                  if (!collectedTags.includes(tag)) {\n                    collectedTags.push(tag)\n                  }\n                }\n              }\n              break\n            case 'unstable-cache':\n              isNestedUnstableCache = true\n              break\n            case 'prerender-client':\n            case 'request':\n              break\n            default:\n              workUnitStore satisfies never\n          }\n        }\n\n        if (\n          // when we are nested inside of other unstable_cache's\n          // we should bypass cache similar to fetches\n          !isNestedUnstableCache &&\n          workStore.fetchCache !== 'force-no-store' &&\n          !workStore.isOnDemandRevalidate &&\n          !incrementalCache.isOnDemandRevalidate &&\n          !workStore.isDraftMode\n        ) {\n          // We attempt to get the current cache entry from the incremental cache.\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            softTags: implicitTags?.tags,\n            fetchIdx,\n            fetchUrl,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              // @TODO the invocation key can have sensitive data in it. we should not log this entire object\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else {\n              // We have a valid cache entry so we will be returning it. We also check to see if we need\n              // to background revalidate it by checking if it is stale.\n              const cachedResponse =\n                cacheEntry.value.data.body !== undefined\n                  ? JSON.parse(cacheEntry.value.data.body)\n                  : undefined\n              if (cacheEntry.isStale) {\n                // In App Router we return the stale result and revalidate in the background\n                if (!workStore.pendingRevalidates) {\n                  workStore.pendingRevalidates = {}\n                }\n\n                // We run the cache function asynchronously and save the result when it completes\n                workStore.pendingRevalidates[invocationKey] =\n                  workUnitAsyncStorage\n                    .run(innerCacheStore, cb, ...args)\n                    .then((result) => {\n                      return cacheNewResult(\n                        result,\n                        incrementalCache,\n                        cacheKey,\n                        tags,\n                        options.revalidate,\n                        fetchIdx,\n                        fetchUrl\n                      )\n                    })\n                    // @TODO This error handling seems wrong. We swallow the error?\n                    .catch((err) =>\n                      console.error(\n                        `revalidating cache with key: ${invocationKey}`,\n                        err\n                      )\n                    )\n              }\n              // We had a valid cache entry so we return it here\n              return cachedResponse\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n\n        if (!workStore.isDraftMode) {\n          if (!workStore.pendingRevalidates) {\n            workStore.pendingRevalidates = {}\n          }\n\n          // We need to push the cache result promise to pending\n          // revalidates otherwise it won't be awaited and is just\n          // dangling\n          workStore.pendingRevalidates[invocationKey] = cacheNewResult(\n            result,\n            incrementalCache,\n            cacheKey,\n            tags,\n            options.revalidate,\n            fetchIdx,\n            fetchUrl\n          )\n        }\n\n        return result\n      } else {\n        noStoreFetchIdx += 1\n        // We are in Pages Router or were called outside of a render. We don't have a store\n        // so we just call the callback directly when it needs to run.\n        // If the entry is fresh we return it. If the entry is stale we return it but revalidate the entry in\n        // the background. If the entry is missing or invalid we generate a new entry and return it.\n\n        if (!incrementalCache.isOnDemandRevalidate) {\n          // We aren't doing an on demand revalidation so we check use the cache if valid\n          const cacheEntry = await incrementalCache.get(cacheKey, {\n            kind: IncrementalCacheKind.FETCH,\n            revalidate: options.revalidate,\n            tags,\n            fetchIdx,\n            fetchUrl,\n            softTags: implicitTags?.tags,\n          })\n\n          if (cacheEntry && cacheEntry.value) {\n            // The entry exists and has a value\n            if (cacheEntry.value.kind !== CachedRouteKind.FETCH) {\n              // The entry is invalid and we need a special warning\n              // @TODO why do we warn this way? Should this just be an error? How are these errors surfaced\n              // so bugs can be reported\n              console.error(\n                `Invariant invalid cacheEntry returned for ${invocationKey}`\n              )\n              // will fall through to generating a new cache entry below\n            } else if (!cacheEntry.isStale) {\n              // We have a valid cache entry and it is fresh so we return it\n              return cacheEntry.value.data.body !== undefined\n                ? JSON.parse(cacheEntry.value.data.body)\n                : undefined\n            }\n          }\n        }\n\n        // If we got this far then we had an invalid cache entry and need to generate a new one\n        const result = await workUnitAsyncStorage.run(\n          innerCacheStore,\n          cb,\n          ...args\n        )\n\n        // we need to wait setting the new cache result here as\n        // we don't have pending revalidates on workStore to\n        // push to and we can't have a dangling promise\n        await cacheNewResult(\n          result,\n          incrementalCache,\n          cacheKey,\n          tags,\n          options.revalidate,\n          fetchIdx,\n          fetchUrl\n        )\n        return result\n      }\n    } finally {\n      if (cacheSignal) {\n        cacheSignal.endRead()\n      }\n    }\n  }\n  // TODO: once AsyncLocalStorage.run() returns the correct types this override will no longer be necessary\n  return cachedCb as unknown as T\n}\n\nfunction getFetchUrlPrefix(\n  workStore: WorkStore,\n  workUnitStore: WorkUnitStore\n): string {\n  switch (workUnitStore.type) {\n    case 'request':\n      const pathname = workUnitStore.url.pathname\n      const searchParams = new URLSearchParams(workUnitStore.url.search)\n\n      const sortedSearch = [...searchParams.keys()]\n        .sort((a, b) => a.localeCompare(b))\n        .map((key) => `${key}=${searchParams.get(key)}`)\n        .join('&')\n\n      return `${pathname}${sortedSearch.length ? '?' : ''}${sortedSearch}`\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-runtime':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return workStore.route\n    default:\n      return workUnitStore satisfies never\n  }\n}\n"], "names": ["CACHE_ONE_YEAR", "validateRevalidate", "validateTags", "workAsyncStorage", "getCacheSignal", "getDraftModeProviderForCacheScope", "workUnitAsyncStorage", "CachedRouteKind", "IncrementalCacheKind", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "FETCH", "data", "headers", "body", "JSON", "stringify", "status", "url", "fetchCache", "unstable_cache", "cb", "keyParts", "options", "Error", "toString", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "workStore", "getStore", "workUnitStore", "maybeIncrementalCache", "globalThis", "__incrementalCache", "cacheSignal", "beginRead", "fetchUrlPrefix", "getFetchUrlPrefix", "invocation<PERSON><PERSON>", "generate<PERSON>ache<PERSON>ey", "nextFetchId", "implicitTags", "innerCacheStore", "type", "phase", "draftMode", "isNestedUnstableCache", "collectedTags", "slice", "tag", "includes", "push", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "get", "softTags", "value", "console", "error", "cachedResponse", "undefined", "parse", "isStale", "pendingRevalidates", "run", "then", "catch", "err", "endRead", "pathname", "searchParams", "URLSearchParams", "search", "sortedSearch", "keys", "sort", "a", "b", "localeCompare", "map", "key", "length", "route"], "mappings": "AAEA,SAASA,cAAc,QAAQ,yBAAwB;AACvD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,wBAAuB;AACxE,SACEC,gBAAgB,QAEX,+CAA8C;AACrD,SACEC,cAAc,EACdC,iCAAiC,EACjCC,oBAAoB,QACf,oDAAmD;AAC1D,SACEC,eAAe,EACfC,oBAAoB,QAEf,uBAAsB;AAQ7B,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAMZ,gBAAgBa,KAAK;QAC3BC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACd;YACrBe,QAAQ;YACRC,KAAK;QACP;QACAZ,YAAY,OAAOA,eAAe,WAAWf,iBAAiBe;IAChE,GACA;QAAEa,YAAY;QAAMd;QAAME;QAAUC;IAAS;IAE/C;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASY,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAMI,CAAC,CAAC;IAEN,IAAIA,QAAQjB,UAAU,KAAK,GAAG;QAC5B,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,IAAI,GADtG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uCAAuC;IACvC,MAAMpB,OAAOkB,QAAQlB,IAAI,GACrBZ,aAAa8B,QAAQlB,IAAI,EAAE,CAAC,eAAe,EAAEgB,GAAGI,QAAQ,IAAI,IAC5D,EAAE;IAEN,kCAAkC;IAClCjC,mBACE+B,QAAQjB,UAAU,EAClB,CAAC,eAAe,EAAEe,GAAGK,IAAI,IAAIL,GAAGI,QAAQ,IAAI;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAME,WAAW,GAAGN,GAAGI,QAAQ,GAAG,CAAC,EACjCG,MAAMC,OAAO,CAACP,aAAaA,SAASQ,IAAI,CAAC,MACzC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,YAAYvC,iBAAiBwC,QAAQ;QAC3C,MAAMC,gBAAgBtC,qBAAqBqC,QAAQ;QAEnD,mEAAmE;QACnE,MAAME,wBAGJH,CAAAA,6BAAAA,UAAW9B,gBAAgB,KAAI,AAACkC,WAAmBC,kBAAkB;QAEvE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,qBAEL,CAFK,IAAIZ,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,IAAI,GADpE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMtB,mBAAmBiC;QAEzB,MAAMG,cAAcJ,gBAAgBxC,eAAewC,iBAAiB;QACpE,IAAII,aAAa;YACfA,YAAYC,SAAS;QACvB;QACA,IAAI;YACF,uEAAuE;YACvE,0EAA0E;YAC1E,yEAAyE;YACzE,sDAAsD;YACtD,MAAMC,iBACJR,aAAaE,gBACTO,kBAAkBT,WAAWE,iBAC7B;YAEN,gEAAgE;YAChE,4FAA4F;YAC5F,gDAAgD;YAChD,MAAMQ,gBAAgB,GAAGhB,SAAS,CAAC,EAAEZ,KAAKC,SAAS,CAACgB,OAAO;YAC3D,MAAM5B,WAAW,MAAMD,iBAAiByC,gBAAgB,CAACD;YACzD,4DAA4D;YAC5D,MAAMnC,WAAW,CAAC,eAAe,EAAEiC,eAAe,CAAC,EAAEpB,GAAGK,IAAI,GAAG,CAAC,CAAC,EAAEL,GAAGK,IAAI,EAAE,GAAGtB,UAAU;YACzF,MAAMG,WACJ,AAAC0B,CAAAA,YAAYA,UAAUY,WAAW,GAAG7C,eAAc,KAAM;YAE3D,MAAM8C,eAAeX,iCAAAA,cAAeW,YAAY;YAEhD,MAAMC,kBAAsC;gBAC1CC,MAAM;gBACNC,OAAO;gBACPH;gBACAI,WACEf,iBACAF,aACArC,kCAAkCqC,WAAWE;YACjD;YAEA,IAAIF,WAAW;gBACbA,UAAUY,WAAW,GAAGtC,WAAW;gBAEnC,+FAA+F;gBAC/F,qGAAqG;gBACrG,4FAA4F;gBAE5F,IAAI4C,wBAAwB;gBAE5B,IAAIhB,eAAe;oBACjB,OAAQA,cAAca,IAAI;wBACxB,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,4FAA4F;4BAC5F,0DAA0D;4BAC1D,+EAA+E;4BAC/E,IAAI,OAAOzB,QAAQjB,UAAU,KAAK,UAAU;gCAC1C,IAAI6B,cAAc7B,UAAU,GAAGiB,QAAQjB,UAAU,EAAE;gCACjD,+EAA+E;gCACjF,OAAO;oCACL6B,cAAc7B,UAAU,GAAGiB,QAAQjB,UAAU;gCAC/C;4BACF;4BAEA,sEAAsE;4BACtE,MAAM8C,gBAAgBjB,cAAc9B,IAAI;4BACxC,IAAI+C,kBAAkB,MAAM;gCAC1BjB,cAAc9B,IAAI,GAAGA,KAAKgD,KAAK;4BACjC,OAAO;gCACL,KAAK,MAAMC,OAAOjD,KAAM;oCACtB,4DAA4D;oCAC5D,IAAI,CAAC+C,cAAcG,QAAQ,CAACD,MAAM;wCAChCF,cAAcI,IAAI,CAACF;oCACrB;gCACF;4BACF;4BACA;wBACF,KAAK;4BACHH,wBAAwB;4BACxB;wBACF,KAAK;wBACL,KAAK;4BACH;wBACF;4BACEhB;oBACJ;gBACF;gBAEA,IACE,sDAAsD;gBACtD,4CAA4C;gBAC5C,CAACgB,yBACDlB,UAAUd,UAAU,KAAK,oBACzB,CAACc,UAAUwB,oBAAoB,IAC/B,CAACtD,iBAAiBsD,oBAAoB,IACtC,CAACxB,UAAUyB,WAAW,EACtB;oBACA,wEAAwE;oBACxE,MAAMC,aAAa,MAAMxD,iBAAiByD,GAAG,CAACxD,UAAU;wBACtDM,MAAMX,qBAAqBY,KAAK;wBAChCL,YAAYiB,QAAQjB,UAAU;wBAC9BD;wBACAwD,QAAQ,EAAEf,gCAAAA,aAAczC,IAAI;wBAC5BE;wBACAC;oBACF;oBAEA,IAAImD,cAAcA,WAAWG,KAAK,EAAE;wBAClC,mCAAmC;wBACnC,IAAIH,WAAWG,KAAK,CAACpD,IAAI,KAAKZ,gBAAgBa,KAAK,EAAE;4BACnD,qDAAqD;4BACrD,6FAA6F;4BAC7F,0BAA0B;4BAC1B,+FAA+F;4BAC/FoD,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAErB,eAAe;wBAE9D,0DAA0D;wBAC5D,OAAO;4BACL,0FAA0F;4BAC1F,0DAA0D;4BAC1D,MAAMsB,iBACJN,WAAWG,KAAK,CAAClD,IAAI,CAACE,IAAI,KAAKoD,YAC3BnD,KAAKoD,KAAK,CAACR,WAAWG,KAAK,CAAClD,IAAI,CAACE,IAAI,IACrCoD;4BACN,IAAIP,WAAWS,OAAO,EAAE;gCACtB,4EAA4E;gCAC5E,IAAI,CAACnC,UAAUoC,kBAAkB,EAAE;oCACjCpC,UAAUoC,kBAAkB,GAAG,CAAC;gCAClC;gCAEA,iFAAiF;gCACjFpC,UAAUoC,kBAAkB,CAAC1B,cAAc,GACzC9C,qBACGyE,GAAG,CAACvB,iBAAiB1B,OAAOW,MAC5BuC,IAAI,CAAC,CAACrE;oCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAkB,QAAQjB,UAAU,EAClBC,UACAC;gCAEJ,EACA,+DAA+D;iCAC9DgE,KAAK,CAAC,CAACC,MACNV,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAErB,eAAe,EAC/C8B;4BAGV;4BACA,kDAAkD;4BAClD,OAAOR;wBACT;oBACF;gBACF;gBAEA,uFAAuF;gBACvF,MAAM/D,SAAS,MAAML,qBAAqByE,GAAG,CAC3CvB,iBACA1B,OACGW;gBAGL,IAAI,CAACC,UAAUyB,WAAW,EAAE;oBAC1B,IAAI,CAACzB,UAAUoC,kBAAkB,EAAE;wBACjCpC,UAAUoC,kBAAkB,GAAG,CAAC;oBAClC;oBAEA,sDAAsD;oBACtD,wDAAwD;oBACxD,WAAW;oBACXpC,UAAUoC,kBAAkB,CAAC1B,cAAc,GAAG1C,eAC5CC,QACAC,kBACAC,UACAC,MACAkB,QAAQjB,UAAU,EAClBC,UACAC;gBAEJ;gBAEA,OAAON;YACT,OAAO;gBACLF,mBAAmB;gBACnB,mFAAmF;gBACnF,8DAA8D;gBAC9D,qGAAqG;gBACrG,4FAA4F;gBAE5F,IAAI,CAACG,iBAAiBsD,oBAAoB,EAAE;oBAC1C,+EAA+E;oBAC/E,MAAME,aAAa,MAAMxD,iBAAiByD,GAAG,CAACxD,UAAU;wBACtDM,MAAMX,qBAAqBY,KAAK;wBAChCL,YAAYiB,QAAQjB,UAAU;wBAC9BD;wBACAE;wBACAC;wBACAqD,QAAQ,EAAEf,gCAAAA,aAAczC,IAAI;oBAC9B;oBAEA,IAAIsD,cAAcA,WAAWG,KAAK,EAAE;wBAClC,mCAAmC;wBACnC,IAAIH,WAAWG,KAAK,CAACpD,IAAI,KAAKZ,gBAAgBa,KAAK,EAAE;4BACnD,qDAAqD;4BACrD,6FAA6F;4BAC7F,0BAA0B;4BAC1BoD,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAErB,eAAe;wBAE9D,0DAA0D;wBAC5D,OAAO,IAAI,CAACgB,WAAWS,OAAO,EAAE;4BAC9B,8DAA8D;4BAC9D,OAAOT,WAAWG,KAAK,CAAClD,IAAI,CAACE,IAAI,KAAKoD,YAClCnD,KAAKoD,KAAK,CAACR,WAAWG,KAAK,CAAClD,IAAI,CAACE,IAAI,IACrCoD;wBACN;oBACF;gBACF;gBAEA,uFAAuF;gBACvF,MAAMhE,SAAS,MAAML,qBAAqByE,GAAG,CAC3CvB,iBACA1B,OACGW;gBAGL,uDAAuD;gBACvD,oDAAoD;gBACpD,+CAA+C;gBAC/C,MAAM/B,eACJC,QACAC,kBACAC,UACAC,MACAkB,QAAQjB,UAAU,EAClBC,UACAC;gBAEF,OAAON;YACT;QACF,SAAU;YACR,IAAIqC,aAAa;gBACfA,YAAYmC,OAAO;YACrB;QACF;IACF;IACA,yGAAyG;IACzG,OAAO3C;AACT;AAEA,SAASW,kBACPT,SAAoB,EACpBE,aAA4B;IAE5B,OAAQA,cAAca,IAAI;QACxB,KAAK;YACH,MAAM2B,WAAWxC,cAAcjB,GAAG,CAACyD,QAAQ;YAC3C,MAAMC,eAAe,IAAIC,gBAAgB1C,cAAcjB,GAAG,CAAC4D,MAAM;YAEjE,MAAMC,eAAe;mBAAIH,aAAaI,IAAI;aAAG,CAC1CC,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,MAAQ,GAAGA,IAAI,CAAC,EAAEV,aAAahB,GAAG,CAAC0B,MAAM,EAC9CxD,IAAI,CAAC;YAER,OAAO,GAAG6C,WAAWI,aAAaQ,MAAM,GAAG,MAAM,KAAKR,cAAc;QACtE,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO9C,UAAUuD,KAAK;QACxB;YACE,OAAOrD;IACX;AACF", "ignoreList": [0]}