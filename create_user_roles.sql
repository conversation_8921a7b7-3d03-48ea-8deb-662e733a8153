-- إنشاء جدول user_roles
CREATE TABLE IF NOT EXISTS user_roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج الأدوار الأساسية
INSERT INTO user_roles (role_name, display_name, description, permissions) VALUES
('admin', 'مدير النظام', 'مدير النظام مع جميع الصلاحيات', '{"all": true}'),
('manager', 'مدير', 'مدير مع صلاحيات إدارية محدودة', '{"manage_users": true, "view_reports": true}'),
('employee', 'موظف', 'موظف عادي', '{"view_cases": true, "edit_own_cases": true}'),
('client', 'عميل', 'عميل يمكنه رؤية قضاياه فقط', '{"view_own_cases": true}'),
('lawyer', 'محامي', 'محامي مع صلاحيات قانونية', '{"manage_cases": true, "view_reports": true}'),
('accountant', 'محاسب', 'محاسب مع صلاحيات مالية', '{"manage_accounting": true, "view_financial_reports": true}')
ON CONFLICT (role_name) DO NOTHING;
