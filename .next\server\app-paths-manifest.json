{"/_not-found/page": "app/_not-found/page.js", "/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/api/issues/route": "app/api/issues/route.js", "/api/courts/route": "app/api/courts/route.js", "/api/follows/route": "app/api/follows/route.js", "/api/follows/user-issues/route": "app/api/follows/user-issues/route.js", "/api/case-distribution/route": "app/api/case-distribution/route.js", "/dashboard/page": "app/dashboard/page.js", "/issues/page": "app/issues/page.js", "/case-distribution/page": "app/case-distribution/page.js", "/follows/page": "app/follows/page.js"}