{"/api/settings/announcements/route": "app/api/settings/announcements/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/accounting/chart-of-accounts/route": "app/api/accounting/chart-of-accounts/route.js", "/api/accounting/currencies/route": "app/api/accounting/currencies/route.js", "/api/accounting/payment-methods/route": "app/api/accounting/payment-methods/route.js", "/api/issues/route": "app/api/issues/route.js", "/api/services/route": "app/api/services/route.js", "/api/clients/route": "app/api/clients/route.js", "/dashboard/page": "app/dashboard/page.js", "/issues/page": "app/issues/page.js", "/case-distribution/page": "app/case-distribution/page.js", "/follows/page": "app/follows/page.js", "/accounting/page": "app/accounting/page.js", "/issue-types/page": "app/issue-types/page.js", "/accounting/payment-vouchers/page": "app/accounting/payment-vouchers/page.js"}