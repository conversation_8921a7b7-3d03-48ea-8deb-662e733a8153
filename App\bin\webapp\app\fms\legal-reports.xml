<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج التقارير القانونية -->
<form-module name="legal-reports" title="التقارير القانونية" icon="chart-bar">
    
    <!-- تقرير ملخص القضايا -->
    <report-view name="cases-summary" title="ملخص القضايا">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date"/>
            <parameter name="date_to" title="إلى تاريخ" type="date"/>
            <parameter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="active">نشطة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </parameter>
            <parameter name="issue_type_id" title="نوع القضية" type="lookup" 
                      lookup-table="issue_types" lookup-field="name"/>
        </parameters>
        
        <data-source>
            SELECT 
                i.case_number,
                i.title,
                i.client_name,
                it.name as issue_type,
                i.status,
                i.amount,
                COUNT(f.id) as follows_count,
                SUM(CASE WHEN f.status = 'completed' THEN 1 ELSE 0 END) as completed_follows,
                i.created_date
            FROM issues i
            LEFT JOIN issue_types it ON i.issue_type_id = it.id
            LEFT JOIN follows f ON i.id = f.case_id
            WHERE (@date_from IS NULL OR i.created_date >= @date_from)
              AND (@date_to IS NULL OR i.created_date <= @date_to)
              AND (@status = '' OR i.status = @status)
              AND (@issue_type_id IS NULL OR i.issue_type_id = @issue_type_id)
            GROUP BY i.id, i.case_number, i.title, i.client_name, it.name, i.status, i.amount, i.created_date
            ORDER BY i.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="title" title="العنوان" width="200"/>
            <column name="client_name" title="الموكل" width="150"/>
            <column name="issue_type" title="النوع" width="120"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="amount" title="المبلغ" width="120" format="currency"/>
            <column name="follows_count" title="عدد المتابعات" width="100"/>
            <column name="completed_follows" title="المتابعات المكتملة" width="120"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <summary>
            <field name="total_cases" title="إجمالي القضايا" type="count"/>
            <field name="total_amount" title="إجمالي المبالغ" type="sum" column="amount" format="currency"/>
            <field name="avg_amount" title="متوسط المبلغ" type="avg" column="amount" format="currency"/>
            <field name="total_follows" title="إجمالي المتابعات" type="sum" column="follows_count"/>
        </summary>
    </report-view>
    
    <!-- تقرير المتابعات -->
    <report-view name="follows-report" title="تقرير المتابعات">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date"/>
            <parameter name="date_to" title="إلى تاريخ" type="date"/>
            <parameter name="case_id" title="القضية" type="lookup" 
                      lookup-table="issues" lookup-field="case_number + ' - ' + title"/>
            <parameter name="service_id" title="نوع الخدمة" type="lookup" 
                      lookup-table="services" lookup-field="name"/>
            <parameter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </parameter>
        </parameters>
        
        <data-source>
            SELECT 
                i.case_number,
                i.title as case_title,
                s.name as service_name,
                f.report,
                f.date_field,
                f.status,
                e.name as user_name,
                f.created_date
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            JOIN services s ON f.service_id = s.id
            LEFT JOIN employees e ON f.user_id = e.id
            WHERE (@date_from IS NULL OR f.date_field >= @date_from)
              AND (@date_to IS NULL OR f.date_field <= @date_to)
              AND (@case_id IS NULL OR f.case_id = @case_id)
              AND (@service_id IS NULL OR f.service_id = @service_id)
              AND (@status = '' OR f.status = @status)
            ORDER BY f.date_field DESC, f.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="case_title" title="عنوان القضية" width="200"/>
            <column name="service_name" title="نوع الخدمة" width="150"/>
            <column name="report" title="التقرير" width="300" truncate="100"/>
            <column name="date_field" title="تاريخ المتابعة" width="120" format="date"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="user_name" title="المستخدم" width="120"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="datetime"/>
        </columns>
        
        <summary>
            <field name="total_follows" title="إجمالي المتابعات" type="count"/>
            <field name="completed_follows" title="المتابعات المكتملة" type="count" 
                   condition="status = 'completed'"/>
            <field name="pending_follows" title="المتابعات المعلقة" type="count" 
                   condition="status = 'pending'"/>
        </summary>
    </report-view>
    
    <!-- تقرير أداء المحامين -->
    <report-view name="lawyers-performance" title="تقرير أداء المحامين">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date"/>
            <parameter name="date_to" title="إلى تاريخ" type="date"/>
            <parameter name="lawyer_id" title="المحامي" type="lookup" 
                      lookup-table="employees" lookup-field="name" 
                      lookup-filter="position LIKE '%محامي%' OR position LIKE '%قانوني%'"/>
        </parameters>
        
        <data-source>
            SELECT 
                e.name as lawyer_name,
                COUNT(DISTINCT cd.issue_id) as total_cases,
                COUNT(f.id) as total_follows,
                SUM(CASE WHEN f.status = 'completed' THEN 1 ELSE 0 END) as completed_follows,
                SUM(CASE WHEN f.status = 'pending' THEN 1 ELSE 0 END) as pending_follows,
                AVG(i.amount) as avg_case_value,
                SUM(i.amount) as total_case_value
            FROM employees e
            LEFT JOIN service_distributions sd ON e.id = sd.lawyer_id
            LEFT JOIN case_distribution cd ON sd.case_distribution_id = cd.id
            LEFT JOIN issues i ON cd.issue_id = i.id
            LEFT JOIN follows f ON i.id = f.case_id AND f.user_id = e.id
            WHERE (e.position LIKE '%محامي%' OR e.position LIKE '%قانوني%')
              AND (@date_from IS NULL OR cd.distribution_date >= @date_from)
              AND (@date_to IS NULL OR cd.distribution_date <= @date_to)
              AND (@lawyer_id IS NULL OR e.id = @lawyer_id)
            GROUP BY e.id, e.name
            HAVING COUNT(DISTINCT cd.issue_id) > 0
            ORDER BY total_cases DESC, completed_follows DESC
        </data-source>
        
        <columns>
            <column name="lawyer_name" title="اسم المحامي" width="200"/>
            <column name="total_cases" title="إجمالي القضايا" width="100" format="number"/>
            <column name="total_follows" title="إجمالي المتابعات" width="120" format="number"/>
            <column name="completed_follows" title="المتابعات المكتملة" width="130" format="number"/>
            <column name="pending_follows" title="المتابعات المعلقة" width="130" format="number"/>
            <column name="avg_case_value" title="متوسط قيمة القضية" width="150" format="currency"/>
            <column name="total_case_value" title="إجمالي قيمة القضايا" width="150" format="currency"/>
        </columns>
        
        <summary>
            <field name="total_lawyers" title="إجمالي المحامين" type="count"/>
            <field name="total_all_cases" title="إجمالي جميع القضايا" type="sum" column="total_cases"/>
            <field name="total_all_follows" title="إجمالي جميع المتابعات" type="sum" column="total_follows"/>
            <field name="grand_total_value" title="إجمالي قيمة جميع القضايا" type="sum" column="total_case_value" format="currency"/>
        </summary>
    </report-view>
</form-module>
