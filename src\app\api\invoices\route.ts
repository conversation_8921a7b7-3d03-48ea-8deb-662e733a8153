import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب الفواتير مع إمكانية التصفية
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('client_id')
    const status = searchParams.get('status')
    const paymentStatus = searchParams.get('payment_status')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['1=1']
    let queryParams: any[] = []
    let paramIndex = 1

    // تصفية حسب العميل
    if (clientId) {
      whereConditions.push(`i.client_id = $${paramIndex}`)
      queryParams.push(parseInt(clientId))
      paramIndex++
    }

    // تصفية حسب الحالة
    if (status) {
      whereConditions.push(`i.status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    // تصفية حسب حالة الدفع
    if (paymentStatus) {
      whereConditions.push(`i.payment_status = $${paramIndex}`)
      queryParams.push(paymentStatus)
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (startDate) {
      whereConditions.push(`i.invoice_date >= $${paramIndex}`)
      queryParams.push(startDate)
      paramIndex++
    }

    if (endDate) {
      whereConditions.push(`i.invoice_date <= $${paramIndex}`)
      queryParams.push(endDate)
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    // الاستعلام الرئيسي
    const invoicesQuery = `
      SELECT 
        i.*,
        c.name as client_name,
        c.phone as client_phone,
        c.email as client_email,
        u.username as created_by_name,
        COUNT(*) OVER() as total_count
      FROM invoices i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN users u ON i.created_by = u.id
      WHERE ${whereClause}
      ORDER BY i.created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(invoicesQuery, queryParams)

    // جلب عناصر الفواتير
    const invoiceIds = result.rows.map(row => row.id)
    let invoiceItems = []

    if (invoiceIds.length > 0) {
      const itemsQuery = `
        SELECT 
          ii.*,
          te.task_description,
          te.start_time,
          te.duration_minutes,
          i.title as case_title,
          i.case_number
        FROM invoice_items ii
        LEFT JOIN time_entries te ON ii.time_entry_id = te.id
        LEFT JOIN issues i ON ii.case_id = i.id
        WHERE ii.invoice_id = ANY($1)
        ORDER BY ii.invoice_id, ii.id
      `
      const itemsResult = await query(itemsQuery, [invoiceIds])
      invoiceItems = itemsResult.rows
    }

    // دمج البيانات
    const invoicesWithItems = result.rows.map(invoice => ({
      ...invoice,
      items: invoiceItems.filter(item => item.invoice_id === invoice.id)
    }))

    // حساب الإحصائيات
    const statsQuery = `
      SELECT 
        COUNT(*) as total_invoices,
        SUM(total_amount) as total_amount,
        SUM(paid_amount) as paid_amount,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN payment_status = 'overdue' THEN 1 END) as overdue_count
      FROM invoices i
      WHERE ${whereClause}
    `

    const statsResult = await query(statsQuery, queryParams.slice(0, -2)) // إزالة limit و offset

    return NextResponse.json({
      success: true,
      data: {
        invoices: invoicesWithItems,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        statistics: statsResult.rows[0]
      }
    })

  } catch (error) {
    console.error('خطأ في جلب الفواتير:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الفواتير' },
      { status: 500 }
    )
  }
}

// POST - إنشاء فاتورة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      clientId,
      invoiceDate,
      dueDate,
      taxRate,
      discountAmount,
      notes,
      termsConditions,
      items,
      createdBy,
      autoGenerate // إنشاء تلقائي من تسجيلات الوقت
    } = body

    // التحقق من البيانات المطلوبة
    if (!clientId) {
      return NextResponse.json(
        { success: false, error: 'معرف العميل مطلوب' },
        { status: 400 }
      )
    }

    // جلب بيانات العميل
    const clientResult = await query('SELECT * FROM clients WHERE id = $1', [clientId])
    if (clientResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العميل غير موجود' },
        { status: 404 }
      )
    }

    const client = clientResult.rows[0]

    // إنشاء رقم الفاتورة
    const invoiceNumberResult = await query(`
      SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 4) AS INTEGER)), 0) + 1 as next_number
      FROM invoices 
      WHERE invoice_number LIKE 'INV%'
    `)
    const nextNumber = invoiceNumberResult.rows[0].next_number
    const invoiceNumber = `INV${nextNumber.toString().padStart(6, '0')}`

    let invoiceItems = items || []

    // إذا كان الإنشاء تلقائي، جلب تسجيلات الوقت غير المفوترة
    if (autoGenerate) {
      const timeEntriesResult = await query(`
        SELECT * FROM time_entries 
        WHERE client_id = $1 AND is_billable = true AND is_billed = false
        ORDER BY start_time DESC
      `, [clientId])

      invoiceItems = timeEntriesResult.rows.map(entry => ({
        description: entry.task_description,
        quantity: entry.duration_minutes / 60, // تحويل إلى ساعات
        unit_price: entry.hourly_rate,
        total_price: entry.billable_amount,
        time_entry_id: entry.id,
        case_id: entry.case_id,
        item_type: 'service'
      }))
    }

    // حساب المجاميع
    const subtotal = invoiceItems.reduce((sum: number, item: any) => sum + (item.total_price || 0), 0)
    const taxAmount = subtotal * ((taxRate || 0) / 100)
    const totalAmount = subtotal + taxAmount - (discountAmount || 0)

    // إنشاء الفاتورة
    const insertInvoiceQuery = `
      INSERT INTO invoices (
        invoice_number, client_id, client_name, client_address,
        invoice_date, due_date, subtotal, tax_rate, tax_amount,
        discount_amount, total_amount, notes, terms_conditions, created_by
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
      ) RETURNING *
    `

    const invoiceValues = [
      invoiceNumber,
      clientId,
      client.name,
      client.address,
      invoiceDate || new Date().toISOString().split('T')[0],
      dueDate,
      subtotal,
      taxRate || 0,
      taxAmount,
      discountAmount || 0,
      totalAmount,
      notes,
      termsConditions,
      createdBy
    ]

    const invoiceResult = await query(insertInvoiceQuery, invoiceValues)
    const invoice = invoiceResult.rows[0]

    // إضافة عناصر الفاتورة
    for (const item of invoiceItems) {
      await query(`
        INSERT INTO invoice_items (
          invoice_id, description, quantity, unit_price, total_price,
          time_entry_id, case_id, item_type
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        invoice.id,
        item.description,
        item.quantity,
        item.unit_price,
        item.total_price,
        item.time_entry_id || null,
        item.case_id || null,
        item.item_type || 'service'
      ])

      // تحديث تسجيلات الوقت كمفوترة
      if (item.time_entry_id) {
        await query(`
          UPDATE time_entries 
          SET is_billed = true 
          WHERE id = $1
        `, [item.time_entry_id])
      }
    }

    // إنشاء إشعار للعميل
    await query(`
      INSERT INTO client_notifications (client_id, title, message, type)
      VALUES ($1, $2, $3, $4)
    `, [
      clientId,
      'فاتورة جديدة',
      `تم إنشاء فاتورة جديدة رقم ${invoiceNumber} بمبلغ ${totalAmount.toFixed(2)} ريال`,
      'info'
    ])

    return NextResponse.json({
      success: true,
      data: invoice,
      message: 'تم إنشاء الفاتورة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء الفاتورة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الفاتورة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث فاتورة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      status,
      paymentStatus,
      paidAmount,
      paymentDate,
      notes
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      )
    }

    const updateQuery = `
      UPDATE invoices 
      SET 
        status = COALESCE($2, status),
        payment_status = COALESCE($3, payment_status),
        paid_amount = COALESCE($4, paid_amount),
        payment_date = COALESCE($5, payment_date),
        notes = COALESCE($6, notes),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `

    const values = [id, status, paymentStatus, paidAmount, paymentDate, notes]
    const result = await query(updateQuery, values)

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الفاتورة غير موجودة' },
        { status: 404 }
      )
    }

    const invoice = result.rows[0]

    // إنشاء إشعار للعميل عند تغيير الحالة
    if (status || paymentStatus) {
      let notificationMessage = ''
      if (paymentStatus === 'paid') {
        notificationMessage = `تم استلام دفعة بمبلغ ${paidAmount} ريال للفاتورة رقم ${invoice.invoice_number}`
      } else if (status === 'sent') {
        notificationMessage = `تم إرسال الفاتورة رقم ${invoice.invoice_number}`
      }

      if (notificationMessage) {
        await query(`
          INSERT INTO client_notifications (client_id, title, message, type)
          VALUES ($1, $2, $3, $4)
        `, [
          invoice.client_id,
          'تحديث الفاتورة',
          notificationMessage,
          'info'
        ])
      }
    }

    return NextResponse.json({
      success: true,
      data: invoice,
      message: 'تم تحديث الفاتورة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الفاتورة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الفاتورة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف فاتورة (فقط المسودات)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن الفاتورة مسودة
    const checkQuery = 'SELECT status FROM invoices WHERE id = $1'
    const checkResult = await query(checkQuery, [parseInt(id)])

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الفاتورة غير موجودة' },
        { status: 404 }
      )
    }

    if (checkResult.rows[0].status !== 'draft') {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف فاتورة تم إرسالها' },
        { status: 400 }
      )
    }

    // إعادة تعيين تسجيلات الوقت كغير مفوترة
    await query(`
      UPDATE time_entries 
      SET is_billed = false 
      WHERE id IN (
        SELECT time_entry_id 
        FROM invoice_items 
        WHERE invoice_id = $1 AND time_entry_id IS NOT NULL
      )
    `, [parseInt(id)])

    // حذف عناصر الفاتورة
    await query('DELETE FROM invoice_items WHERE invoice_id = $1', [parseInt(id)])

    // حذف الفاتورة
    const deleteQuery = 'DELETE FROM invoices WHERE id = $1 RETURNING invoice_number'
    const result = await query(deleteQuery, [parseInt(id)])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الفاتورة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الفاتورة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الفاتورة' },
      { status: 500 }
    )
  }
}