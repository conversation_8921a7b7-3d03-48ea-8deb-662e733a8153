const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function applyExistingAccountLinking() {
  const client = await pool.connect();
  
  try {
    console.log('🔗 تطبيق الربط التلقائي على الحسابات الموجودة...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // جلب جميع الحسابات الرئيسية المربوطة بدليل الحسابات
    const linkedAccounts = await client.query(`
      SELECT 
        ma.id as main_account_id,
        ma.account_name,
        ma.chart_account_id,
        coa.account_code,
        coa.account_name as chart_account_name
      FROM main_accounts ma
      JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      WHERE ma.chart_account_id IS NOT NULL
      ORDER BY ma.account_name
    `);

    console.log(`📋 تم العثور على ${linkedAccounts.rows.length} حساب رئيسي مربوط`);

    let totalClientsUpdated = 0;
    let totalEmployeesUpdated = 0;
    let totalSuppliersUpdated = 0;

    for (const account of linkedAccounts.rows) {
      console.log(`\n🔗 معالجة الحساب: ${account.account_name} -> ${account.account_code}`);

      switch (account.account_name) {
        case 'حسابات العملاء':
          const clientsUpdated = await linkClientsToAccount(client, account.chart_account_id, account.account_code);
          totalClientsUpdated += clientsUpdated;
          break;
          
        case 'حسابات الموظفين':
          const employeesUpdated = await linkEmployeesToAccount(client, account.chart_account_id, account.account_code);
          totalEmployeesUpdated += employeesUpdated;
          break;
          
        case 'حسابات الموردين':
          const suppliersUpdated = await linkSuppliersToAccount(client, account.chart_account_id, account.account_code);
          totalSuppliersUpdated += suppliersUpdated;
          break;
          
        default:
          console.log(`⚠️ نوع حساب غير مدعوم: ${account.account_name}`);
          break;
      }
    }

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم تطبيق الربط التلقائي بنجاح!');
    console.log('================================');
    console.log(`👥 العملاء المربوطين: ${totalClientsUpdated}`);
    console.log(`👨‍💼 الموظفين المربوطين: ${totalEmployeesUpdated}`);
    console.log(`🏪 الموردين المربوطين: ${totalSuppliersUpdated}`);
    console.log(`📊 إجمالي السجلات المربوطة: ${totalClientsUpdated + totalEmployeesUpdated + totalSuppliersUpdated}`);

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تطبيق الربط التلقائي:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

async function linkClientsToAccount(client, parentAccountId, parentAccountCode) {
  try {
    const clients = await client.query(`
      SELECT id, name 
      FROM clients 
      WHERE status = 'active'
      ORDER BY id
    `);

    let updatedCount = 0;

    for (const clientRecord of clients.rows) {
      try {
        const subAccountCode = `${parentAccountCode}${String(clientRecord.id).padStart(3, '0')}`;
        
        let subAccountResult = await client.query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [subAccountCode]);

        let subAccountId;

        if (subAccountResult.rows.length === 0) {
          const newAccountResult = await client.query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'أصول', 'مدين', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `, [
            subAccountCode,
            `حساب العميل: ${clientRecord.name}`,
            parentAccountId
          ]);
          
          subAccountId = newAccountResult.rows[0].id;
          console.log(`  ✅ تم إنشاء حساب فرعي: ${subAccountCode} - ${clientRecord.name}`);
        } else {
          subAccountId = subAccountResult.rows[0].id;
          console.log(`  🔄 تم العثور على حساب فرعي: ${subAccountCode} - ${clientRecord.name}`);
        }

        await client.query(`
          UPDATE clients 
          SET account_id = $1
          WHERE id = $2
        `, [subAccountId, clientRecord.id]);

        updatedCount++;

      } catch (error) {
        console.error(`  ❌ خطأ في ربط العميل ${clientRecord.name}:`, error.message);
      }
    }

    console.log(`  ✅ تم ربط ${updatedCount} عميل`);
    return updatedCount;

  } catch (error) {
    console.error('خطأ في ربط العملاء:', error.message);
    return 0;
  }
}

async function linkEmployeesToAccount(client, parentAccountId, parentAccountCode) {
  try {
    const employees = await client.query(`
      SELECT id, name 
      FROM employees 
      WHERE status = 'active'
      ORDER BY id
    `);

    let updatedCount = 0;

    for (const employee of employees.rows) {
      try {
        const subAccountCode = `${parentAccountCode}${String(employee.id).padStart(3, '0')}`;
        
        let subAccountResult = await client.query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [subAccountCode]);

        let subAccountId;

        if (subAccountResult.rows.length === 0) {
          const newAccountResult = await client.query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'مصروفات', 'مدين', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `, [
            subAccountCode,
            `حساب الموظف: ${employee.name}`,
            parentAccountId
          ]);
          
          subAccountId = newAccountResult.rows[0].id;
          console.log(`  ✅ تم إنشاء حساب فرعي: ${subAccountCode} - ${employee.name}`);
        } else {
          subAccountId = subAccountResult.rows[0].id;
          console.log(`  🔄 تم العثور على حساب فرعي: ${subAccountCode} - ${employee.name}`);
        }

        await client.query(`
          UPDATE employees 
          SET account_id = $1
          WHERE id = $2
        `, [subAccountId, employee.id]);

        updatedCount++;

      } catch (error) {
        console.error(`  ❌ خطأ في ربط الموظف ${employee.name}:`, error.message);
      }
    }

    console.log(`  ✅ تم ربط ${updatedCount} موظف`);
    return updatedCount;

  } catch (error) {
    console.error('خطأ في ربط الموظفين:', error.message);
    return 0;
  }
}

async function linkSuppliersToAccount(client, parentAccountId, parentAccountCode) {
  try {
    // التحقق من وجود جدول الموردين
    const suppliersTableCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `);

    if (suppliersTableCheck.rows.length === 0) {
      console.log('  ⚠️ جدول الموردين غير موجود');
      return 0;
    }

    const suppliers = await client.query(`
      SELECT id, name 
      FROM suppliers 
      WHERE status = 'active'
      ORDER BY id
    `);

    let updatedCount = 0;

    for (const supplier of suppliers.rows) {
      try {
        const subAccountCode = `${parentAccountCode}${String(supplier.id).padStart(3, '0')}`;
        
        let subAccountResult = await client.query(`
          SELECT id FROM chart_of_accounts WHERE account_code = $1
        `, [subAccountCode]);

        let subAccountId;

        if (subAccountResult.rows.length === 0) {
          const newAccountResult = await client.query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, account_nature, 
              account_level, parent_id, allow_transactions, is_active, created_date
            ) VALUES ($1, $2, 'خصوم', 'دائن', 5, $3, true, true, CURRENT_DATE)
            RETURNING id
          `, [
            subAccountCode,
            `حساب المورد: ${supplier.name}`,
            parentAccountId
          ]);
          
          subAccountId = newAccountResult.rows[0].id;
          console.log(`  ✅ تم إنشاء حساب فرعي: ${subAccountCode} - ${supplier.name}`);
        } else {
          subAccountId = subAccountResult.rows[0].id;
          console.log(`  🔄 تم العثور على حساب فرعي: ${subAccountCode} - ${supplier.name}`);
        }

        await client.query(`
          UPDATE suppliers 
          SET account_id = $1
          WHERE id = $2
        `, [subAccountId, supplier.id]);

        updatedCount++;

      } catch (error) {
        console.error(`  ❌ خطأ في ربط المورد ${supplier.name}:`, error.message);
      }
    }

    console.log(`  ✅ تم ربط ${updatedCount} مورد`);
    return updatedCount;

  } catch (error) {
    console.error('خطأ في ربط الموردين:', error.message);
    return 0;
  }
}

applyExistingAccountLinking().catch(console.error);
