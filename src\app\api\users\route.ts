import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع المستخدمين
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    // جلب المستخدمين مع بيانات الموظفين والأدوار
    const usersResult = await query(`
      SELECT 
        u.id, u.username, u.employee_id, u.role, u.user_type, u.permissions, 
        u.status, u.is_active, u.last_login, u.created_date,
        e.name, e.position, e.department, e.phone, e.email,
        ur.display_name as role_display_name
      FROM users u
      LEFT JOIN employees e ON u.employee_id = e.id
      LEFT JOIN user_roles ur ON u.role = ur.role_name
      ORDER BY u.user_type DESC, u.role, u.username
    `)

    return NextResponse.json({
      success: true,
      data: usersResult.rows
    })

  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب المستخدمين' },
      { status: 500 }
    )
  }
}

// POST - إضافة مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { username, password, employee_id, role, user_type, permissions, is_active } = body

    // التحقق من البيانات المطلوبة
    if (!username || !password || !role || !user_type) {
      return NextResponse.json(
        { success: false, error: 'جميع الحقول المطلوبة يجب ملؤها' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    const existingUser = await query(
      'SELECT id FROM users WHERE username = $1',
      [username]
    )

    if (existingUser.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم موجود بالفعل' },
        { status: 400 }
      )
    }

    // إضافة المستخدم الجديد
    const result = await query(`
      INSERT INTO users (username, password_hash, employee_id, role, user_type, permissions, is_active, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, 'active')
      RETURNING id, username, role, user_type, permissions, is_active
    `, [
      username,
      password, // في الإنتاج يجب تشفير كلمة المرور
      employee_id || null,
      role,
      user_type,
      permissions || [],
      is_active !== false
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستخدم بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المستخدم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, username, email, role, status } = body

    if (!id || !username || !email) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم المستخدم والبريد الإلكتروني مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE users
      SET username = $1, email = $2, role = $3, status = $4,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING id, username, email, role, status, created_at
    `, [username, email, role, status, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المستخدم بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات المستخدم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مستخدم
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM users WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المستخدم' },
      { status: 500 }
    )
  }
}
