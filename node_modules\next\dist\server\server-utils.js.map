{"version": 3, "sources": ["../../src/server/server-utils.ts"], "sourcesContent": ["import type { Rewrite } from '../lib/load-custom-routes'\nimport type { RouteMatchFn } from '../shared/lib/router/utils/route-matcher'\nimport type { NextConfig } from './config'\nimport type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\n\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { getPathMatch } from '../shared/lib/router/utils/path-match'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { normalizeRscURL } from '../shared/lib/router/utils/app-paths'\nimport {\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../lib/constants'\nimport { normalizeNextQueryParam } from './web/utils'\nimport type { IncomingHttpHeaders, IncomingMessage } from 'http'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { parseReqUrl } from '../lib/url'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport { parseAndValidateFlightRouterState } from './app-render/parse-and-validate-flight-router-state'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport { NEXT_ROUTER_STATE_TREE_HEADER } from '../client/components/app-router-headers'\nimport { getSelectedParams } from '../client/components/router-reducer/compute-changed-path'\n\nfunction filterInternalQuery(\n  query: Record<string, undefined | string | string[]>,\n  paramKeys: string[]\n) {\n  // this is used to pass query information in rewrites\n  // but should not be exposed in final query\n  delete query['nextInternalLocale']\n\n  for (const key in query) {\n    const isNextQueryPrefix =\n      key !== NEXT_QUERY_PARAM_PREFIX && key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n\n    const isNextInterceptionMarkerPrefix =\n      key !== NEXT_INTERCEPTION_MARKER_PREFIX &&\n      key.startsWith(NEXT_INTERCEPTION_MARKER_PREFIX)\n\n    if (\n      isNextQueryPrefix ||\n      isNextInterceptionMarkerPrefix ||\n      paramKeys.includes(key)\n    ) {\n      delete query[key]\n    }\n  }\n}\n\nexport function normalizeCdnUrl(\n  req: BaseNextRequest | IncomingMessage,\n  paramKeys: string[]\n) {\n  // make sure to normalize req.url from CDNs to strip dynamic and rewrite\n  // params from the query which are added during routing\n  const _parsedUrl = parseReqUrl(req.url!)\n\n  // we can't normalize if we can't parse\n  if (!_parsedUrl) {\n    return req.url\n  }\n  delete (_parsedUrl as any).search\n  filterInternalQuery(_parsedUrl.query, paramKeys)\n\n  req.url = formatUrl(_parsedUrl)\n}\n\nexport function interpolateDynamicPath(\n  pathname: string,\n  params: ParsedUrlQuery,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  if (!defaultRouteRegex) return pathname\n\n  for (const param of Object.keys(defaultRouteRegex.groups)) {\n    const { optional, repeat } = defaultRouteRegex.groups[param]\n    let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n    if (optional) {\n      builtParam = `[${builtParam}]`\n    }\n\n    let paramValue: string\n    const value = params[param]\n\n    if (Array.isArray(value)) {\n      paramValue = value.map((v) => v && encodeURIComponent(v)).join('/')\n    } else if (value) {\n      paramValue = encodeURIComponent(value)\n    } else {\n      paramValue = ''\n    }\n\n    if (paramValue || optional) {\n      pathname = pathname.replaceAll(builtParam, paramValue)\n    }\n  }\n\n  return pathname\n}\n\nexport function normalizeDynamicRouteParams(\n  query: ParsedUrlQuery,\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex>,\n  defaultRouteMatches: ParsedUrlQuery,\n  ignoreMissingOptional: boolean\n) {\n  let hasValidParams = true\n  let params: ParsedUrlQuery = {}\n\n  for (const key of Object.keys(defaultRouteRegex.groups)) {\n    let value: string | string[] | undefined = query[key]\n\n    if (typeof value === 'string') {\n      value = normalizeRscURL(value)\n    } else if (Array.isArray(value)) {\n      value = value.map(normalizeRscURL)\n    }\n\n    // if the value matches the default value we can't rely\n    // on the parsed params, this is used to signal if we need\n    // to parse x-now-route-matches or not\n    const defaultValue = defaultRouteMatches![key]\n    const isOptional = defaultRouteRegex!.groups[key].optional\n\n    const isDefaultValue = Array.isArray(defaultValue)\n      ? defaultValue.some((defaultVal) => {\n          return Array.isArray(value)\n            ? value.some((val) => val.includes(defaultVal))\n            : value?.includes(defaultVal)\n        })\n      : value?.includes(defaultValue as string)\n\n    if (\n      isDefaultValue ||\n      (typeof value === 'undefined' && !(isOptional && ignoreMissingOptional))\n    ) {\n      return { params: {}, hasValidParams: false }\n    }\n\n    // non-provided optional values should be undefined so normalize\n    // them to undefined\n    if (\n      isOptional &&\n      (!value ||\n        (Array.isArray(value) &&\n          value.length === 1 &&\n          // fallback optional catch-all SSG pages have\n          // [[...paramName]] for the root path on Vercel\n          (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n    ) {\n      value = undefined\n      delete query[key]\n    }\n\n    // query values from the proxy aren't already split into arrays\n    // so make sure to normalize catch-all values\n    if (\n      value &&\n      typeof value === 'string' &&\n      defaultRouteRegex!.groups[key].repeat\n    ) {\n      value = value.split('/')\n    }\n\n    if (value) {\n      params[key] = value\n    }\n  }\n\n  return {\n    params,\n    hasValidParams,\n  }\n}\n\nexport function getServerUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n  trailingSlash,\n  caseSensitive,\n}: {\n  page: string\n  i18n?: NextConfig['i18n']\n  basePath: string\n  rewrites: DeepReadonly<{\n    fallback?: ReadonlyArray<Rewrite>\n    afterFiles?: ReadonlyArray<Rewrite>\n    beforeFiles?: ReadonlyArray<Rewrite>\n  }>\n  pageIsDynamic: boolean\n  trailingSlash?: boolean\n  caseSensitive: boolean\n}) {\n  let defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n  let dynamicRouteMatcher: RouteMatchFn | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getNamedRouteRegex(page, {\n      prefixRouteKeys: false,\n    })\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(\n    req: BaseNextRequest | IncomingMessage,\n    parsedUrl: UrlWithParsedQuery\n  ) {\n    const rewriteParams: Record<string, string> = {}\n    let fsPathname = parsedUrl.pathname\n\n    const matchesPage = () => {\n      const fsPathnameNoSlash = removeTrailingSlash(fsPathname || '')\n      return (\n        fsPathnameNoSlash === removeTrailingSlash(page) ||\n        dynamicRouteMatcher?.(fsPathnameNoSlash)\n      )\n    }\n\n    const checkRewrite = (rewrite: DeepReadonly<Rewrite>): boolean => {\n      const matcher = getPathMatch(\n        rewrite.source + (trailingSlash ? '(/)?' : ''),\n        {\n          removeUnnamedParams: true,\n          strict: true,\n          sensitive: !!caseSensitive,\n        }\n      )\n\n      if (!parsedUrl.pathname) return false\n\n      let params = matcher(parsedUrl.pathname)\n\n      if ((rewrite.has || rewrite.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          rewrite.has as Rewrite['has'],\n          rewrite.missing as Rewrite['missing']\n        )\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        try {\n          // An interception rewrite might reference a dynamic param for a route the user\n          // is currently on, which wouldn't be extractable from the matched route params.\n          // This attempts to extract the dynamic params from the provided router state.\n          if (isInterceptionRouteRewrite(rewrite as Rewrite)) {\n            const stateHeader = req.headers[NEXT_ROUTER_STATE_TREE_HEADER]\n\n            if (stateHeader) {\n              params = {\n                ...getSelectedParams(\n                  parseAndValidateFlightRouterState(stateHeader)\n                ),\n                ...params,\n              }\n            }\n          }\n        } catch (err) {\n          // this is a no-op -- we couldn't extract dynamic params from the provided router state,\n          // so we'll just use the params from the route matcher\n        }\n\n        const { parsedDestination, destQuery } = prepareDestination({\n          appendParamsToQuery: true,\n          destination: rewrite.destination,\n          params: params,\n          query: parsedUrl.query,\n        })\n\n        // if the rewrite destination is external break rewrite chain\n        if (parsedDestination.protocol) {\n          return true\n        }\n\n        Object.assign(rewriteParams, destQuery, params)\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        // for each property in parsedUrl.query, if the value is parametrized (eg :foo), look up the value\n        // in rewriteParams and replace the parametrized value with the actual value\n        // this is used when the rewrite destination does not contain the original source param\n        // and so the value is still parametrized and needs to be replaced with the actual rewrite param\n        Object.entries(parsedUrl.query).forEach(([key, value]) => {\n          if (value && typeof value === 'string' && value.startsWith(':')) {\n            const paramName = value.slice(1)\n            const actualValue = rewriteParams[paramName]\n            if (actualValue) {\n              parsedUrl.query[key] = actualValue\n            }\n          }\n        })\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        fsPathname = parsedUrl.pathname\n        if (!fsPathname) return false\n\n        if (basePath) {\n          fsPathname = fsPathname.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const result = normalizeLocalePath(fsPathname, i18n.locales)\n          fsPathname = result.pathname\n          parsedUrl.query.nextInternalLocale =\n            result.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          return true\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            return true\n          }\n        }\n      }\n      return false\n    }\n\n    for (const rewrite of rewrites.beforeFiles || []) {\n      checkRewrite(rewrite)\n    }\n\n    if (fsPathname !== page) {\n      let finished = false\n\n      for (const rewrite of rewrites.afterFiles || []) {\n        finished = checkRewrite(rewrite)\n        if (finished) break\n      }\n\n      if (!finished && !matchesPage()) {\n        for (const rewrite of rewrites.fallback || []) {\n          finished = checkRewrite(rewrite)\n          if (finished) break\n        }\n      }\n    }\n    return rewriteParams\n  }\n\n  function getParamsFromRouteMatches(routeMatchesHeader: string) {\n    // If we don't have a default route regex, we can't get params from route\n    // matches\n    if (!defaultRouteRegex) return null\n\n    const { groups, routeKeys } = defaultRouteRegex\n\n    const matcher = getRouteMatcher({\n      re: {\n        // Simulate a RegExp match from the \\`req.url\\` input\n        exec: (str: string) => {\n          // Normalize all the prefixed query params.\n          const obj: Record<string, string> = Object.fromEntries(\n            new URLSearchParams(str)\n          )\n          for (const [key, value] of Object.entries(obj)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            obj[normalizedKey] = value\n            delete obj[key]\n          }\n\n          // Use all the named route keys.\n          const result = {} as RegExpExecArray\n          for (const keyName of Object.keys(routeKeys)) {\n            const paramName = routeKeys[keyName]\n\n            // If this param name is not a valid parameter name, then skip it.\n            if (!paramName) continue\n\n            const group = groups[paramName]\n            const value = obj[keyName]\n\n            // When we're missing a required param, we can't match the route.\n            if (!group.optional && !value) return null\n\n            result[group.pos] = value\n          }\n\n          return result\n        },\n      },\n      groups,\n    })\n\n    const routeMatches = matcher(routeMatchesHeader)\n    if (!routeMatches) return null\n\n    return routeMatches\n  }\n\n  function normalizeQueryParams(\n    query: Record<string, string | string[] | undefined>,\n    routeParamKeys: Set<string>\n  ) {\n    // this is used to pass query information in rewrites\n    // but should not be exposed in final query\n    delete query['nextInternalLocale']\n\n    for (const [key, value] of Object.entries(query)) {\n      const normalizedKey = normalizeNextQueryParam(key)\n      if (!normalizedKey) continue\n\n      // Remove the prefixed key from the query params because we want\n      // to consume it for the dynamic route matcher.\n      delete query[key]\n      routeParamKeys.add(normalizedKey)\n\n      if (typeof value === 'undefined') continue\n\n      query[normalizedKey] = Array.isArray(value)\n        ? value.map((v) => decodeQueryPathParameter(v))\n        : decodeQueryPathParameter(value)\n    }\n  }\n\n  return {\n    handleRewrites,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    normalizeQueryParams,\n    getParamsFromRouteMatches,\n    /**\n     * Normalize dynamic route params.\n     *\n     * @param query - The query params to normalize.\n     * @param ignoreMissingOptional - Whether to ignore missing optional params.\n     * @returns The normalized params and whether they are valid.\n     */\n    normalizeDynamicRouteParams: (\n      query: ParsedUrlQuery,\n      ignoreMissingOptional: boolean\n    ) => {\n      if (!defaultRouteRegex || !defaultRouteMatches) {\n        return { params: {}, hasValidParams: false }\n      }\n\n      return normalizeDynamicRouteParams(\n        query,\n        defaultRouteRegex,\n        defaultRouteMatches,\n        ignoreMissingOptional\n      )\n    },\n\n    normalizeCdnUrl: (\n      req: BaseNextRequest | IncomingMessage,\n      paramKeys: string[]\n    ) => normalizeCdnUrl(req, paramKeys),\n\n    interpolateDynamicPath: (\n      pathname: string,\n      params: Record<string, undefined | string | string[]>\n    ) => interpolateDynamicPath(pathname, params, defaultRouteRegex),\n\n    filterInternalQuery: (query: ParsedUrlQuery, paramKeys: string[]) =>\n      filterInternalQuery(query, paramKeys),\n  }\n}\n\nexport function getPreviouslyRevalidatedTags(\n  headers: IncomingHttpHeaders,\n  previewModeId: string | undefined\n): string[] {\n  return typeof headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' &&\n    headers[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] === previewModeId\n    ? headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',')\n    : []\n}\n"], "names": ["getPreviouslyRevalidatedTags", "getServerUtils", "interpolateDynamicPath", "normalizeCdnUrl", "normalizeDynamicRouteParams", "filterInternalQuery", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "isNextQueryPrefix", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "isNextInterceptionMarkerPrefix", "NEXT_INTERCEPTION_MARKER_PREFIX", "includes", "req", "_parsedUrl", "parseReqUrl", "url", "search", "formatUrl", "pathname", "params", "defaultRouteRegex", "param", "Object", "keys", "groups", "optional", "repeat", "builtParam", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "replaceAll", "defaultRouteMatches", "ignoreMissingOptional", "hasValidParams", "normalizeRscURL", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "val", "length", "undefined", "split", "page", "i18n", "basePath", "rewrites", "pageIsDynamic", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "getNamedRouteRegex", "prefixRouteKeys", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "isInterceptionRouteRewrite", "<PERSON><PERSON><PERSON><PERSON>", "headers", "NEXT_ROUTER_STATE_TREE_HEADER", "getSelectedParams", "parseAndValidateFlightRouterState", "err", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "entries", "for<PERSON>ach", "paramName", "slice", "actualValue", "replace", "RegExp", "result", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "routeMatchesHeader", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "normalizedKey", "normalizeNextQueryParam", "keyName", "group", "pos", "routeMatches", "normalizeQueryParams", "routeParamKeys", "add", "decodeQueryPathParameter", "previewModeId", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;IA8egBA,4BAA4B;eAA5BA;;IAnTAC,cAAc;eAAdA;;IA7GAC,sBAAsB;eAAtBA;;IAlBAC,eAAe;eAAfA;;IAoDAC,2BAA2B;eAA3BA;;;qCAzGoB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BAMzB;uBACiC;0CAEC;qBAEb;2BACF;mDACwB;oDACP;kCACG;oCACZ;AAElC,SAASC,oBACPC,KAAoD,EACpDC,SAAmB;IAEnB,qDAAqD;IACrD,2CAA2C;IAC3C,OAAOD,KAAK,CAAC,qBAAqB;IAElC,IAAK,MAAME,OAAOF,MAAO;QACvB,MAAMG,oBACJD,QAAQE,kCAAuB,IAAIF,IAAIG,UAAU,CAACD,kCAAuB;QAE3E,MAAME,iCACJJ,QAAQK,0CAA+B,IACvCL,IAAIG,UAAU,CAACE,0CAA+B;QAEhD,IACEJ,qBACAG,kCACAL,UAAUO,QAAQ,CAACN,MACnB;YACA,OAAOF,KAAK,CAACE,IAAI;QACnB;IACF;AACF;AAEO,SAASL,gBACdY,GAAsC,EACtCR,SAAmB;IAEnB,wEAAwE;IACxE,uDAAuD;IACvD,MAAMS,aAAaC,IAAAA,gBAAW,EAACF,IAAIG,GAAG;IAEtC,uCAAuC;IACvC,IAAI,CAACF,YAAY;QACf,OAAOD,IAAIG,GAAG;IAChB;IACA,OAAO,AAACF,WAAmBG,MAAM;IACjCd,oBAAoBW,WAAWV,KAAK,EAAEC;IAEtCQ,IAAIG,GAAG,GAAGE,IAAAA,oBAAS,EAACJ;AACtB;AAEO,SAASd,uBACdmB,QAAgB,EAChBC,MAAsB,EACtBC,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOF;IAE/B,KAAK,MAAMG,SAASC,OAAOC,IAAI,CAACH,kBAAkBI,MAAM,EAAG;QACzD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGN,kBAAkBI,MAAM,CAACH,MAAM;QAC5D,IAAIM,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,KAAKL,MAAM,CAAC,CAAC;QAEnD,IAAII,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,IAAIC;QACJ,MAAMC,QAAQV,MAAM,CAACE,MAAM;QAE3B,IAAIS,MAAMC,OAAO,CAACF,QAAQ;YACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;QACjE,OAAO,IAAIN,OAAO;YAChBD,aAAaM,mBAAmBL;QAClC,OAAO;YACLD,aAAa;QACf;QAEA,IAAIA,cAAcH,UAAU;YAC1BP,WAAWA,SAASkB,UAAU,CAACT,YAAYC;QAC7C;IACF;IAEA,OAAOV;AACT;AAEO,SAASjB,4BACdE,KAAqB,EACrBiB,iBAAwD,EACxDiB,mBAAmC,EACnCC,qBAA8B;IAE9B,IAAIC,iBAAiB;IACrB,IAAIpB,SAAyB,CAAC;IAE9B,KAAK,MAAMd,OAAOiB,OAAOC,IAAI,CAACH,kBAAkBI,MAAM,EAAG;QACvD,IAAIK,QAAuC1B,KAAK,CAACE,IAAI;QAErD,IAAI,OAAOwB,UAAU,UAAU;YAC7BA,QAAQW,IAAAA,yBAAe,EAACX;QAC1B,OAAO,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YAC/BA,QAAQA,MAAMG,GAAG,CAACQ,yBAAe;QACnC;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeJ,mBAAoB,CAAChC,IAAI;QAC9C,MAAMqC,aAAatB,kBAAmBI,MAAM,CAACnB,IAAI,CAACoB,QAAQ;QAE1D,MAAMkB,iBAAiBb,MAAMC,OAAO,CAACU,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOf,MAAMC,OAAO,CAACF,SACjBA,MAAMe,IAAI,CAAC,CAACE,MAAQA,IAAInC,QAAQ,CAACkC,eACjChB,yBAAAA,MAAOlB,QAAQ,CAACkC;QACtB,KACAhB,yBAAAA,MAAOlB,QAAQ,CAAC8B;QAEpB,IACEE,kBACC,OAAOd,UAAU,eAAe,CAAEa,CAAAA,cAAcJ,qBAAoB,GACrE;YACA,OAAO;gBAAEnB,QAAQ,CAAC;gBAAGoB,gBAAgB;YAAM;QAC7C;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEG,cACC,CAAA,CAACb,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMkB,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9ClB,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAExB,IAAI,EAAE,CAAC,AAAD,CAAE,GAC1D;YACAwB,QAAQmB;YACR,OAAO7C,KAAK,CAACE,IAAI;QACnB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEwB,SACA,OAAOA,UAAU,YACjBT,kBAAmBI,MAAM,CAACnB,IAAI,CAACqB,MAAM,EACrC;YACAG,QAAQA,MAAMoB,KAAK,CAAC;QACtB;QAEA,IAAIpB,OAAO;YACTV,MAAM,CAACd,IAAI,GAAGwB;QAChB;IACF;IAEA,OAAO;QACLV;QACAoB;IACF;AACF;AAEO,SAASzC,eAAe,EAC7BoD,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,aAAa,EAad;IACC,IAAIpC;IACJ,IAAIqC;IACJ,IAAIpB;IAEJ,IAAIiB,eAAe;QACjBlC,oBAAoBsC,IAAAA,8BAAkB,EAACR,MAAM;YAC3CS,iBAAiB;QACnB;QACAF,sBAAsBG,IAAAA,6BAAe,EAACxC;QACtCiB,sBAAsBoB,oBAAoBP;IAC5C;IAEA,SAASW,eACPjD,GAAsC,EACtCkD,SAA6B;QAE7B,MAAMC,gBAAwC,CAAC;QAC/C,IAAIC,aAAaF,UAAU5C,QAAQ;QAEnC,MAAM+C,cAAc;YAClB,MAAMC,oBAAoBC,IAAAA,wCAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,IAAAA,wCAAmB,EAACjB,UAC1CO,uCAAAA,oBAAsBS;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,IAAAA,uBAAY,EAC1BF,QAAQG,MAAM,GAAIjB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEkB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACnB;YACf;YAGF,IAAI,CAACM,UAAU5C,QAAQ,EAAE,OAAO;YAEhC,IAAIC,SAASmD,QAAQR,UAAU5C,QAAQ;YAEvC,IAAI,AAACmD,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAO,AAAD,KAAM1D,QAAQ;gBAC9C,MAAM2D,YAAYC,IAAAA,4BAAQ,EACxBnE,KACAkD,UAAU3D,KAAK,EACfkE,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACbxD,OAAO0D,MAAM,CAAC7D,QAAQ2D;gBACxB,OAAO;oBACL3D,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,IAAI;oBACF,+EAA+E;oBAC/E,gFAAgF;oBAChF,8EAA8E;oBAC9E,IAAI8D,IAAAA,8DAA0B,EAACZ,UAAqB;wBAClD,MAAMa,cAActE,IAAIuE,OAAO,CAACC,+CAA6B,CAAC;wBAE9D,IAAIF,aAAa;4BACf/D,SAAS;gCACP,GAAGkE,IAAAA,qCAAiB,EAClBC,IAAAA,oEAAiC,EAACJ,aACnC;gCACD,GAAG/D,MAAM;4BACX;wBACF;oBACF;gBACF,EAAE,OAAOoE,KAAK;gBACZ,wFAAwF;gBACxF,sDAAsD;gBACxD;gBAEA,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAavB,QAAQuB,WAAW;oBAChCzE,QAAQA;oBACRhB,OAAO2D,UAAU3D,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIqF,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEAvE,OAAO0D,MAAM,CAACjB,eAAe0B,WAAWtE;gBACxCG,OAAO0D,MAAM,CAAClB,UAAU3D,KAAK,EAAEqF,kBAAkBrF,KAAK;gBACtD,OAAO,AAACqF,kBAA0BrF,KAAK;gBAEvC,kGAAkG;gBAClG,4EAA4E;gBAC5E,uFAAuF;gBACvF,gGAAgG;gBAChGmB,OAAOwE,OAAO,CAAChC,UAAU3D,KAAK,EAAE4F,OAAO,CAAC,CAAC,CAAC1F,KAAKwB,MAAM;oBACnD,IAAIA,SAAS,OAAOA,UAAU,YAAYA,MAAMrB,UAAU,CAAC,MAAM;wBAC/D,MAAMwF,YAAYnE,MAAMoE,KAAK,CAAC;wBAC9B,MAAMC,cAAcnC,aAAa,CAACiC,UAAU;wBAC5C,IAAIE,aAAa;4BACfpC,UAAU3D,KAAK,CAACE,IAAI,GAAG6F;wBACzB;oBACF;gBACF;gBAEA5E,OAAO0D,MAAM,CAAClB,WAAW0B;gBAEzBxB,aAAaF,UAAU5C,QAAQ;gBAC/B,IAAI,CAAC8C,YAAY,OAAO;gBAExB,IAAIZ,UAAU;oBACZY,aAAaA,WAAWmC,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEhD,UAAU,GAAG,OAAO;gBACrE;gBAEA,IAAID,MAAM;oBACR,MAAMkD,SAASC,IAAAA,wCAAmB,EAACtC,YAAYb,KAAKoD,OAAO;oBAC3DvC,aAAaqC,OAAOnF,QAAQ;oBAC5B4C,UAAU3D,KAAK,CAACqG,kBAAkB,GAChCH,OAAOI,cAAc,IAAItF,OAAOqF,kBAAkB;gBACtD;gBAEA,IAAIxC,eAAed,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAII,iBAAiBG,qBAAqB;oBACxC,MAAMiD,gBAAgBjD,oBAAoBO;oBAC1C,IAAI0C,eAAe;wBACjB5C,UAAU3D,KAAK,GAAG;4BAChB,GAAG2D,UAAU3D,KAAK;4BAClB,GAAGuG,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMrC,WAAWhB,SAASsD,WAAW,IAAI,EAAE,CAAE;YAChDvC,aAAaC;QACf;QAEA,IAAIL,eAAed,MAAM;YACvB,IAAI0D,WAAW;YAEf,KAAK,MAAMvC,WAAWhB,SAASwD,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWxC,aAAaC;gBACxB,IAAIuC,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC3C,eAAe;gBAC/B,KAAK,MAAMI,WAAWhB,SAASyD,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWxC,aAAaC;oBACxB,IAAIuC,UAAU;gBAChB;YACF;QACF;QACA,OAAO7C;IACT;IAEA,SAASgD,0BAA0BC,kBAA0B;QAC3D,yEAAyE;QACzE,UAAU;QACV,IAAI,CAAC5F,mBAAmB,OAAO;QAE/B,MAAM,EAAEI,MAAM,EAAEyF,SAAS,EAAE,GAAG7F;QAE9B,MAAMkD,UAAUV,IAAAA,6BAAe,EAAC;YAC9BsD,IAAI;gBACF,qDAAqD;gBACrDC,MAAM,CAACC;oBACL,2CAA2C;oBAC3C,MAAMC,MAA8B/F,OAAOgG,WAAW,CACpD,IAAIC,gBAAgBH;oBAEtB,KAAK,MAAM,CAAC/G,KAAKwB,MAAM,IAAIP,OAAOwE,OAAO,CAACuB,KAAM;wBAC9C,MAAMG,gBAAgBC,IAAAA,8BAAuB,EAACpH;wBAC9C,IAAI,CAACmH,eAAe;wBAEpBH,GAAG,CAACG,cAAc,GAAG3F;wBACrB,OAAOwF,GAAG,CAAChH,IAAI;oBACjB;oBAEA,gCAAgC;oBAChC,MAAMgG,SAAS,CAAC;oBAChB,KAAK,MAAMqB,WAAWpG,OAAOC,IAAI,CAAC0F,WAAY;wBAC5C,MAAMjB,YAAYiB,SAAS,CAACS,QAAQ;wBAEpC,kEAAkE;wBAClE,IAAI,CAAC1B,WAAW;wBAEhB,MAAM2B,QAAQnG,MAAM,CAACwE,UAAU;wBAC/B,MAAMnE,QAAQwF,GAAG,CAACK,QAAQ;wBAE1B,iEAAiE;wBACjE,IAAI,CAACC,MAAMlG,QAAQ,IAAI,CAACI,OAAO,OAAO;wBAEtCwE,MAAM,CAACsB,MAAMC,GAAG,CAAC,GAAG/F;oBACtB;oBAEA,OAAOwE;gBACT;YACF;YACA7E;QACF;QAEA,MAAMqG,eAAevD,QAAQ0C;QAC7B,IAAI,CAACa,cAAc,OAAO;QAE1B,OAAOA;IACT;IAEA,SAASC,qBACP3H,KAAoD,EACpD4H,cAA2B;QAE3B,qDAAqD;QACrD,2CAA2C;QAC3C,OAAO5H,KAAK,CAAC,qBAAqB;QAElC,KAAK,MAAM,CAACE,KAAKwB,MAAM,IAAIP,OAAOwE,OAAO,CAAC3F,OAAQ;YAChD,MAAMqH,gBAAgBC,IAAAA,8BAAuB,EAACpH;YAC9C,IAAI,CAACmH,eAAe;YAEpB,gEAAgE;YAChE,+CAA+C;YAC/C,OAAOrH,KAAK,CAACE,IAAI;YACjB0H,eAAeC,GAAG,CAACR;YAEnB,IAAI,OAAO3F,UAAU,aAAa;YAElC1B,KAAK,CAACqH,cAAc,GAAG1F,MAAMC,OAAO,CAACF,SACjCA,MAAMG,GAAG,CAAC,CAACC,IAAMgG,IAAAA,kDAAwB,EAAChG,MAC1CgG,IAAAA,kDAAwB,EAACpG;QAC/B;IACF;IAEA,OAAO;QACLgC;QACAzC;QACAqC;QACApB;QACAyF;QACAf;QACA;;;;;;KAMC,GACD9G,6BAA6B,CAC3BE,OACAmC;YAEA,IAAI,CAAClB,qBAAqB,CAACiB,qBAAqB;gBAC9C,OAAO;oBAAElB,QAAQ,CAAC;oBAAGoB,gBAAgB;gBAAM;YAC7C;YAEA,OAAOtC,4BACLE,OACAiB,mBACAiB,qBACAC;QAEJ;QAEAtC,iBAAiB,CACfY,KACAR,YACGJ,gBAAgBY,KAAKR;QAE1BL,wBAAwB,CACtBmB,UACAC,SACGpB,uBAAuBmB,UAAUC,QAAQC;QAE9ClB,qBAAqB,CAACC,OAAuBC,YAC3CF,oBAAoBC,OAAOC;IAC/B;AACF;AAEO,SAASP,6BACdsF,OAA4B,EAC5B+C,aAAiC;IAEjC,OAAO,OAAO/C,OAAO,CAACgD,6CAAkC,CAAC,KAAK,YAC5DhD,OAAO,CAACiD,iDAAsC,CAAC,KAAKF,gBAClD/C,OAAO,CAACgD,6CAAkC,CAAC,CAAClF,KAAK,CAAC,OAClD,EAAE;AACR", "ignoreList": [0]}