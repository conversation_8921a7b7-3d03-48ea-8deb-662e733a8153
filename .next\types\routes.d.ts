// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/accounting" | "/accounting/account-linking" | "/accounting/chart-of-accounts" | "/accounting/default-accounts" | "/accounting/link-accounts" | "/accounting/main-accounts" | "/accounting/payment-vouchers" | "/accounting/receipt-vouchers" | "/accounting/reports" | "/accounting/reports/account-statement" | "/branches" | "/case-distribution" | "/case-reports" | "/client-accounts" | "/client-login" | "/client-portal" | "/company" | "/courts" | "/dashboard" | "/employees" | "/follows" | "/governorates" | "/issue-types" | "/issues" | "/issues/new" | "/journal-entries-new" | "/lawyer-earnings" | "/login" | "/movements" | "/percentages" | "/services" | "/settings/announcements" | "/settings/cost-centers" | "/settings/navigation-pages" | "/setup" | "/setup-simple" | "/test-db" | "/time-tracking" | "/under-construction" | "/users"
type AppRouteHandlerRoutes = "/api/accounting/account-linking" | "/api/accounting/account-linking/create-system-accounts" | "/api/accounting/chart-of-accounts" | "/api/accounting/chart-of-accounts/[id]" | "/api/accounting/chart-of-accounts/link" | "/api/accounting/currencies" | "/api/accounting/main-accounts" | "/api/accounting/payment-methods" | "/api/accounting/projects" | "/api/accounting/vouchers" | "/api/auth/users" | "/api/case-distribution" | "/api/chart-of-accounts" | "/api/chart-of-accounts-new" | "/api/chart-of-accounts-new/[id]" | "/api/chart-of-accounts/link-tables" | "/api/chart-of-accounts/main" | "/api/clients" | "/api/courts" | "/api/employees" | "/api/follows" | "/api/follows/service-allocation" | "/api/follows/user-issues" | "/api/hearings" | "/api/issues" | "/api/issues/[id]" | "/api/issues/undistributed" | "/api/journal-entries-new" | "/api/migrate-accounts" | "/api/reset-database" | "/api/services" | "/api/settings/announcements" | "/api/test-connection" | "/api/users" | "/api/users/[id]"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/accounting": {}
  "/accounting/account-linking": {}
  "/accounting/chart-of-accounts": {}
  "/accounting/default-accounts": {}
  "/accounting/link-accounts": {}
  "/accounting/main-accounts": {}
  "/accounting/payment-vouchers": {}
  "/accounting/receipt-vouchers": {}
  "/accounting/reports": {}
  "/accounting/reports/account-statement": {}
  "/api/accounting/account-linking": {}
  "/api/accounting/account-linking/create-system-accounts": {}
  "/api/accounting/chart-of-accounts": {}
  "/api/accounting/chart-of-accounts/[id]": { "id": string; }
  "/api/accounting/chart-of-accounts/link": {}
  "/api/accounting/currencies": {}
  "/api/accounting/main-accounts": {}
  "/api/accounting/payment-methods": {}
  "/api/accounting/projects": {}
  "/api/accounting/vouchers": {}
  "/api/auth/users": {}
  "/api/case-distribution": {}
  "/api/chart-of-accounts": {}
  "/api/chart-of-accounts-new": {}
  "/api/chart-of-accounts-new/[id]": { "id": string; }
  "/api/chart-of-accounts/link-tables": {}
  "/api/chart-of-accounts/main": {}
  "/api/clients": {}
  "/api/courts": {}
  "/api/employees": {}
  "/api/follows": {}
  "/api/follows/service-allocation": {}
  "/api/follows/user-issues": {}
  "/api/hearings": {}
  "/api/issues": {}
  "/api/issues/[id]": { "id": string; }
  "/api/issues/undistributed": {}
  "/api/journal-entries-new": {}
  "/api/migrate-accounts": {}
  "/api/reset-database": {}
  "/api/services": {}
  "/api/settings/announcements": {}
  "/api/test-connection": {}
  "/api/users": {}
  "/api/users/[id]": { "id": string; }
  "/branches": {}
  "/case-distribution": {}
  "/case-reports": {}
  "/client-accounts": {}
  "/client-login": {}
  "/client-portal": {}
  "/company": {}
  "/courts": {}
  "/dashboard": {}
  "/employees": {}
  "/follows": {}
  "/governorates": {}
  "/issue-types": {}
  "/issues": {}
  "/issues/new": {}
  "/journal-entries-new": {}
  "/lawyer-earnings": {}
  "/login": {}
  "/movements": {}
  "/percentages": {}
  "/services": {}
  "/settings/announcements": {}
  "/settings/cost-centers": {}
  "/settings/navigation-pages": {}
  "/setup": {}
  "/setup-simple": {}
  "/test-db": {}
  "/time-tracking": {}
  "/under-construction": {}
  "/users": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
