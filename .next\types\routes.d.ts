// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/accounting" | "/accounting/account-linking" | "/accounting/chart-of-accounts" | "/accounting/default-accounts" | "/accounting/link-accounts" | "/accounting/main-accounts" | "/accounting/payment-vouchers" | "/accounting/receipt-vouchers" | "/accounting/reports" | "/accounting/reports/account-statement" | "/branches" | "/case-distribution" | "/case-reports" | "/client-accounts" | "/client-login" | "/client-portal" | "/clients" | "/company" | "/courts" | "/dashboard" | "/documents" | "/documents/archive" | "/documents/upload" | "/employee-reports" | "/employees" | "/financial-reports" | "/follows" | "/governorates" | "/invoices" | "/issue-types" | "/issues" | "/issues/new" | "/journal-entries-new" | "/lawyer-earnings" | "/login" | "/movements" | "/opening-balances" | "/payment-vouchers" | "/percentages" | "/receipt-vouchers" | "/reports" | "/services" | "/settings/announcements" | "/settings/cost-centers" | "/settings/navigation-pages" | "/setup" | "/setup-integrated" | "/setup-simple" | "/test-db" | "/test-issue-select" | "/time-tracking" | "/trial-balance" | "/under-construction" | "/users"
type AppRouteHandlerRoutes = "/api/account-linking" | "/api/account-linking-settings" | "/api/accounting/account-linking" | "/api/accounting/account-linking/create-system-accounts" | "/api/accounting/chart-of-accounts" | "/api/accounting/chart-of-accounts/[id]" | "/api/accounting/chart-of-accounts/link" | "/api/accounting/currencies" | "/api/accounting/main-accounts" | "/api/accounting/payment-methods" | "/api/accounting/projects" | "/api/accounting/vouchers" | "/api/auth/users" | "/api/auto-link-account" | "/api/branches" | "/api/case-distribution" | "/api/chart-of-accounts" | "/api/chart-of-accounts-new" | "/api/chart-of-accounts-new/[id]" | "/api/chart-of-accounts/link-tables" | "/api/chart-of-accounts/main" | "/api/chat/conversations" | "/api/chat/messages" | "/api/chat/messages/read" | "/api/client-accounts" | "/api/clients" | "/api/companies" | "/api/cost-centers" | "/api/courts" | "/api/currencies" | "/api/employees" | "/api/follows" | "/api/follows/service-allocation" | "/api/follows/user-issues" | "/api/governorates" | "/api/hearings" | "/api/init" | "/api/invoices" | "/api/issue-types" | "/api/issues" | "/api/issues/[id]" | "/api/issues/undistributed" | "/api/journal-entries-new" | "/api/lawyer-earnings" | "/api/legal-files/stats" | "/api/lineages" | "/api/main-accounts" | "/api/main-accounts/[id]" | "/api/migrate-accounts" | "/api/movements" | "/api/navigation-pages/all" | "/api/notifications" | "/api/opening-balances" | "/api/payment-vouchers" | "/api/percentages" | "/api/receipt-vouchers" | "/api/reports" | "/api/reset-database" | "/api/seed-data" | "/api/seed-users" | "/api/services" | "/api/settings/announcements" | "/api/setup-integrated-ledgersmb" | "/api/setup-simple" | "/api/setup-step-by-step" | "/api/simple-receipt" | "/api/suppliers" | "/api/table-stats" | "/api/test-connection" | "/api/test-database-exists" | "/api/test-db" | "/api/test-operations" | "/api/test-tables" | "/api/time-tracking" | "/api/user-roles" | "/api/users" | "/api/users/[id]"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/accounting": {}
  "/accounting/account-linking": {}
  "/accounting/chart-of-accounts": {}
  "/accounting/default-accounts": {}
  "/accounting/link-accounts": {}
  "/accounting/main-accounts": {}
  "/accounting/payment-vouchers": {}
  "/accounting/receipt-vouchers": {}
  "/accounting/reports": {}
  "/accounting/reports/account-statement": {}
  "/api/account-linking": {}
  "/api/account-linking-settings": {}
  "/api/accounting/account-linking": {}
  "/api/accounting/account-linking/create-system-accounts": {}
  "/api/accounting/chart-of-accounts": {}
  "/api/accounting/chart-of-accounts/[id]": { "id": string; }
  "/api/accounting/chart-of-accounts/link": {}
  "/api/accounting/currencies": {}
  "/api/accounting/main-accounts": {}
  "/api/accounting/payment-methods": {}
  "/api/accounting/projects": {}
  "/api/accounting/vouchers": {}
  "/api/auth/users": {}
  "/api/auto-link-account": {}
  "/api/branches": {}
  "/api/case-distribution": {}
  "/api/chart-of-accounts": {}
  "/api/chart-of-accounts-new": {}
  "/api/chart-of-accounts-new/[id]": { "id": string; }
  "/api/chart-of-accounts/link-tables": {}
  "/api/chart-of-accounts/main": {}
  "/api/chat/conversations": {}
  "/api/chat/messages": {}
  "/api/chat/messages/read": {}
  "/api/client-accounts": {}
  "/api/clients": {}
  "/api/companies": {}
  "/api/cost-centers": {}
  "/api/courts": {}
  "/api/currencies": {}
  "/api/employees": {}
  "/api/follows": {}
  "/api/follows/service-allocation": {}
  "/api/follows/user-issues": {}
  "/api/governorates": {}
  "/api/hearings": {}
  "/api/init": {}
  "/api/invoices": {}
  "/api/issue-types": {}
  "/api/issues": {}
  "/api/issues/[id]": { "id": string; }
  "/api/issues/undistributed": {}
  "/api/journal-entries-new": {}
  "/api/lawyer-earnings": {}
  "/api/legal-files/stats": {}
  "/api/lineages": {}
  "/api/main-accounts": {}
  "/api/main-accounts/[id]": { "id": string; }
  "/api/migrate-accounts": {}
  "/api/movements": {}
  "/api/navigation-pages/all": {}
  "/api/notifications": {}
  "/api/opening-balances": {}
  "/api/payment-vouchers": {}
  "/api/percentages": {}
  "/api/receipt-vouchers": {}
  "/api/reports": {}
  "/api/reset-database": {}
  "/api/seed-data": {}
  "/api/seed-users": {}
  "/api/services": {}
  "/api/settings/announcements": {}
  "/api/setup-integrated-ledgersmb": {}
  "/api/setup-simple": {}
  "/api/setup-step-by-step": {}
  "/api/simple-receipt": {}
  "/api/suppliers": {}
  "/api/table-stats": {}
  "/api/test-connection": {}
  "/api/test-database-exists": {}
  "/api/test-db": {}
  "/api/test-operations": {}
  "/api/test-tables": {}
  "/api/time-tracking": {}
  "/api/user-roles": {}
  "/api/users": {}
  "/api/users/[id]": { "id": string; }
  "/branches": {}
  "/case-distribution": {}
  "/case-reports": {}
  "/client-accounts": {}
  "/client-login": {}
  "/client-portal": {}
  "/clients": {}
  "/company": {}
  "/courts": {}
  "/dashboard": {}
  "/documents": {}
  "/documents/archive": {}
  "/documents/upload": {}
  "/employee-reports": {}
  "/employees": {}
  "/financial-reports": {}
  "/follows": {}
  "/governorates": {}
  "/invoices": {}
  "/issue-types": {}
  "/issues": {}
  "/issues/new": {}
  "/journal-entries-new": {}
  "/lawyer-earnings": {}
  "/login": {}
  "/movements": {}
  "/opening-balances": {}
  "/payment-vouchers": {}
  "/percentages": {}
  "/receipt-vouchers": {}
  "/reports": {}
  "/services": {}
  "/settings/announcements": {}
  "/settings/cost-centers": {}
  "/settings/navigation-pages": {}
  "/setup": {}
  "/setup-integrated": {}
  "/setup-simple": {}
  "/test-db": {}
  "/test-issue-select": {}
  "/time-tracking": {}
  "/trial-balance": {}
  "/under-construction": {}
  "/users": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
