// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/dashboard" | "/login"
type AppRouteHandlerRoutes = "/api/auth/users" | "/api/case-distribution" | "/api/chart-of-accounts" | "/api/chart-of-accounts-new" | "/api/chart-of-accounts-new/[id]" | "/api/chart-of-accounts/link-tables" | "/api/chart-of-accounts/main" | "/api/employees" | "/api/follows" | "/api/follows/service-allocation" | "/api/follows/user-issues" | "/api/journal-entries-new" | "/api/migrate-accounts" | "/api/reset-database" | "/api/settings/announcements" | "/api/test-connection"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/api/auth/users": {}
  "/api/case-distribution": {}
  "/api/chart-of-accounts": {}
  "/api/chart-of-accounts-new": {}
  "/api/chart-of-accounts-new/[id]": { "id": string; }
  "/api/chart-of-accounts/link-tables": {}
  "/api/chart-of-accounts/main": {}
  "/api/employees": {}
  "/api/follows": {}
  "/api/follows/service-allocation": {}
  "/api/follows/user-issues": {}
  "/api/journal-entries-new": {}
  "/api/migrate-accounts": {}
  "/api/reset-database": {}
  "/api/settings/announcements": {}
  "/api/test-connection": {}
  "/dashboard": {}
  "/login": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
