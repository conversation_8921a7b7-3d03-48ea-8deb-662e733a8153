import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع أرباح المحامين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const lawyerId = searchParams.get('lawyer_id')
    const caseId = searchParams.get('case_id')
    const serviceId = searchParams.get('service_id')
    
    let whereClause = ''
    let params: any[] = []
    let paramIndex = 1
    
    const conditions = []
    
    if (lawyerId) {
      conditions.push(`le.lawyer_id = $${paramIndex}`)
      params.push(lawyerId)
      paramIndex++
    }
    
    if (caseId) {
      conditions.push(`le.case_id = $${paramIndex}`)
      params.push(caseId)
      paramIndex++
    }
    
    if (serviceId) {
      conditions.push(`le.service_id = $${paramIndex}`)
      params.push(serviceId)
      paramIndex++
    }
    
    if (conditions.length > 0) {
      whereClause = 'WHERE ' + conditions.join(' AND ')
    }
    
    const result = await query(`
      SELECT 
        le.*,
        i.case_number,
        i.title as case_title,
        s.name as service_name,
        e.name as lawyer_name
      FROM lawyer_earnings le
      LEFT JOIN issues i ON le.case_id = i.id
      LEFT JOIN services s ON le.service_id = s.id
      LEFT JOIN employees e ON le.lawyer_id = e.id
      ${whereClause}
      ORDER BY le.earning_date DESC, le.created_date DESC
    `, params)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching lawyer earnings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات أرباح المحامين' },
      { status: 500 }
    )
  }
}

// POST - إضافة ربح جديد للمحامي
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      lawyer_id,
      case_id,
      service_id,
      follow_id,
      allocated_amount,
      earned_amount,
      notes
    } = body

    if (!lawyer_id || !case_id || !service_id || !earned_amount) {
      return NextResponse.json(
        { success: false, error: 'المحامي والقضية والخدمة والمبلغ المكتسب مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من صحة المبلغ
    if (earned_amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'المبلغ المكتسب يجب أن يكون أكبر من صفر' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO lawyer_earnings (
        lawyer_id, case_id, service_id, follow_id, 
        allocated_amount, earned_amount, notes
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      lawyer_id, case_id, service_id, follow_id || null,
      allocated_amount || 0, earned_amount, notes || null
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الربح بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating lawyer earning:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تسجيل الربح' },
      { status: 500 }
    )
  }
}

// PUT - تحديث ربح المحامي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      lawyer_id,
      case_id,
      service_id,
      follow_id,
      allocated_amount,
      earned_amount,
      notes
    } = body

    if (!id || !lawyer_id || !case_id || !service_id || !earned_amount) {
      return NextResponse.json(
        { success: false, error: 'المعرف والمحامي والقضية والخدمة والمبلغ المكتسب مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من صحة المبلغ
    if (earned_amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'المبلغ المكتسب يجب أن يكون أكبر من صفر' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE lawyer_earnings 
      SET lawyer_id = $1, case_id = $2, service_id = $3, follow_id = $4,
          allocated_amount = $5, earned_amount = $6, notes = $7
      WHERE id = $8
      RETURNING *
    `, [
      lawyer_id, case_id, service_id, follow_id || null,
      allocated_amount || 0, earned_amount, notes || null, id
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الربح غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الربح بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating lawyer earning:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الربح' },
      { status: 500 }
    )
  }
}

// DELETE - حذف ربح المحامي
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الربح مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM lawyer_earnings WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الربح غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الربح بنجاح'
    })
  } catch (error) {
    console.error('Error deleting lawyer earning:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الربح' },
      { status: 500 }
    )
  }
}
