import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// الصفحات العامة التي لا تحتاج تسجيل دخول
const publicPaths = [
  '/login',
  '/client-login',
  '/_next',
  '/favicon.ico',
  '/api/auth/users',
  '/api/client-portal/auth/simple'
]

// الصفحات المحمية للمستخدمين فقط
const userOnlyPaths = [
  '/dashboard',
  '/follows',
  '/issues',
  '/clients',
  '/users',
  '/accounting',
  '/reports'
]

// الصفحات المحمية للعملاء فقط
const clientOnlyPaths = [
  '/client-portal'
]

export function middleware(request: NextRequest) {
  // تعطيل middleware مؤقتاً للاختبار
  return NextResponse.next()

  const { pathname } = request.nextUrl

  // السماح بالوصول للصفحات العامة
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next()
  }

  // التحقق من وجود جلسة مستخدم
  const userSession = request.cookies.get('userSession')?.value

  if (!userSession) {
    // إذا لم توجد جلسة، توجيه إلى صفحة الدخول
    if (pathname === '/') {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // للصفحات المحمية، توجيه إلى الدخول المناسب
    if (clientOnlyPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    if (userOnlyPaths.some(path => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // للصفحات الأخرى، توجيه إلى الدخول العام
    return NextResponse.redirect(new URL('/login', request.url))
  }

  try {
    const sessionData = JSON.parse(userSession)

    // التحقق من نوع المستخدم وتوجيهه للصفحات المناسبة
    if (sessionData.type === 'client') {
      // العملاء يمكنهم الوصول فقط لبوابة العملاء
      if (userOnlyPaths.some(path => pathname.startsWith(path))) {
        return NextResponse.redirect(new URL('/client-portal', request.url))
      }

      // توجيه العميل للبوابة إذا حاول الوصول للصفحة الرئيسية
      if (pathname === '/') {
        return NextResponse.redirect(new URL('/client-portal', request.url))
      }
    } else if (sessionData.type === 'user') {
      // المستخدمون لا يمكنهم الوصول لبوابة العملاء
      if (clientOnlyPaths.some(path => pathname.startsWith(path))) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }

      // توجيه المستخدم للوحة التحكم إذا حاول الوصول للصفحة الرئيسية
      if (pathname === '/') {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    }

  } catch (error) {
    // إذا كانت بيانات الجلسة تالفة، حذفها وتوجيه للدخول
    const response = NextResponse.redirect(new URL('/login', request.url))
    response.cookies.delete('userSession')
    return response
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}