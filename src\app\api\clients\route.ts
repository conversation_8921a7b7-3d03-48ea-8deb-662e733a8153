import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'
import bcrypt from 'bcrypt'

// GET - جلب جميع الموكلين
export async function GET() {
  try {
    const result = await query(`
      SELECT
        c.*,
        ma.id as main_account_id,
        ma.account_code as sub_account_code,
        coa.account_code as parent_account_code,
        coa.account_name as parent_account_name,
        coa.current_balance as parent_account_balance,
        ma.balance as individual_balance
      FROM clients c
      LEFT JOIN main_accounts ma ON c.account_id = ma.id
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY c.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      clients: result.rows
    })
  } catch (error) {
    console.error('Error fetching clients:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الموكلين' },
      { status: 500 }
    )
  }
}

// POST - إضافة موكل جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      phone,
      email,
      address,
      id_number,
      client_type = 'individual',
      username,
      password,
      status = 'active'
    } = body

    // التحقق من البيانات المطلوبة
    if (!name || !id_number || !username || !password) {
      return NextResponse.json(
        { success: false, error: 'الاسم ورقم الهوية واسم الدخول وكلمة المرور مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم الهوية
    const existingClient = await query(
      'SELECT id FROM clients WHERE id_number = $1',
      [id_number]
    )

    if (existingClient.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم الهوية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار اسم المستخدم
    const existingUsername = await query(
      'SELECT id FROM clients WHERE username = $1',
      [username]
    )

    if (existingUsername.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10)

    // إدراج الموكل الجديد
    const result = await query(`
      INSERT INTO clients (name, phone, email, address, id_number, client_type, username, password_hash, status)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `, [name, phone, email, address, id_number, client_type, username, hashedPassword, status])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الموكل بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating client:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الموكل' },
      { status: 500 }
    )
  }
}

// PUT - تحديث موكل
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      phone,
      email,
      address,
      id_number,
      client_type,
      username,
      password,
      status
    } = body

    if (!id || !name || !id_number || !username) {
      return NextResponse.json(
        { success: false, error: 'المعرف والاسم ورقم الهوية واسم الدخول مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من وجود الموكل
    const existingClient = await query(
      'SELECT id FROM clients WHERE id = $1',
      [id]
    )

    if (existingClient.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الموكل غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم تكرار رقم الهوية مع موكل آخر
    const duplicateCheck = await query(
      'SELECT id FROM clients WHERE id_number = $1 AND id != $2',
      [id_number, id]
    )

    if (duplicateCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم الهوية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار اسم المستخدم مع موكل آخر
    const usernameCheck = await query(
      'SELECT id FROM clients WHERE username = $1 AND id != $2',
      [username, id]
    )

    if (usernameCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تحديث بيانات الموكل
    let updateQuery = `
      UPDATE clients
      SET name = $1, phone = $2, email = $3, address = $4,
          id_number = $5, client_type = $6, username = $7, status = $8, updated_at = CURRENT_TIMESTAMP
    `
    let params = [name, phone, email, address, id_number, client_type, username, status]

    // إذا تم توفير كلمة مرور جديدة، قم بتشفيرها وتحديثها
    if (password && password.trim() !== '') {
      const hashedPassword = await bcrypt.hash(password, 10)
      updateQuery += `, password_hash = $${params.length + 1}`
      params.push(hashedPassword)
    }

    updateQuery += ` WHERE id = $${params.length + 1} RETURNING *`
    params.push(id)

    const result = await query(updateQuery, params)

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الموكل بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating client:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الموكل' },
      { status: 500 }
    )
  }
}

// DELETE - حذف موكل
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الموكل مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود قضايا مرتبطة بالموكل
    const relatedIssues = await query(
      'SELECT COUNT(*) as count FROM issues WHERE client_id = $1',
      [id]
    )

    if (parseInt(relatedIssues.rows[0].count) > 0) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف الموكل لوجود قضايا مرتبطة به' },
        { status: 400 }
      )
    }

    // حذف الموكل
    const result = await query(
      'DELETE FROM clients WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الموكل غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموكل بنجاح'
    })
  } catch (error) {
    console.error('Error deleting client:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الموكل' },
      { status: 500 }
    )
  }
}

