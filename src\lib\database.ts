import { Pool } from 'pg'

// Database configuration from mohammi.txt
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123',
  ssl: false,
  connectionTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  max: 20
}

// Create connection pool
const pool = new Pool(dbConfig)

// Test database connection
export async function testConnection() {
  try {
    const client = await pool.connect()
    const result = await client.query('SELECT NOW()')
    client.release()
    console.log('✅ Database connected successfully:', result.rows[0])
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}

// Generic query function
export async function query(text: string, params?: any[]) {
  let client;
  try {
    client = await pool.connect()
    const result = await client.query(text, params)
    return result
  } catch (error) {
    console.error('Database query error:', error)
    console.error('Query:', text)
    console.error('Params:', params)
    throw error
  } finally {
    if (client) {
      client.release()
    }
  }
}

// Initialize database tables
export async function initializeTables() {
  try {
    // Create clients table
    await query(`
      CREATE TABLE IF NOT EXISTS clients (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        id_number VARCHAR(20) UNIQUE,
        status VARCHAR(20) DEFAULT 'active',
        cases_count INTEGER DEFAULT 0,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create employees table
    await query(`
      CREATE TABLE IF NOT EXISTS employees (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255),
        department VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        id_number VARCHAR(20) UNIQUE,
        salary DECIMAL(10,2),
        hire_date DATE,
        status VARCHAR(20) DEFAULT 'active',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create issue_types table
    await query(`
      CREATE TABLE IF NOT EXISTS issue_types (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        color VARCHAR(50),
        cases_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create issues table
    await query(`
      CREATE TABLE IF NOT EXISTS issues (
        id SERIAL PRIMARY KEY,
        case_number VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        client_id INTEGER REFERENCES clients(id),
        client_name VARCHAR(255),
        court_name VARCHAR(255),
        issue_type_id INTEGER REFERENCES issue_types(id),
        issue_type VARCHAR(255),
        status VARCHAR(50) DEFAULT 'pending',
        amount DECIMAL(12,2),
        next_hearing DATE,
        notes TEXT,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create lineages table (النسب المالية)
    await query(`
      CREATE TABLE IF NOT EXISTS lineages (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        management_share DECIMAL(5,2) DEFAULT 0.00,
        court_share DECIMAL(5,2) DEFAULT 0.00,
        commission_share DECIMAL(5,2) DEFAULT 0.00,
        other_share DECIMAL(5,2) DEFAULT 0.00,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create follows table (المتابعات)
    await query(`
      CREATE TABLE IF NOT EXISTS follows (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        case_title VARCHAR(255),
        client_name VARCHAR(255),
        follow_type VARCHAR(50),
        description TEXT,
        due_date DATE,
        status VARCHAR(50) DEFAULT 'pending',
        priority VARCHAR(20) DEFAULT 'medium',
        created_by VARCHAR(255),
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create movements table (الحركات المالية)
    await query(`
      CREATE TABLE IF NOT EXISTS movements (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        case_title VARCHAR(255),
        movement_type VARCHAR(20) NOT NULL, -- 'income' or 'expense'
        category VARCHAR(255),
        amount DECIMAL(12,2) NOT NULL,
        description TEXT,
        date DATE DEFAULT CURRENT_DATE,
        reference_number VARCHAR(100),
        status VARCHAR(50) DEFAULT 'pending',
        created_by VARCHAR(255),
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create users table
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user',
        status VARCHAR(20) DEFAULT 'active',
        last_login TIMESTAMP,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create companies table
    await query(`
      CREATE TABLE IF NOT EXISTS companies (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        legal_name VARCHAR(255),
        registration_number VARCHAR(100),
        address TEXT,
        city VARCHAR(100),
        country VARCHAR(100),
        phone VARCHAR(20),
        email VARCHAR(255),
        website VARCHAR(255),
        tax_number VARCHAR(50),
        commercial_register VARCHAR(50),
        logo_url VARCHAR(500),
        logo_right_text TEXT,
        logo_left_text TEXT,
        logo_image_url VARCHAR(500),
        established_date DATE,
        legal_form VARCHAR(100),
        capital DECIMAL(15,2),
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create journal_entries table
    await query(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id SERIAL PRIMARY KEY,
        entry_number VARCHAR(50) UNIQUE NOT NULL,
        description TEXT NOT NULL,
        date DATE DEFAULT CURRENT_DATE,
        total_debit DECIMAL(12,2) DEFAULT 0.00,
        total_credit DECIMAL(12,2) DEFAULT 0.00,
        status VARCHAR(50) DEFAULT 'pending',
        created_by VARCHAR(255),
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create governorates table
    await query(`
      CREATE TABLE IF NOT EXISTS governorates (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(10),
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create branches table
    await query(`
      CREATE TABLE IF NOT EXISTS branches (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        governorate_id INTEGER REFERENCES governorates(id),
        address TEXT,
        phone VARCHAR(20),
        manager_name VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create courts table
    await query(`
      CREATE TABLE IF NOT EXISTS courts (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(100),
        governorate_id INTEGER REFERENCES governorates(id),
        address TEXT,
        phone VARCHAR(20),
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create announcements table
    await query(`
      CREATE TABLE IF NOT EXISTS announcements (
        id SERIAL PRIMARY KEY,
        announcement_1 TEXT,
        announcement_2 TEXT,
        announcement_3 TEXT,
        announcement_4 TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create opening_balances table
    await query(`
      CREATE TABLE IF NOT EXISTS opening_balances (
        id SERIAL PRIMARY KEY,
        account_id INTEGER NOT NULL,
        debit_balance DECIMAL(15,2) DEFAULT 0,
        credit_balance DECIMAL(15,2) DEFAULT 0,
        balance_date DATE NOT NULL,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create navigation_pages table for smart search
    await query(`
      CREATE TABLE IF NOT EXISTS navigation_pages (
        id SERIAL PRIMARY KEY,
        page_title VARCHAR(255) NOT NULL,
        page_url VARCHAR(500) NOT NULL,
        page_description TEXT,
        category VARCHAR(100),
        keywords TEXT,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Insert default navigation pages
    await query(`
      INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords) VALUES
      ('لوحة التحكم', '/', 'الصفحة الرئيسية للنظام', 'رئيسي', 'لوحة,تحكم,رئيسي,dashboard'),
      ('العملاء', '/clients', 'إدارة بيانات العملاء', 'إدارة', 'عملاء,clients,زبائن'),
      ('الموظفين', '/employees', 'إدارة بيانات الموظفين', 'إدارة', 'موظفين,employees,عمال'),
      ('أنواع القضايا', '/issue-types', 'إدارة أنواع القضايا', 'قضايا', 'أنواع,قضايا,types'),
      ('القضايا', '/issues', 'إدارة القضايا والدعاوى', 'قضايا', 'قضايا,دعاوى,issues,cases'),
      ('المتابعات', '/follows', 'متابعة القضايا والمهام', 'قضايا', 'متابعات,follows,مهام'),
      ('حركة القضايا', '/movements', 'إدارة حركة القضايا', 'مالية', 'حركات,مالية,movements'),
      ('دليل الحسابات', '/accounting/chart-of-accounts', 'دليل الحسابات المحاسبي', 'محاسبة', 'دليل,حسابات,chart,accounts'),
      ('سندات الصرف', '/accounting/payment-vouchers', 'إدارة سندات الصرف', 'محاسبة', 'سندات,صرف,payment,vouchers'),
      ('سندات القبض', '/accounting/receipt-vouchers', 'إدارة سندات القبض', 'محاسبة', 'سندات,قبض,receipt,vouchers'),
      ('القيود اليومية', '/accounting/journal-entries', 'إدارة القيود اليومية', 'محاسبة', 'قيود,يومية,journal,entries'),
      ('الأرصدة الافتتاحية', '/accounting/opening-balances', 'إدارة الأرصدة الافتتاحية', 'محاسبة', 'أرصدة,افتتاحية,opening,balances'),
      ('التقارير المحاسبية', '/accounting/reports', 'التقارير المحاسبية', 'تقارير', 'تقارير,محاسبية,reports'),
      ('كشف حساب', '/accounting/reports/account-statement', 'كشف حساب تفصيلي', 'تقارير', 'كشف,حساب,statement'),
      ('تقارير القضايا', '/case-reports', 'تقارير القضايا والدعاوى', 'تقارير', 'تقارير,قضايا,cases'),
      ('التقارير المالية', '/financial-reports', 'التقارير المالية', 'تقارير', 'تقارير,مالية,financial'),
      ('تقارير الموظفين', '/employee-reports', 'تقارير الموظفين', 'تقارير', 'تقارير,موظفين,employees'),
      ('بيانات الشركة', '/company', 'إدارة بيانات الشركة', 'إعدادات', 'شركة,company,بيانات'),
      ('مراكز التكلفة', '/settings/cost-centers', 'إدارة مراكز التكلفة', 'إعدادات', 'مراكز,تكلفة,cost,centers'),
      ('الإعلانات', '/settings/announcements', 'إدارة الإعلانات', 'إعدادات', 'إعلانات,announcements'),
      ('المحافظات', '/governorates', 'إدارة المحافظات', 'إدارة', 'محافظات,governorates'),
      ('الفروع', '/branches', 'إدارة الفروع', 'إدارة', 'فروع,branches'),
      ('المحاكم', '/courts', 'إدارة المحاكم', 'إدارة', 'محاكم,courts')
      ON CONFLICT DO NOTHING
    `)

    console.log('✅ All database tables initialized successfully')
    return true
  } catch (error) {
    console.error('❌ Failed to initialize database tables:', error)
    throw error
  }
}

export default pool
