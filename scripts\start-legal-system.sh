#!/bin/bash

# سكريبت بدء تشغيل النظام القانوني
# Legal System Startup Script

echo "🚀 بدء تشغيل النظام القانوني..."

# مسار المشروع
PROJECT_DIR="/home/<USER>/Downloads/legal-system"
LOG_FILE="/home/<USER>/legal-system.log"
PID_FILE="/home/<USER>/legal-system.pid"

# التحقق من وجود المشروع
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ مجلد المشروع غير موجود: $PROJECT_DIR"
    exit 1
fi

# الانتقال إلى مجلد المشروع
cd "$PROJECT_DIR"

# التحقق من وجود عملية تشغيل سابقة
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if ps -p $OLD_PID > /dev/null 2>&1; then
        echo "⚠️  النظام يعمل بالفعل (PID: $OLD_PID)"
        echo "🌐 يمكنك الوصول للنظام على: http://localhost:7443"
        exit 0
    else
        echo "🧹 إزالة ملف PID القديم"
        rm -f "$PID_FILE"
    fi
fi

# التحقق من توفر المنفذ
if lsof -Pi :7443 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  المنفذ 7443 مستخدم بالفعل"
    echo "🔍 العمليات التي تستخدم المنفذ:"
    lsof -Pi :7443 -sTCP:LISTEN
    echo "🌐 يمكنك الوصول للنظام على: http://localhost:7443"
    exit 0
fi

# بدء تشغيل النظام
echo "▶️  بدء تشغيل النظام..."
echo "📁 مجلد المشروع: $PROJECT_DIR"
echo "📝 ملف السجل: $LOG_FILE"

# تشغيل النظام في الخلفية
nohup npm run dev > "$LOG_FILE" 2>&1 &
SERVER_PID=$!

# حفظ معرف العملية
echo $SERVER_PID > "$PID_FILE"

echo "✅ تم بدء تشغيل النظام"
echo "🆔 معرف العملية: $SERVER_PID"
echo "📝 ملف السجل: $LOG_FILE"

# انتظار قليل للتأكد من بدء التشغيل
sleep 5

# التحقق من أن العملية ما زالت تعمل
if ps -p $SERVER_PID > /dev/null 2>&1; then
    echo "✅ النظام يعمل بنجاح"
    echo "🌐 يمكنك الوصول للنظام على: http://localhost:7443"
    echo "👤 تسجيل الدخول: admin / ana8080"
else
    echo "❌ فشل في بدء تشغيل النظام"
    echo "📋 آخر سطور من السجل:"
    tail -10 "$LOG_FILE"
    rm -f "$PID_FILE"
    exit 1
fi

echo ""
echo "🎛️ أوامر إدارة النظام:"
echo "========================"
echo "⏹️  إيقاف النظام:      ./scripts/stop-legal-system.sh"
echo "🔄 إعادة تشغيل:        ./scripts/restart-legal-system.sh"
echo "📊 حالة النظام:        ./scripts/status-legal-system.sh"
echo "📋 عرض السجلات:       tail -f $LOG_FILE"
