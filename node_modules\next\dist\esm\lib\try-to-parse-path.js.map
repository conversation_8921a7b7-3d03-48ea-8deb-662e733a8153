{"version": 3, "sources": ["../../src/lib/try-to-parse-path.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\nimport { parse, tokensToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport { parse as parseURL } from 'url'\nimport isError from './is-error'\nimport { normalizeTokensForRegexp } from './route-pattern-normalizer'\n\ninterface ParseResult {\n  error?: any\n  parsedPath: string\n  regexStr?: string\n  route: string\n  tokens?: Token[]\n}\n\n/**\n * If there is an error show our error link but still show original error or\n * a formatted one if we can\n */\nfunction reportError({ route, parsedPath }: ParseResult, err: any) {\n  let errMatches\n  if (isError(err) && (errMatches = err.message.match(/at (\\d{0,})/))) {\n    const position = parseInt(errMatches[1], 10)\n    console.error(\n      `\\nError parsing \\`${route}\\` ` +\n        `https://nextjs.org/docs/messages/invalid-route-source\\n` +\n        `Reason: ${err.message}\\n\\n` +\n        `  ${parsedPath}\\n` +\n        `  ${new Array(position).fill(' ').join('')}^\\n`\n    )\n  } else {\n    console.error(\n      `\\nError parsing ${route} https://nextjs.org/docs/messages/invalid-route-source`,\n      err\n    )\n  }\n}\n\n/**\n * Safe wrapper around tokensToRegexp that handles path-to-regexp 6.3.0+ validation errors.\n */\nfunction safeTokensToRegexp(tokens: Token[]): RegExp {\n  try {\n    return tokensToRegexp(tokens)\n  } catch (error) {\n    if (isError(error)) {\n      // Try to normalize tokens with repeating modifiers but no prefix/suffix\n      const normalizedTokens = normalizeTokensForRegexp(tokens)\n      return tokensToRegexp(normalizedTokens)\n    }\n    throw error\n  }\n}\n\n/**\n * Attempts to parse a given route with `path-to-regexp` and returns an object\n * with the result. Whenever an error happens on parse, it will print an error\n * attempting to find the error position and showing a link to the docs. When\n * `handleUrl` is set to `true` it will also attempt to parse the route\n * and use the resulting pathname to parse with `path-to-regexp`.\n */\nexport function tryToParsePath(\n  route: string,\n  options?: {\n    handleUrl?: boolean\n  }\n): ParseResult {\n  const result: ParseResult = { route, parsedPath: route }\n  try {\n    if (options?.handleUrl) {\n      const parsed = parseURL(route, true)\n      result.parsedPath = `${parsed.pathname!}${parsed.hash || ''}`\n    }\n\n    result.tokens = parse(result.parsedPath)\n\n    // Use safe wrapper instead of proactive detection\n    if (result.tokens) {\n      result.regexStr = safeTokensToRegexp(result.tokens).source\n    }\n  } catch (err) {\n    reportError(result, err)\n    result.error = err\n  }\n\n  return result\n}\n"], "names": ["parse", "tokensToRegexp", "parseURL", "isError", "normalizeTokensForRegexp", "reportError", "route", "parsed<PERSON><PERSON>", "err", "err<PERSON><PERSON><PERSON>", "message", "match", "position", "parseInt", "console", "error", "Array", "fill", "join", "safeTokensToRegexp", "tokens", "normalizedTokens", "tryToParsePath", "options", "result", "handleUrl", "parsed", "pathname", "hash", "regexStr", "source"], "mappings": "AACA,SAASA,KAAK,EAAEC,cAAc,QAAQ,oCAAmC;AACzE,SAASD,SAASE,QAAQ,QAAQ,MAAK;AACvC,OAAOC,aAAa,aAAY;AAChC,SAASC,wBAAwB,QAAQ,6BAA4B;AAUrE;;;CAGC,GACD,SAASC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAe,EAAEC,GAAQ;IAC/D,IAAIC;IACJ,IAAIN,QAAQK,QAASC,CAAAA,aAAaD,IAAIE,OAAO,CAACC,KAAK,CAAC,cAAa,GAAI;QACnE,MAAMC,WAAWC,SAASJ,UAAU,CAAC,EAAE,EAAE;QACzCK,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAET,MAAM,GAAG,CAAC,GAC7B,CAAC,uDAAuD,CAAC,GACzD,CAAC,QAAQ,EAAEE,IAAIE,OAAO,CAAC,IAAI,CAAC,GAC5B,CAAC,EAAE,EAAEH,WAAW,EAAE,CAAC,GACnB,CAAC,EAAE,EAAE,IAAIS,MAAMJ,UAAUK,IAAI,CAAC,KAAKC,IAAI,CAAC,IAAI,GAAG,CAAC;IAEtD,OAAO;QACLJ,QAAQC,KAAK,CACX,CAAC,gBAAgB,EAAET,MAAM,sDAAsD,CAAC,EAChFE;IAEJ;AACF;AAEA;;CAEC,GACD,SAASW,mBAAmBC,MAAe;IACzC,IAAI;QACF,OAAOnB,eAAemB;IACxB,EAAE,OAAOL,OAAO;QACd,IAAIZ,QAAQY,QAAQ;YAClB,wEAAwE;YACxE,MAAMM,mBAAmBjB,yBAAyBgB;YAClD,OAAOnB,eAAeoB;QACxB;QACA,MAAMN;IACR;AACF;AAEA;;;;;;CAMC,GACD,OAAO,SAASO,eACdhB,KAAa,EACbiB,OAEC;IAED,MAAMC,SAAsB;QAAElB;QAAOC,YAAYD;IAAM;IACvD,IAAI;QACF,IAAIiB,2BAAAA,QAASE,SAAS,EAAE;YACtB,MAAMC,SAASxB,SAASI,OAAO;YAC/BkB,OAAOjB,UAAU,GAAG,GAAGmB,OAAOC,QAAQ,GAAID,OAAOE,IAAI,IAAI,IAAI;QAC/D;QAEAJ,OAAOJ,MAAM,GAAGpB,MAAMwB,OAAOjB,UAAU;QAEvC,kDAAkD;QAClD,IAAIiB,OAAOJ,MAAM,EAAE;YACjBI,OAAOK,QAAQ,GAAGV,mBAAmBK,OAAOJ,MAAM,EAAEU,MAAM;QAC5D;IACF,EAAE,OAAOtB,KAAK;QACZH,YAAYmB,QAAQhB;QACpBgB,OAAOT,KAAK,GAAGP;IACjB;IAEA,OAAOgB;AACT", "ignoreList": [0]}