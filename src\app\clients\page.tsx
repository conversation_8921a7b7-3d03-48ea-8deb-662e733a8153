'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { autoLinkAccount, handleAutoLinkResult } from '@/lib/auto-account-linking'
import {
  UserCheck,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  X,
  Save,
  User,
  Link
} from 'lucide-react'

interface Client {
  id: number
  name: string
  phone: string
  email: string
  address: string
  id_number: string
  client_type: string
  username: string
  password_hash: string
  status: string
  is_active: boolean
  is_online: boolean
  last_login: string
  created_date: string
  cases_count: number
  current_balance?: number
  main_account_id?: number
  sub_account_code?: string
  parent_account_code?: string
  parent_account_name?: string
  parent_account_balance?: number
}

export default function ClientsPage() {
  const [clients, setClients] = useState<Client[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    id_number: '',
    client_type: 'individual',
    username: '',
    password: '',
    status: 'active' as 'active' | 'inactive'
  })

  // جلب البيانات من قاعدة البيانات
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const fetchClients = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch('/api/clients')
      const result = await response.json()

      if (result.success) {
        setClients(result.clients || [])
      } else {
        console.error('Error fetching clients:', result.error)
        setDbError(result.error || 'فشل في جلب بيانات الموكلين')
        setClients([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setClients([])
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchClients()
  }, [])

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.phone.includes(searchTerm) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
  }

  const getStatusText = (status: string) => {
    return status === 'active' ? 'نشط' : 'غير نشط'
  }

  const handleDelete = async (id: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الموكل؟')) {
      try {
        const response = await fetch(`/api/clients?id=${id}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          alert('تم حذف الموكل بنجاح')
          fetchClients() // إعادة جلب البيانات
        } else {
          alert(result.error || 'فشل في حذف الموكل')
        }
      } catch (error) {
        console.error('Error deleting client:', error)
        alert('حدث خطأ في الاتصال')
      }
    }
  }

  const handleEdit = (client: Client) => {
    console.log('تحرير العميل:', client)
    setEditingClient(client)
    setFormData({
      name: client.name || '',
      phone: client.phone || '',
      email: client.email || '',
      address: client.address || '',
      id_number: client.id_number || '',
      status: client.status || 'active'
    })
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleView = (client: Client) => {
    setEditingClient(client)
    setModalType('view')
    setIsModalOpen(true)
  }

  const handleAddNew = () => {
    setEditingClient(null)
    setFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      id_number: '',
      client_type: 'individual',
      username: '',
      password: '',
      status: 'active'
    })
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (modalType === 'add') {
        const response = await fetch('/api/clients', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData)
        })

        const result = await response.json()

        if (result.success) {
          alert('تم إضافة الموكل بنجاح')

          // تطبيق الربط التلقائي للحساب المحاسبي
          if (result.data?.id) {
            const autoLinkResult = await autoLinkAccount('clients', result.data.id, formData.name)
            handleAutoLinkResult(autoLinkResult, false) // لا نظهر تنبيه منفصل

            if (autoLinkResult.success) {
              alert(`✅ تم إضافة الموكل وربطه بالحساب المحاسبي بنجاح\n🔗 كود الحساب: ${autoLinkResult.sub_account_code}`)
            } else {
              alert('✅ تم إضافة الموكل بنجاح\n⚠️ لم يتم الربط التلقائي بالحساب المحاسبي')
            }
          }

          fetchClients() // إعادة جلب البيانات
        } else {
          alert(result.error || 'فشل في إضافة الموكل')
          return
        }
      } else if (modalType === 'edit' && editingClient) {
        console.log('بيانات التحديث للعميل:', { ...formData, id: editingClient.id })

        const response = await fetch('/api/clients', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...formData, id: editingClient.id })
        })

        const result = await response.json()
        console.log('نتيجة تحديث العميل:', result)

        if (result.success) {
          alert('تم تحديث بيانات الموكل بنجاح')
          // إعادة جلب البيانات لضمان التحديث
          await fetchClients()
          // إغلاق النافذة وإعادة تعيين البيانات
          setIsModalOpen(false)
          setEditingClient(null)
          setFormData({
            name: '',
            phone: '',
            email: '',
            id_number: '',
            address: '',
            status: 'active'
          })
        } else {
          alert(result.error || 'فشل في تحديث بيانات الموكل')
          return
        }
      }

      // إغلاق النافذة فقط للإضافة (التحديث يُغلق داخل الشرط)
      if (modalType === 'add') {
        setIsModalOpen(false)
        setEditingClient(null)
        setFormData({
          name: '',
          phone: '',
          email: '',
          id_number: '',
          address: '',
          status: 'active'
        })
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('حدث خطأ في الاتصال')
    }
  }

  const stats = {
    total: clients.length,
    active: clients.filter(c => c.status === 'active').length,
    inactive: clients.filter(c => c.status === 'inactive').length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <UserCheck className="h-8 w-8 mr-3 text-blue-600" />
              إدارة الموكلين
            </h1>
            <p className="text-gray-600 mt-1">إدارة ومتابعة جميع الموكلين</p>
          </div>

          <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة موكل جديد
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <UserCheck className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي الموكلين</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <UserCheck className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.active}</div>
                  <div className="text-sm text-gray-600">موكلين نشطين</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <UserCheck className="h-6 w-6 text-red-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.inactive}</div>
                  <div className="text-sm text-gray-600">موكلين غير نشطين</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في الموكلين..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserCheck className="h-5 w-5 mr-2" />
              قائمة الموكلين ({filteredClients.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* عرض رسالة الخطأ */}
            {dbError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="mr-3">
                    <h3 className="text-sm font-medium text-red-800">
                      خطأ في الاتصال بقاعدة البيانات
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{dbError}</p>
                      <p className="mt-1">يرجى التأكد من:</p>
                      <ul className="list-disc list-inside mt-1">
                        <li>تشغيل خادم PostgreSQL</li>
                        <li>صحة بيانات الاتصال في ملف mohammi.txt</li>
                        <li>وجود قاعدة البيانات 'mohammi'</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="mt-4">
                  <Button
                    onClick={fetchClients}
                    variant="outline"
                    size="sm"
                    className="bg-white hover:bg-gray-50"
                  >
                    إعادة المحاولة
                  </Button>
                </div>
              </div>
            )}

            {/* عرض رسالة التحميل */}
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="mr-3 text-gray-600">جاري تحميل البيانات...</span>
              </div>
            )}

            {/* عرض الجدول فقط إذا لم يكن هناك خطأ ولا يتم التحميل */}
            {!dbError && !isLoading && (
            <div className="overflow-x-auto" style={{ maxWidth: '100vw' }}>
              <table className="w-full border-collapse min-w-max">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="text-right p-4 font-semibold text-lg min-w-[100px]">رقم الموكل</th>
                    <th className="text-right p-4 font-semibold text-lg min-w-[200px]">اسم الموكل</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[150px]">رقم الهوية</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[120px]">رقم الهاتف</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[150px]">الحساب الأب</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[120px]">الرصيد</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[100px]">الحالة</th>
                    <th className="text-center p-4 font-semibold text-lg min-w-[150px]">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredClients.map((client) => (
                    <tr key={client.id} className="border-b hover:bg-gray-50">
                      <td className="p-4 font-medium">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700">
                          عميل #{client.id}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          <User className="h-5 w-5 mr-2 text-gray-400" />
                          <span className="font-medium text-lg">{client.name}</span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <Badge className="bg-gray-100 text-gray-800 text-sm px-3 py-1">
                          {client.id_number}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex items-center justify-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-400" />
                          <span className="text-lg">{client.phone}</span>
                        </div>
                      </td>
                      <td className="text-center p-4">
                        {client.parent_account_code ? (
                          <div className="flex flex-col items-center">
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 mb-1">
                              {client.parent_account_code}
                            </Badge>
                            <span className="text-xs text-gray-600 max-w-[120px] truncate" title={client.parent_account_name}>
                              {client.parent_account_name}
                            </span>
                          </div>
                        ) : (
                          <Badge variant="outline" className="bg-gray-100 text-gray-500">
                            غير مربوط
                          </Badge>
                        )}
                      </td>
                      <td className="text-center p-4">
                        <div className="flex flex-col items-center">
                          <span className={`font-medium text-sm ${
                            (client.current_balance || 0) > 0 ? 'text-green-600' :
                            (client.current_balance || 0) < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {(client.current_balance || 0).toLocaleString()} ر.ي
                          </span>
                          {client.sub_account_code && (
                            <span className="text-xs text-gray-500 font-mono">
                              {client.sub_account_code}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="text-center p-4">
                        <Badge className={client.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                          {client.is_active ? 'نشط' : 'غير نشط'}
                        </Badge>
                      </td>
                      <td className="text-center p-4">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleView(client)}
                            className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 hover:border-blue-300"
                            title="عرض تفاصيل الموكل"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(client)}
                            className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200 hover:border-yellow-300"
                            title="تعديل بيانات الموكل"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(client.id)}
                            className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200 hover:border-red-300"
                            title="حذف الموكل"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            )}

            {/* عرض رسالة عدم وجود بيانات */}
            {!dbError && !isLoading && clients.length === 0 && (
              <div className="text-center py-8">
                <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد موكلين</h3>
                <p className="text-gray-600">لم يتم العثور على أي موكلين في قاعدة البيانات</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Modal للإضافة/التعديل/المشاهدة */}
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {modalType === 'add' && 'إضافة موكل جديد'}
                  {modalType === 'edit' && 'تعديل بيانات الموكل'}
                  {modalType === 'view' && 'عرض بيانات الموكل'}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {modalType === 'view' && editingClient ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>الاسم</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.name}</p>
                    </div>
                    <div>
                      <Label>رقم الهوية</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.id_number}</p>
                    </div>
                    <div>
                      <Label>الهاتف</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.phone}</p>
                    </div>
                    <div>
                      <Label>البريد الإلكتروني</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.email}</p>
                    </div>
                    <div>
                      <Label>نوع الموكل</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">
                        {editingClient.client_type === 'individual' ? 'فرد' : 'شركة'}
                      </p>
                    </div>
                    <div>
                      <Label>اسم الدخول</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.username}</p>
                    </div>
                    <div>
                      <Label>كلمة المرور</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">••••••••</p>
                    </div>
                    <div>
                      <Label>الحالة</Label>
                      <Badge className={editingClient.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {editingClient.status === 'active' ? 'نشط' : 'غير نشط'}
                      </Badge>
                    </div>
                    <div>
                      <Label>عدد القضايا</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.cases_count || 0}</p>
                    </div>
                    <div>
                      <Label>تاريخ التسجيل</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.created_date}</p>
                    </div>
                    <div>
                      <Label>آخر دخول</Label>
                      <p className="mt-1 p-2 bg-gray-50 rounded">
                        {editingClient.last_login ? new Date(editingClient.last_login).toLocaleString('ar-SA') : 'لم يسجل دخول بعد'}
                      </p>
                    </div>
                    <div>
                      <Label>حالة الاتصال</Label>
                      <Badge className={editingClient.is_online ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {editingClient.is_online ? 'متصل' : 'غير متصل'}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label>العنوان</Label>
                    <p className="mt-1 p-2 bg-gray-50 rounded">{editingClient.address}</p>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">الاسم *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="id_number">رقم الهوية *</Label>
                      <Input
                        id="id_number"
                        value={formData.id_number}
                        onChange={(e) => setFormData({...formData, id_number: e.target.value})}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="client_type">نوع الموكل</Label>
                      <select
                        id="client_type"
                        value={formData.client_type}
                        onChange={(e) => setFormData({...formData, client_type: e.target.value})}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="individual">فرد</option>
                        <option value="company">شركة</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="username">اسم الدخول *</Label>
                      <Input
                        id="username"
                        value={formData.username}
                        onChange={(e) => setFormData({...formData, username: e.target.value})}
                        required
                        placeholder="اسم المستخدم للدخول"
                      />
                    </div>
                    <div>
                      <Label htmlFor="password">كلمة المرور *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => setFormData({...formData, password: e.target.value})}
                        required={modalType === 'add'}
                        placeholder={modalType === 'edit' ? 'اتركها فارغة للاحتفاظ بالحالية' : 'كلمة المرور'}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">العنوان</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({...formData, address: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="status">الحالة</Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => setFormData({...formData, status: e.target.value as 'active' | 'inactive'})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="active">نشط</option>
                      <option value="inactive">غير نشط</option>
                    </select>
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button type="submit" className="flex-1">
                      <Save className="h-4 w-4 mr-2" />
                      {modalType === 'add' ? 'إضافة' : 'تحديث'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsModalOpen(false)}
                      className="flex-1"
                    >
                      إلغاء
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
