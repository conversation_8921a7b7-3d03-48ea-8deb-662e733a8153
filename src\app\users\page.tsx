'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Users,
  UserPlus,
  Edit,
  Trash2,
  Eye,
  Search,
  Crown,
  User,
  Shield,
  AlertCircle
} from 'lucide-react'

interface SystemUser {
  id: number
  username: string
  name: string
  position: string
  role: string
  role_display_name: string
  user_type: 'admin' | 'user'
  permissions: string[]
  status: string
  is_active: boolean
  last_login: string
  created_date: string
  employee_id?: number
}

interface Employee {
  id: number
  name: string
  position: string
  department: string
  phone: string
  email: string
}

interface UserRole {
  role_name: string
  display_name: string
  description: string
  permissions: string[]
}

function UsersPageContent() {
  const { user: currentUser, isAdmin } = useAuth()
  const [users, setUsers] = useState<SystemUser[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [roles, setRoles] = useState<UserRole[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('all')
  const [filterType, setFilterType] = useState('all')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [selectedUser, setSelectedUser] = useState<SystemUser | null>(null)

  // بيانات النموذج
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    employee_id: '',
    role: 'user',
    user_type: 'user' as 'admin' | 'user',
    permissions: [] as string[],
    is_active: true
  })

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    if (currentUser) {
      fetchUsers()
      fetchEmployees()
      fetchRoles()
    }
  }, [currentUser])

  // جلب المستخدمين
  const fetchUsers = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${currentUser?.token}`
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setUsers(result.data)
        }
      }
    } catch (error) {
      console.error('خطأ في جلب المستخدمين:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // جلب الموظفين
  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees', {
        headers: {
          'Authorization': `Bearer ${currentUser?.token}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // التحقق من وجود البيانات في المكان الصحيح
          const employeeData = result.employees || result.data || []
          if (Array.isArray(employeeData)) {
            setEmployees(employeeData)
          } else {
            console.error('بيانات الموظفين ليست مصفوفة:', employeeData)
            setEmployees([])
          }
        } else {
          console.error('فشل في جلب الموظفين:', result.error)
          setEmployees([])
        }
      } else {
        console.error('خطأ HTTP في جلب الموظفين:', response.status)
        setEmployees([])
      }
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error)
      setEmployees([])
    }
  }

  // جلب الأدوار
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/user-roles', {
        headers: {
          'Authorization': `Bearer ${currentUser?.token}`
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setRoles(result.data)
        }
      }
    } catch (error) {
      console.error('خطأ في جلب الأدوار:', error)
    }
  }

  // فتح نافذة إضافة مستخدم
  const handleAddUser = () => {
    setModalType('add')
    setSelectedUser(null)
    setFormData({
      username: '',
      password: '',
      employee_id: '',
      role: 'user',
      user_type: 'user',
      permissions: [],
      is_active: true
    })
    setIsModalOpen(true)
  }

  // فتح نافذة تعديل مستخدم
  const handleEditUser = (user: SystemUser) => {
    setModalType('edit')
    setSelectedUser(user)
    setFormData({
      username: user.username,
      password: '',
      employee_id: user.employee_id?.toString() || '',
      role: user.role,
      user_type: user.user_type,
      permissions: user.permissions || [],
      is_active: user.is_active
    })
    setIsModalOpen(true)
  }

  // فتح نافذة عرض مستخدم
  const handleViewUser = (user: SystemUser) => {
    setModalType('view')
    setSelectedUser(user)
    setIsModalOpen(true)
  }

  // حذف مستخدم
  const handleDeleteUser = async (userId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return

    try {
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${currentUser?.token}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          alert('تم حذف المستخدم بنجاح')
          fetchUsers()
        } else {
          alert(result.error || 'فشل في حذف المستخدم')
        }
      }
    } catch (error) {
      console.error('خطأ في حذف المستخدم:', error)
      alert('حدث خطأ في حذف المستخدم')
    }
  }

  // حفظ المستخدم
  const handleSaveUser = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = modalType === 'add' ? '/api/users' : `/api/users/${selectedUser?.id}`
      const method = modalType === 'add' ? 'POST' : 'PUT'

      // إذا كان نوع المستخدم مدير، إعطاء جميع الصلاحيات
      let finalPermissions = formData.permissions
      if (formData.user_type === 'admin') {
        const selectedRole = roles.find(r => r.role_name === formData.role)
        finalPermissions = selectedRole?.permissions || [
          'manage_users', 'manage_follows', 'manage_cases', 
          'manage_clients', 'manage_accounting', 'view_all_reports'
        ]
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser?.token}`
        },
        body: JSON.stringify({
          ...formData,
          permissions: finalPermissions
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          alert(modalType === 'add' ? 'تم إضافة المستخدم بنجاح' : 'تم تحديث المستخدم بنجاح')
          setIsModalOpen(false)
          fetchUsers()
        } else {
          alert(result.error || 'فشل في حفظ المستخدم')
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ المستخدم:', error)
      alert('حدث خطأ في حفظ المستخدم')
    }
  }

  // تحديث الصلاحيات عند تغيير الدور
  const handleRoleChange = (roleValue: string) => {
    setFormData(prev => ({ ...prev, role: roleValue }))
    
    const selectedRole = roles.find(r => r.role_name === roleValue)
    if (selectedRole) {
      setFormData(prev => ({ 
        ...prev, 
        permissions: prev.user_type === 'admin' ? selectedRole.permissions : selectedRole.permissions 
      }))
    }
  }

  // تحديث الصلاحيات عند تغيير نوع المستخدم
  const handleUserTypeChange = (userType: 'admin' | 'user') => {
    setFormData(prev => ({ ...prev, user_type: userType }))
    
    if (userType === 'admin') {
      const selectedRole = roles.find(r => r.role_name === formData.role)
      if (selectedRole) {
        setFormData(prev => ({ 
          ...prev, 
          permissions: selectedRole.permissions 
        }))
      }
    }
  }

  // تصفية المستخدمين
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.position?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = filterRole === 'all' || user.role === filterRole
    const matchesType = filterType === 'all' || user.user_type === filterType
    
    return matchesSearch && matchesRole && matchesType
  })

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    if (!dateString) return 'غير محدد'
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  // تنسيق حالة المستخدم
  const getStatusBadge = (user: SystemUser) => {
    if (!user.is_active) {
      return <Badge className="bg-red-100 text-red-800">غير نشط</Badge>
    }
    if (user.status === 'active') {
      return <Badge className="bg-green-100 text-green-800">نشط</Badge>
    }
    return <Badge className="bg-gray-100 text-gray-800">{user.status}</Badge>
  }

  // تنسيق نوع المستخدم
  const getUserTypeBadge = (userType: string) => {
    if (userType === 'admin') {
      return (
        <Badge className="bg-purple-100 text-purple-800">
          <Crown className="h-3 w-3 mr-1" />
          مدير
        </Badge>
      )
    }
    return (
      <Badge className="bg-blue-100 text-blue-800">
        <User className="h-3 w-3 mr-1" />
        مستخدم عادي
      </Badge>
    )
  }

  // 🚨 إيقاف فحص isAdmin مؤقتاً - السماح لجميع المستخدمين بالوصول
  /*
  // 🚨 إيقاف فحص isAdmin مؤقتاً - السماح لجميع المستخدمين بالوصول
  /*
  if (!isAdmin()) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-red-800 mb-2">
              غير مخول للوصول
            </h2>
            <p className="text-red-700 text-sm">
              هذه الصفحة مخصصة للمديرين فقط.
            </p>
          </div>
        </div>
      </div>
    )
  }
  */

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والإحصائيات */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Users className="h-8 w-8 mr-3 text-blue-600" />
              إدارة المستخدمين
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة حسابات المستخدمين والصلاحيات
            </p>
          </div>
          
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{users.length}</div>
              <div className="text-sm text-gray-600">إجمالي المستخدمين</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {users.filter(u => u.is_active).length}
              </div>
              <div className="text-sm text-gray-600">المستخدمين النشطين</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {users.filter(u => u.user_type === 'admin').length}
              </div>
              <div className="text-sm text-gray-600">المديرين</div>
            </div>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex flex-1 items-center space-x-4 space-x-reverse">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المستخدمين..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
                
                <Select value={filterRole} onValueChange={setFilterRole}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="تصفية بالدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأدوار</SelectItem>
                    {roles.map(role => (
                      <SelectItem key={role.role_name} value={role.role_name}>
                        {role.display_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="تصفية بالنوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="admin">مدير</SelectItem>
                    <SelectItem value="user">مستخدم عادي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={handleAddUser} className="bg-blue-600 hover:bg-blue-700">
                <UserPlus className="h-4 w-4 mr-2" />
                إضافة مستخدم جديد
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* جدول المستخدمين */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              قائمة المستخدمين ({filteredUsers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد مستخدمين</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3 font-semibold">المستخدم</th>
                      <th className="text-center p-3 font-semibold">النوع</th>
                      <th className="text-center p-3 font-semibold">الدور</th>
                      <th className="text-center p-3 font-semibold">المنصب</th>
                      <th className="text-center p-3 font-semibold">الحالة</th>
                      <th className="text-center p-3 font-semibold">آخر دخول</th>
                      <th className="text-center p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                              {user.user_type === 'admin' ? (
                                <Crown className="h-5 w-5 text-purple-600" />
                              ) : (
                                <User className="h-5 w-5 text-blue-600" />
                              )}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">@{user.username}</div>
                            </div>
                          </div>
                        </td>
                        <td className="text-center p-3">
                          {getUserTypeBadge(user.user_type)}
                        </td>
                        <td className="text-center p-3">
                          <Badge className="bg-gray-100 text-gray-800">
                            {user.role_display_name}
                          </Badge>
                        </td>
                        <td className="text-center p-3">{user.position || 'غير محدد'}</td>
                        <td className="text-center p-3">
                          {getStatusBadge(user)}
                        </td>
                        <td className="text-center p-3 text-sm text-gray-600">
                          {formatDate(user.last_login)}
                        </td>
                        <td className="text-center p-3">
                          <div className="flex items-center justify-center space-x-2 space-x-reverse">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleViewUser(user)}
                              className="bg-blue-50 hover:bg-blue-100 text-blue-700"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditUser(user)}
                              className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {user.id !== currentUser?.id && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteUser(user.id)}
                                className="bg-red-50 hover:bg-red-100 text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إضافة/تعديل/عرض المستخدم */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {modalType === 'add' && 'إضافة مستخدم جديد'}
                {modalType === 'edit' && 'تعديل المستخدم'}
                {modalType === 'view' && 'عرض بيانات المستخدم'}
              </DialogTitle>
            </DialogHeader>

            {modalType === 'view' && selectedUser ? (
              // عرض بيانات المستخدم
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">الاسم</Label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">اسم المستخدم</Label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.username}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">النوع</Label>
                    <div className="mt-1">{getUserTypeBadge(selectedUser.user_type)}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">الدور</Label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.role_display_name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">المنصب</Label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.position || 'غير محدد'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">الحالة</Label>
                    <div className="mt-1">{getStatusBadge(selectedUser)}</div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">تاريخ الإنشاء</Label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(selectedUser.created_date)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">آخر دخول</Label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(selectedUser.last_login)}</p>
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">الصلاحيات</Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedUser.permissions?.map(permission => (
                      <Badge key={permission} className="bg-green-100 text-green-800">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              // نموذج إضافة/تعديل المستخدم
              <form onSubmit={handleSaveUser} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="username">اسم المستخدم *</Label>
                    <Input
                      id="username"
                      value={formData.username}
                      onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                      required
                      disabled={modalType === 'edit'}
                    />
                  </div>
                  
                  {modalType === 'add' && (
                    <div>
                      <Label htmlFor="password">كلمة المرور *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                        required
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employee_id">الموظف</Label>
                    <Select 
                      value={formData.employee_id} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, employee_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الموظف" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.isArray(employees) ? employees.map(employee => (
                          <SelectItem key={employee.id} value={employee.id.toString()}>
                            {employee.name} - {employee.position}
                          </SelectItem>
                        )) : []}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="user_type">نوع المستخدم *</Label>
                    <Select 
                      value={formData.user_type} 
                      onValueChange={handleUserTypeChange}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user">مستخدم عادي</SelectItem>
                        <SelectItem value="admin">مدير</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="role">الدور *</Label>
                  <Select 
                    value={formData.role} 
                    onValueChange={handleRoleChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map(role => (
                        <SelectItem key={role.role_name} value={role.role_name}>
                          {role.display_name} - {role.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>الصلاحيات</Label>
                  <div className="mt-2 p-3 border rounded-lg bg-gray-50">
                    <div className="flex flex-wrap gap-2">
                      {formData.permissions.map(permission => (
                        <Badge key={permission} className="bg-green-100 text-green-800">
                          <Shield className="h-3 w-3 mr-1" />
                          {permission}
                        </Badge>
                      ))}
                    </div>
                    {formData.user_type === 'admin' && (
                      <p className="text-sm text-purple-600 mt-2">
                        <Crown className="h-4 w-4 inline mr-1" />
                        المديرين لديهم جميع الصلاحيات تلقائياً
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="is_active">المستخدم نشط</Label>
                </div>

                <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                  <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                    إلغاء
                  </Button>
                  <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                    {modalType === 'add' ? 'إضافة' : 'تحديث'}
                  </Button>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

export default function UsersPage() {
  // 🚨 إيقاف ProtectedRoute مؤقتاً - السماح لجميع المستخدمين بالوصول
  return <UsersPageContent />

  // الكود الأصلي (معطل مؤقتاً):
  /*
  return (
    <ProtectedRoute
      userType="user"
      requiredPermissions={['manage_users', 'system_admin']}
    >
      <UsersPageContent />
    </ProtectedRoute>
  )
  */
}