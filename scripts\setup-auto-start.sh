#!/bin/bash

# سكريبت إعداد التشغيل التلقائي للنظام القانوني
# Legal System Auto-Start Setup Script

echo "🚀 إعداد التشغيل التلقائي للنظام القانوني..."
echo "================================================"

# التحقق من صلاحيات المدير
if [ "$EUID" -eq 0 ]; then
    echo "❌ لا تشغل هذا السكريبت كمدير (root)"
    echo "استخدم: ./scripts/setup-auto-start.sh"
    exit 1
fi

# مسار المشروع
PROJECT_DIR="/home/<USER>/Downloads/legal-system"
SERVICE_FILE="legal-system.service"

echo "📁 مسار المشروع: $PROJECT_DIR"

# التحقق من وجود المشروع
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ مجلد المشروع غير موجود: $PROJECT_DIR"
    exit 1
fi

# التحقق من وجود ملف الخدمة
if [ ! -f "$SERVICE_FILE" ]; then
    echo "❌ ملف الخدمة غير موجود: $SERVICE_FILE"
    exit 1
fi

echo "✅ تم العثور على ملف الخدمة"

# نسخ ملف الخدمة إلى systemd
echo "📋 نسخ ملف الخدمة إلى systemd..."
sudo cp "$SERVICE_FILE" /etc/systemd/system/

if [ $? -eq 0 ]; then
    echo "✅ تم نسخ ملف الخدمة بنجاح"
else
    echo "❌ فشل في نسخ ملف الخدمة"
    exit 1
fi

# إعادة تحميل systemd
echo "🔄 إعادة تحميل systemd..."
sudo systemctl daemon-reload

if [ $? -eq 0 ]; then
    echo "✅ تم إعادة تحميل systemd بنجاح"
else
    echo "❌ فشل في إعادة تحميل systemd"
    exit 1
fi

# تفعيل الخدمة للتشغيل التلقائي
echo "⚡ تفعيل الخدمة للتشغيل التلقائي..."
sudo systemctl enable legal-system.service

if [ $? -eq 0 ]; then
    echo "✅ تم تفعيل الخدمة للتشغيل التلقائي"
else
    echo "❌ فشل في تفعيل الخدمة"
    exit 1
fi

# بدء الخدمة
echo "🚀 بدء تشغيل الخدمة..."
sudo systemctl start legal-system.service

if [ $? -eq 0 ]; then
    echo "✅ تم بدء تشغيل الخدمة بنجاح"
else
    echo "❌ فشل في بدء تشغيل الخدمة"
    echo "🔍 فحص حالة الخدمة:"
    sudo systemctl status legal-system.service
    exit 1
fi

# التحقق من حالة الخدمة
echo ""
echo "🔍 فحص حالة الخدمة:"
sudo systemctl status legal-system.service --no-pager

echo ""
echo "📊 معلومات الخدمة:"
echo "================================"
echo "🏷️  اسم الخدمة: legal-system.service"
echo "📁 مسار المشروع: $PROJECT_DIR"
echo "🌐 عنوان الخادم: http://localhost:7443"
echo "👤 المستخدم: mohhash"

echo ""
echo "🎛️ أوامر إدارة الخدمة:"
echo "================================"
echo "▶️  بدء الخدمة:        sudo systemctl start legal-system"
echo "⏹️  إيقاف الخدمة:      sudo systemctl stop legal-system"
echo "🔄 إعادة تشغيل:        sudo systemctl restart legal-system"
echo "📊 حالة الخدمة:        sudo systemctl status legal-system"
echo "📋 سجلات الخدمة:       sudo journalctl -u legal-system -f"
echo "❌ تعطيل التشغيل التلقائي: sudo systemctl disable legal-system"

echo ""
echo "🎉 تم إعداد التشغيل التلقائي بنجاح!"
echo "🔄 سيتم تشغيل النظام تلقائياً عند إعادة تشغيل الجهاز"
echo "🌐 يمكنك الوصول للنظام على: http://localhost:7443"

# إنشاء سكريبت مراقبة سريع
echo ""
echo "📝 إنشاء سكريبت مراقبة سريع..."

cat > /home/<USER>/legal-system-monitor.sh << 'EOF'
#!/bin/bash
# سكريبت مراقبة النظام القانوني

echo "📊 حالة النظام القانوني"
echo "========================"

# حالة الخدمة
echo "🔍 حالة الخدمة:"
sudo systemctl is-active legal-system.service

echo ""
echo "📈 معلومات الخدمة:"
sudo systemctl status legal-system.service --no-pager -l

echo ""
echo "🌐 فحص الاتصال:"
if curl -s http://localhost:7443 > /dev/null; then
    echo "✅ الخادم يعمل بشكل طبيعي"
else
    echo "❌ الخادم لا يستجيب"
fi

echo ""
echo "💾 استخدام الذاكرة:"
ps aux | grep -E "(node|npm)" | grep -v grep

echo ""
echo "🔗 الروابط المفيدة:"
echo "النظام: http://localhost:7443"
echo "تسجيل الدخول: http://localhost:7443/login"
EOF

chmod +x /home/<USER>/legal-system-monitor.sh
echo "✅ تم إنشاء سكريبت المراقبة: /home/<USER>/legal-system-monitor.sh"

echo ""
echo "🎯 للمراقبة السريعة استخدم: ./legal-system-monitor.sh"
