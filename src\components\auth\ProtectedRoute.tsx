'use client'

import { useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Loader2 } from 'lucide-react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermissions?: string[]
  userType?: 'user' | 'client'
  fallback?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  requiredPermissions = [], 
  userType,
  fallback 
}: ProtectedRouteProps) {
  const { user, loading, hasAnyPermission } = useAuth()

  // عرض شاشة التحميل أثناء التحقق من المصادقة
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  // إذا لم يكن هناك مستخدم مسجل دخول، سيتم التوجيه تلقائياً بواسطة useAuth
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600">جاري التوجيه لصفحة الدخول...</p>
        </div>
      </div>
    )
  }

  // التحقق من نوع المستخدم
  if (userType && user.type !== userType) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-red-800 mb-2">
              غير مخول للوصول
            </h2>
            <p className="text-red-700 text-sm">
              هذه الصفحة مخصصة {userType === 'user' ? 'للمستخدمين' : 'للعملاء'} فقط.
            </p>
            <p className="text-red-600 text-xs mt-2">
              نوع حسابك الحالي: {user.type === 'user' ? 'مستخدم' : 'عميل'}
            </p>
          </div>
        </div>
      </div>
    )
  }

  // 🚨 إيقاف فحص الصلاحيات مؤقتاً - السماح لجميع المستخدمين بالوصول
  // التحقق من الصلاحيات المطلوبة (معطل مؤقتاً)
  /*
  if (requiredPermissions.length > 0 && !hasAnyPermission(requiredPermissions)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-yellow-800 mb-2">
              صلاحيات غير كافية
            </h2>
            <p className="text-yellow-700 text-sm mb-3">
              تحتاج إلى إحدى الصلاحيات التالية للوصول لهذه الصفحة:
            </p>
            <ul className="text-yellow-600 text-xs space-y-1">
              {requiredPermissions.map(permission => (
                <li key={permission} className="bg-yellow-100 px-2 py-1 rounded">
                  {permission}
                </li>
              ))}
            </ul>
            <div className="mt-4 text-xs text-gray-600">
              <p>دورك الحالي: {user.role_display_name || user.role}</p>
              <p>صلاحياتك: {user.permissions?.join(', ') || 'لا توجد'}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }
  */

  // إذا تم اجتياز جميع الفحوصات، عرض المحتوى
  return <>{children}</>
}