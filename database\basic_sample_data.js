const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123',
  ssl: false
});

async function insertBasicSampleData() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 بدء إدراج البيانات التجريبية الأساسية...');

    // إدراج بيانات تجريبية للوثائق
    await client.query(`
      INSERT INTO documents (
        title, description, file_name, file_path, file_size, file_type, mime_type,
        category, subcategory, tags, access_level, is_confidential
      ) VALUES 
      ('دليل الإجراءات القانونية', 'دليل شامل للإجراءات القانونية في المملكة', 'legal_guide.pdf', '/uploads/documents/legal_guide.pdf', 1024000, 'pdf', 'application/pdf', 'guide', 'legal', ARRAY['دليل', 'إجراءات', 'قانون'], 'public', false),
      ('نموذج عقد استشارة', 'نموذج عقد استشارة قانونية', 'contract_template.pdf', '/uploads/documents/contract_template.pdf', 512000, 'pdf', 'application/pdf', 'template', 'contract', ARRAY['نموذج', 'عقد', 'استشارة'], 'public', false),
      ('وثيقة سرية', 'وثيقة سرية للاستخدام الداخلي', 'confidential_doc.pdf', '/uploads/documents/confidential_doc.pdf', 256000, 'pdf', 'application/pdf', 'internal', 'confidential', ARRAY['سري', 'داخلي'], 'restricted', true),
      ('تقرير مالي', 'تقرير مالي ربع سنوي', 'financial_report.pdf', '/uploads/documents/financial_report.pdf', 2048000, 'pdf', 'application/pdf', 'financial', 'report', ARRAY['مالي', 'تقرير', 'ربع سنوي'], 'private', false),
      ('مذكرة قانونية', 'مذكرة قانونية حول قضية معينة', 'legal_memo.docx', '/uploads/documents/legal_memo.docx', 128000, 'docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'memo', 'legal', ARRAY['مذكرة', 'قانونية'], 'private', false)
      ON CONFLICT DO NOTHING
    `);

    // إدراج بيانات تجريبية لتسجيلات الوقت
    await client.query(`
      INSERT INTO time_entries (
        employee_id, start_time, end_time, duration_minutes,
        task_description, task_category, hourly_rate, billable_amount, is_billable, status
      ) VALUES 
      (1, CURRENT_TIMESTAMP - INTERVAL '3 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours', 60, 'مراجعة الوثائق القانونية', 'documentation', 200.00, 200.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '2 days', CURRENT_TIMESTAMP - INTERVAL '2 days' + INTERVAL '90 minutes', 90, 'اجتماع مع العميل', 'meeting', 200.00, 300.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day' + INTERVAL '45 minutes', 45, 'بحث قانوني', 'research', 200.00, 150.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '4 hours', CURRENT_TIMESTAMP - INTERVAL '3 hours', 60, 'إعداد التقارير', 'documentation', 200.00, 200.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '6 hours', CURRENT_TIMESTAMP - INTERVAL '5 hours', 60, 'مراسلات قانونية', 'correspondence', 200.00, 200.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '30 minutes', NULL, NULL, 'مراجعة ملف قضية جديدة', 'documentation', 200.00, 0.00, true, 'active')
      ON CONFLICT DO NOTHING
    `);

    // إدراج حساب عميل تجريبي
    await client.query(`
      INSERT INTO client_portal_accounts (
        client_id, username, email, password_hash, is_active, is_verified,
        language, timezone, notification_preferences
      ) VALUES 
      (1, 'demo_client', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Txjyvq', true, true,
       'ar', 'Asia/Riyadh', '{"email_notifications": true, "case_updates": true, "document_uploads": true, "appointment_reminders": true, "invoice_notifications": true}')
      ON CONFLICT (client_id) DO NOTHING
    `);

    // إدراج إشعارات تجريبية
    await client.query(`
      INSERT INTO client_notifications (
        client_id, title, message, type, is_read
      ) VALUES 
      (1, 'مرحباً بك في بوابة العملاء', 'تم إنشاء حسابك بنجاح في بوابة العملاء. يمكنك الآن متابعة قضاياك والوصول للوثائق والخدمات المختلفة.', 'success', false),
      (1, 'وثيقة جديدة متاحة', 'تم إضافة وثيقة جديدة لحسابك: دليل الإجراءات القانونية', 'info', false),
      (1, 'تحديث في النظام', 'تم تحديث النظام وإضافة ميزات جديدة لتحسين تجربتك', 'info', true),
      (1, 'تذكير مهم', 'لا تنس مراجعة الوثائق المطلوبة قبل الموعد القادم', 'warning', false),
      (1, 'رسالة من المحامي', 'يرجى التواصل معنا لمناقشة تفاصيل إضافية حول قضيتك', 'info', false)
      ON CONFLICT DO NOTHING
    `);

    // إدراج طلبات تجريبية من العملاء
    await client.query(`
      INSERT INTO client_requests (
        client_id, request_type, title, description, priority, status
      ) VALUES 
      (1, 'document_request', 'طلب نسخة من الوثيقة', 'أحتاج نسخة من دليل الإجراءات القانونية', 'medium', 'pending'),
      (1, 'meeting_request', 'طلب موعد استشارة', 'أرغب في حجز موعد لاستشارة قانونية', 'high', 'in_progress'),
      (1, 'status_inquiry', 'استفسار عن الخدمات', 'ما هي الخدمات المتاحة في البوابة؟', 'low', 'completed'),
      (1, 'document_request', 'طلب وثيقة مخصصة', 'أحتاج إعداد وثيقة قانونية مخصصة', 'high', 'pending'),
      (1, 'status_inquiry', 'استفسار عن الرسوم', 'ما هي رسوم الخدمات المختلفة؟', 'medium', 'in_progress')
      ON CONFLICT DO NOTHING
    `);

    // إدراج جلسة تجريبية للعميل
    await client.query(`
      INSERT INTO client_sessions (
        client_id, session_token, ip_address, user_agent, expires_at
      ) VALUES 
      (1, 'demo_session_token_123456789abcdef', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', CURRENT_TIMESTAMP + INTERVAL '24 hours')
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ تم إدراج جميع البيانات التجريبية بنجاح!');
    console.log('📋 البيانات المُدرجة:');
    console.log('   - 5 وثائق تجريبية (دليل، نموذج، وثيقة سرية، تقرير مالي، مذكرة)');
    console.log('   - 6 تسجيلات وقت (5 مكتملة + 1 نشط)');
    console.log('   - حساب عميل واحد (demo_client)');
    console.log('   - 5 إشعارات للعميل');
    console.log('   - 5 طلبات من العميل');
    console.log('   - جلسة واحدة نشطة للعميل');

  } catch (error) {
    console.error('❌ خطأ في إدراج البيانات التجريبية:', error);
    throw error;
  } finally {
    client.release();
  }
}

// تشغيل الدالة
insertBasicSampleData()
  .then(() => {
    console.log('🎉 تم الانتهاء من إدراج البيانات التجريبية الأساسية!');
    console.log('💡 يمكنك الآن اختبار الميزات التالية:');
    console.log('   - إدارة الوثائق: /documents');
    console.log('   - تتبع الوقت: /time-tracking');
    console.log('   - بوابة العملاء: /client-portal');
    console.log('   - إدارة حسابات العملاء: /client-accounts');
    console.log('   - الفواتير: /invoices');
    console.log('');
    console.log('🔐 بيانات تسجيل الدخول للعميل التجريبي:');
    console.log('   اسم المستخدم: demo_client');
    console.log('   كلمة المرور: password123');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 فشل في إدراج البيانات التجريبية:', error);
    process.exit(1);
  });