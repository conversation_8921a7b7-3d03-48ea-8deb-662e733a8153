const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function restoreSampleData() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 استعادة البيانات التجريبية...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. إضافة محافظات
    console.log('🏛️ إضافة المحافظات...');
    const governorates = [
      { name: 'صنعاء', code: 'SAN' },
      { name: 'عدن', code: 'ADE' },
      { name: 'تعز', code: 'TAI' },
      { name: 'الحديدة', code: 'HOD' },
      { name: 'إب', code: 'IBB' },
      { name: 'ذ<PERSON><PERSON><PERSON>', code: 'DHA' },
      { name: 'حضرموت', code: 'HAD' },
      { name: 'لحج', code: 'LAH' },
      { name: 'أبي<PERSON>', code: 'ABI' },
      { name: 'شبوة', code: 'SHA' }
    ];

    for (const gov of governorates) {
      await client.query(`
        INSERT INTO governorates (name, code, created_date)
        VALUES ($1, $2, CURRENT_DATE)
        ON CONFLICT (code) DO NOTHING
      `, [gov.name, gov.code]);
    }
    console.log(`✅ تم إضافة ${governorates.length} محافظة`);

    // 2. إضافة محاكم
    console.log('\n⚖️ إضافة المحاكم...');
    const courts = [
      { name: 'المحكمة العليا', type: 'محكمة عليا', governorate: 'صنعاء', address: 'شارع الزبيري، صنعاء', phone: '01-274856' },
      { name: 'محكمة استئناف صنعاء', type: 'محكمة استئناف', governorate: 'صنعاء', address: 'شارع الستين، صنعاء', phone: '01-274857' },
      { name: 'المحكمة التجارية بصنعاء', type: 'محكمة تجارية', governorate: 'صنعاء', address: 'شارع الحصبة، صنعاء', phone: '01-274858' },
      { name: 'محكمة الأحوال الشخصية', type: 'محكمة أحوال شخصية', governorate: 'صنعاء', address: 'شارع الثورة، صنعاء', phone: '01-274859' },
      { name: 'المحكمة الجنائية', type: 'محكمة جنائية', governorate: 'صنعاء', address: 'شارع الجمهورية، صنعاء', phone: '01-274860' }
    ];

    for (const court of courts) {
      const govResult = await client.query('SELECT id FROM governorates WHERE name = $1', [court.governorate]);
      if (govResult.rows.length > 0) {
        await client.query(`
          INSERT INTO courts (name, type, governorate_id, address, phone, is_active, created_date)
          VALUES ($1, $2, $3, $4, $5, true, CURRENT_DATE)
        `, [court.name, court.type, govResult.rows[0].id, court.address, court.phone]);
      }
    }
    console.log(`✅ تم إضافة ${courts.length} محكمة`);

    // 3. إضافة عملاء
    console.log('\n👥 إضافة العملاء...');
    const clients = [
      { name: 'أحمد محمد علي', phone: '777123456', email: '<EMAIL>', id_number: '01234567890', address: 'شارع الزبيري، صنعاء' },
      { name: 'فاطمة أحمد حسن', phone: '777234567', email: '<EMAIL>', id_number: '01234567891', address: 'شارع الستين، صنعاء' },
      { name: 'محمد عبدالله صالح', phone: '777345678', email: '<EMAIL>', id_number: '01234567892', address: 'شارع الحصبة، صنعاء' },
      { name: 'عائشة علي محمد', phone: '777456789', email: '<EMAIL>', id_number: '01234567893', address: 'شارع الثورة، صنعاء' },
      { name: 'يوسف حسن أحمد', phone: '777567890', email: '<EMAIL>', id_number: '01234567894', address: 'شارع الجمهورية، صنعاء' }
    ];

    for (const clientData of clients) {
      await client.query(`
        INSERT INTO clients (name, phone, email, id_number, address, status, created_date)
        VALUES ($1, $2, $3, $4, $5, 'active', CURRENT_DATE)
      `, [clientData.name, clientData.phone, clientData.email, clientData.id_number, clientData.address]);
    }
    console.log(`✅ تم إضافة ${clients.length} عميل`);

    // 4. إضافة موظفين إضافيين
    console.log('\n👨‍💼 إضافة موظفين إضافيين...');
    const employees = [
      { name: 'سارة أحمد محمد', position: 'محامية', department: 'القسم القانوني', phone: '777111222', email: '<EMAIL>', id_number: '11111111', salary: 35000 },
      { name: 'خالد محمد علي', position: 'محاسب', department: 'المحاسبة', phone: '777222333', email: '<EMAIL>', id_number: '22222222', salary: 30000 },
      { name: 'نادية حسن صالح', position: 'سكرتيرة', department: 'الإدارة', phone: '777333444', email: '<EMAIL>', id_number: '33333333', salary: 25000 },
      { name: 'عمر عبدالله أحمد', position: 'مستشار قانوني', department: 'الاستشارات', phone: '777444555', email: '<EMAIL>', id_number: '44444444', salary: 40000 },
      { name: 'ليلى محمد حسن', position: 'محامية متدربة', department: 'القسم القانوني', phone: '777555666', email: '<EMAIL>', id_number: '55555555', salary: 20000 }
    ];

    const govId = await client.query('SELECT id FROM governorates WHERE name = $1', ['صنعاء']);
    const governorateId = govId.rows[0].id;

    for (let i = 0; i < employees.length; i++) {
      const emp = employees[i];
      await client.query(`
        INSERT INTO employees (
          name, position, department, phone, email, id_number, 
          address, salary, hire_date, employee_number, status, 
          governorate_id, created_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_DATE, $9, 'active', $10, CURRENT_DATE)
      `, [
        emp.name, emp.position, emp.department, emp.phone, emp.email, emp.id_number,
        'صنعاء، اليمن', emp.salary, `EMP00${i + 2}`, governorateId
      ]);
    }
    console.log(`✅ تم إضافة ${employees.length} موظف إضافي`);

    // 5. إضافة خدمات
    console.log('\n🛠️ إضافة الخدمات...');
    const services = [
      { name: 'استشارة قانونية' },
      { name: 'صياغة عقود' },
      { name: 'تمثيل قانوني' },
      { name: 'دراسة قانونية' },
      { name: 'توثيق مستندات' },
      { name: 'تأسيس شركات' },
      { name: 'قضايا عمالية' },
      { name: 'قضايا تجارية' }
    ];

    for (const service of services) {
      await client.query(`
        INSERT INTO services (name, created_date)
        VALUES ($1, CURRENT_DATE)
      `, [service.name]);
    }
    console.log(`✅ تم إضافة ${services.length} خدمة`);

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم استعادة البيانات التجريبية بنجاح!');
    console.log('================================');
    console.log('📊 ملخص البيانات المستعادة:');
    console.log(`🏛️ المحافظات: ${governorates.length}`);
    console.log(`⚖️ المحاكم: ${courts.length}`);
    console.log(`👥 العملاء: ${clients.length}`);
    console.log(`👨‍💼 الموظفين الإضافيين: ${employees.length}`);
    console.log(`🛠️ الخدمات: ${services.length}`);
    console.log('================================');

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في استعادة البيانات:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

restoreSampleData().catch(console.error);
