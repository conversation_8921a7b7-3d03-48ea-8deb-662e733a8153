const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function addCasesAndFollows() {
  const client = await pool.connect();
  
  try {
    console.log('📋 إضافة قضايا ومتابعات تجريبية...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. جلب معرفات العملاء والمحاكم والموظفين
    const clientsResult = await client.query('SELECT id, name FROM clients LIMIT 5');
    const courtsResult = await client.query('SELECT id, name FROM courts LIMIT 5');
    const employeesResult = await client.query('SELECT id, name FROM employees LIMIT 6');

    const clients = clientsResult.rows;
    const courts = courtsResult.rows;
    const employees = employeesResult.rows;

    console.log(`📊 البيانات المتاحة: ${clients.length} عميل، ${courts.length} محكمة، ${employees.length} موظف`);

    // 2. إضافة قضايا
    console.log('\n⚖️ إضافة القضايا...');
    const cases = [
      {
        case_number: 'CASE-2024-001',
        title: 'قضية نزاع تجاري',
        case_type: 'تجاري',
        status: 'جاري',
        priority: 'عالية',
        description: 'نزاع تجاري بين شركتين حول عقد توريد'
      },
      {
        case_number: 'CASE-2024-002',
        title: 'قضية عمالية',
        case_type: 'عمالي',
        status: 'جديد',
        priority: 'متوسطة',
        description: 'نزاع عمالي حول مستحقات الموظف'
      },
      {
        case_number: 'CASE-2024-003',
        title: 'قضية أحوال شخصية',
        case_type: 'أحوال شخصية',
        status: 'مكتمل',
        priority: 'منخفضة',
        description: 'قضية طلاق وحضانة أطفال'
      },
      {
        case_number: 'CASE-2024-004',
        title: 'قضية عقارية',
        case_type: 'عقاري',
        status: 'جاري',
        priority: 'عالية',
        description: 'نزاع حول ملكية عقار'
      },
      {
        case_number: 'CASE-2024-005',
        title: 'قضية جنائية',
        case_type: 'جنائي',
        status: 'معلق',
        priority: 'عالية',
        description: 'قضية احتيال مالي'
      }
    ];

    for (let i = 0; i < cases.length && i < clients.length && i < courts.length; i++) {
      const caseData = cases[i];
      const client_id = clients[i].id;
      const court_id = courts[i % courts.length].id;
      const employee_id = employees[i % employees.length].id;

      await client.query(`
        INSERT INTO issues (
          case_number, title, issue_type, status, description,
          client_id, court_id, created_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_DATE)
      `, [
        caseData.case_number, caseData.title, caseData.case_type, caseData.status,
        caseData.description, client_id, court_id
      ]);
    }
    console.log(`✅ تم إضافة ${cases.length} قضية`);

    // 3. إضافة جلسات محاكمة
    console.log('\n🏛️ إضافة جلسات المحاكمة...');
    
    const issuesResult = await client.query('SELECT id, case_number FROM issues LIMIT 5');
    const issues = issuesResult.rows;

    const hearings = [
      {
        hearing_date: '2024-09-01',
        hearing_time: '10:00:00',
        hearing_type: 'جلسة أولى',
        status: 'مجدولة',
        notes: 'الجلسة الأولى لمناقشة القضية'
      },
      {
        hearing_date: '2024-09-15',
        hearing_time: '11:00:00',
        hearing_type: 'جلسة مرافعة',
        status: 'مجدولة',
        notes: 'جلسة مرافعة الدفاع'
      },
      {
        hearing_date: '2024-08-20',
        hearing_time: '09:00:00',
        hearing_type: 'جلسة حكم',
        status: 'مكتملة',
        notes: 'تم إصدار الحكم'
      },
      {
        hearing_date: '2024-09-30',
        hearing_time: '14:00:00',
        hearing_type: 'جلسة استئناف',
        status: 'مجدولة',
        notes: 'جلسة استئناف الحكم'
      },
      {
        hearing_date: '2024-10-10',
        hearing_time: '10:30:00',
        hearing_type: 'جلسة تنفيذ',
        status: 'مجدولة',
        notes: 'جلسة تنفيذ الحكم'
      }
    ];

    for (let i = 0; i < hearings.length && i < issues.length; i++) {
      const hearing = hearings[i];
      const issue_id = issues[i].id;
      const court_id = courts[i % courts.length].id;

      const court_name = courts[i % courts.length].name;

      await client.query(`
        INSERT INTO hearings (
          issue_id, court_name, hearing_date, hearing_time, hearing_type,
          status, notes, created_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_DATE)
      `, [
        issue_id, court_name, hearing.hearing_date, hearing.hearing_time,
        hearing.hearing_type, hearing.status, hearing.notes
      ]);
    }
    console.log(`✅ تم إضافة ${hearings.length} جلسة محاكمة`);

    // 4. إضافة متابعات
    console.log('\n📝 إضافة المتابعات...');
    
    const follows = [
      {
        follow_type: 'اتصال هاتفي',
        description: 'تم التواصل مع العميل لمناقشة تطورات القضية',
        status: 'مكتمل',
        priority: 'متوسطة'
      },
      {
        follow_type: 'اجتماع',
        description: 'اجتماع مع العميل في المكتب لمراجعة الأوراق',
        status: 'مجدول',
        priority: 'عالية'
      },
      {
        follow_type: 'بريد إلكتروني',
        description: 'إرسال تحديث للعميل حول موعد الجلسة القادمة',
        status: 'مكتمل',
        priority: 'منخفضة'
      },
      {
        follow_type: 'زيارة ميدانية',
        description: 'زيارة موقع النزاع لجمع الأدلة',
        status: 'جاري',
        priority: 'عالية'
      },
      {
        follow_type: 'استشارة',
        description: 'استشارة قانونية مع خبير في المجال',
        status: 'مجدول',
        priority: 'متوسطة'
      }
    ];

    for (let i = 0; i < follows.length && i < issues.length; i++) {
      const follow = follows[i];
      const issue_id = issues[i].id;
      const user_id = employees[i % employees.length].id;

      await client.query(`
        INSERT INTO follows (
          case_id, user_id, service_type, description, status, priority,
          date_field, created_date
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_DATE, CURRENT_DATE)
      `, [
        issue_id, user_id, follow.follow_type, follow.description,
        follow.status, follow.priority
      ]);
    }
    console.log(`✅ تم إضافة ${follows.length} متابعة`);

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم إضافة القضايا والمتابعات بنجاح!');
    console.log('================================');
    console.log('📊 ملخص البيانات المضافة:');
    console.log(`⚖️ القضايا: ${cases.length}`);
    console.log(`🏛️ جلسات المحاكمة: ${hearings.length}`);
    console.log(`📝 المتابعات: ${follows.length}`);
    console.log('================================');

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إضافة القضايا والمتابعات:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addCasesAndFollows().catch(console.error);
