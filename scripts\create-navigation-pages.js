const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function createNavigationPages() {
  const client = await pool.connect();
  
  try {
    console.log('🧭 إنشاء بيانات صفحات التنقل...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // حذف البيانات الموجودة
    await client.query('DELETE FROM navigation_pages');
    console.log('🧹 تم حذف البيانات الموجودة');

    // صفحات التنقل
    const pages = [
      // الصفحة الرئيسية
      { name: 'الصفحة الرئيسية', url: '/', category: 'رئيسية', description: 'الصفحة الرئيسية للنظام', icon: 'Home', order: 1 },
      { name: 'لوحة التحكم', url: '/dashboard', category: 'رئيسية', description: 'لوحة التحكم الرئيسية', icon: 'LayoutDashboard', order: 2 },

      // إدارة البيانات الأساسية
      { name: 'إدارة المحافظات', url: '/governorates', category: 'البيانات الأساسية', description: 'إدارة المحافظات اليمنية', icon: 'MapPin', order: 10 },
      { name: 'إدارة المحاكم', url: '/courts', category: 'البيانات الأساسية', description: 'إدارة بيانات المحاكم', icon: 'Building', order: 11 },
      { name: 'إدارة الفروع', url: '/branches', category: 'البيانات الأساسية', description: 'إدارة فروع المكتب', icon: 'GitBranch', order: 12 },
      { name: 'أنواع القضايا', url: '/issue-types', category: 'البيانات الأساسية', description: 'إدارة أنواع القضايا', icon: 'FileText', order: 13 },
      { name: 'الخدمات القانونية', url: '/services', category: 'البيانات الأساسية', description: 'إدارة الخدمات القانونية', icon: 'Briefcase', order: 14 },

      // إدارة الأشخاص
      { name: 'إدارة الموظفين', url: '/employees', category: 'إدارة الأشخاص', description: 'إدارة بيانات الموظفين', icon: 'Users', order: 20 },
      { name: 'إدارة العملاء', url: '/clients', category: 'إدارة الأشخاص', description: 'إدارة بيانات العملاء', icon: 'UserCheck', order: 21 },
      { name: 'إدارة المستخدمين', url: '/users', category: 'إدارة الأشخاص', description: 'إدارة مستخدمي النظام', icon: 'UserCog', order: 22 },

      // إدارة القضايا
      { name: 'إدارة القضايا', url: '/issues', category: 'إدارة القضايا', description: 'إدارة القضايا القانونية', icon: 'Scale', order: 30 },
      { name: 'إضافة قضية جديدة', url: '/issues/add', category: 'إدارة القضايا', description: 'إضافة قضية قانونية جديدة', icon: 'Plus', order: 31 },
      { name: 'توزيع القضايا', url: '/case-distribution', category: 'إدارة القضايا', description: 'توزيع القضايا على المحامين', icon: 'Shuffle', order: 32 },
      { name: 'المتابعات', url: '/follows', category: 'إدارة القضايا', description: 'متابعة القضايا والمهام', icon: 'CheckSquare', order: 33 },

      // المحاسبة
      { name: 'النظام المحاسبي', url: '/accounting', category: 'المحاسبة', description: 'النظام المحاسبي الرئيسي', icon: 'Calculator', order: 40 },
      { name: 'دليل الحسابات', url: '/accounting/chart-of-accounts', category: 'المحاسبة', description: 'إدارة دليل الحسابات', icon: 'BookOpen', order: 41 },
      { name: 'الحسابات الرئيسية', url: '/accounting/main-accounts', category: 'المحاسبة', description: 'إدارة الحسابات الرئيسية', icon: 'Link2', order: 42 },
      { name: 'الأرصدة الافتتاحية', url: '/opening-balances', category: 'المحاسبة', description: 'إدارة الأرصدة الافتتاحية', icon: 'DollarSign', order: 43 },
      { name: 'القيود اليومية', url: '/journal-entries-new', category: 'المحاسبة', description: 'إدارة القيود اليومية', icon: 'FileEdit', order: 44 },
      { name: 'ميزان المراجعة', url: '/trial-balance', category: 'المحاسبة', description: 'ميزان المراجعة', icon: 'BarChart3', order: 45 },

      // السندات والمدفوعات
      { name: 'سندات القبض', url: '/receipt-vouchers', category: 'السندات والمدفوعات', description: 'إدارة سندات القبض', icon: 'Receipt', order: 50 },
      { name: 'سندات الصرف', url: '/payment-vouchers', category: 'السندات والمدفوعات', description: 'إدارة سندات الصرف', icon: 'CreditCard', order: 51 },
      { name: 'الفواتير', url: '/invoices', category: 'السندات والمدفوعات', description: 'إدارة الفواتير', icon: 'FileText', order: 52 },
      { name: 'حسابات العملاء', url: '/client-accounts', category: 'السندات والمدفوعات', description: 'حسابات العملاء المالية', icon: 'Wallet', order: 53 },

      // التقارير
      { name: 'التقارير المالية', url: '/financial-reports', category: 'التقارير', description: 'التقارير المالية', icon: 'TrendingUp', order: 60 },
      { name: 'تقارير الموظفين', url: '/employee-reports', category: 'التقارير', description: 'تقارير الموظفين', icon: 'Users', order: 61 },
      { name: 'تقارير القضايا', url: '/case-reports', category: 'التقارير', description: 'تقارير القضايا', icon: 'BarChart', order: 62 },
      { name: 'أرباح المحامين', url: '/lawyer-earnings', category: 'التقارير', description: 'تقارير أرباح المحامين', icon: 'PieChart', order: 63 },

      // الوثائق والملفات
      { name: 'إدارة الوثائق', url: '/documents', category: 'الوثائق والملفات', description: 'إدارة الوثائق والملفات', icon: 'FolderOpen', order: 70 },
      { name: 'رفع الوثائق', url: '/documents/upload', category: 'الوثائق والملفات', description: 'رفع الوثائق الجديدة', icon: 'Upload', order: 71 },
      { name: 'أرشيف الوثائق', url: '/documents/archive', category: 'الوثائق والملفات', description: 'أرشيف الوثائق', icon: 'Archive', order: 72 },

      // بوابة العملاء
      { name: 'بوابة العملاء', url: '/client-portal', category: 'بوابة العملاء', description: 'بوابة العملاء الإلكترونية', icon: 'Globe', order: 80 },
      { name: 'تسجيل دخول العملاء', url: '/client-login', category: 'بوابة العملاء', description: 'صفحة تسجيل دخول العملاء', icon: 'LogIn', order: 81 },

      // الإعدادات
      { name: 'إعدادات النظام', url: '/settings', category: 'الإعدادات', description: 'إعدادات النظام العامة', icon: 'Settings', order: 90 },
      { name: 'إدارة صفحات التنقل', url: '/settings/navigation-pages', category: 'الإعدادات', description: 'إدارة صفحات التنقل', icon: 'Navigation', order: 91 },
      { name: 'مراكز التكلفة', url: '/settings/cost-centers', category: 'الإعدادات', description: 'إدارة مراكز التكلفة', icon: 'Target', order: 92 },
      { name: 'الإعلانات', url: '/settings/announcements', category: 'الإعدادات', description: 'إدارة الإعلانات', icon: 'Megaphone', order: 93 },

      // الإدارة
      { name: 'إدارة قاعدة البيانات', url: '/admin/database', category: 'الإدارة', description: 'إدارة قاعدة البيانات', icon: 'Database', order: 100 },
      { name: 'عارض قاعدة البيانات', url: '/admin/database-viewer', category: 'الإدارة', description: 'عرض بيانات قاعدة البيانات', icon: 'Eye', order: 101 },
      { name: 'إعدادات الذكاء الاصطناعي', url: '/admin/ai-settings', category: 'الإدارة', description: 'إعدادات الذكاء الاصطناعي', icon: 'Brain', order: 102 },

      // أخرى
      { name: 'تتبع الوقت', url: '/time-tracking', category: 'أخرى', description: 'تتبع وقت العمل', icon: 'Clock', order: 110 },
      { name: 'النسب والعمولات', url: '/percentages', category: 'أخرى', description: 'إدارة النسب والعمولات', icon: 'Percent', order: 111 },
      { name: 'الحركات المالية', url: '/movements', category: 'أخرى', description: 'الحركات المالية', icon: 'ArrowUpDown', order: 112 },
      { name: 'بيانات الشركة', url: '/company', category: 'أخرى', description: 'إدارة بيانات الشركة', icon: 'Building2', order: 113 }
    ];

    console.log(`📋 سيتم إضافة ${pages.length} صفحة`);

    let addedCount = 0;

    for (const page of pages) {
      try {
        await client.query(`
          INSERT INTO navigation_pages (
            page_title, page_url, category, page_description, keywords, is_active, created_date
          ) VALUES ($1, $2, $3, $4, $5, true, CURRENT_DATE)
        `, [
          page.name,
          page.url,
          page.category,
          page.description,
          page.icon
        ]);

        addedCount++;
        console.log(`✅ ${page.name} - ${page.url}`);

      } catch (error) {
        console.error(`❌ خطأ في إضافة ${page.name}:`, error.message);
      }
    }

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم إنشاء صفحات التنقل بنجاح!');
    console.log('================================');
    console.log(`📊 إجمالي الصفحات المضافة: ${addedCount}`);
    
    // إحصائيات حسب الفئة
    const categories = {};
    pages.forEach(page => {
      if (!categories[page.category]) categories[page.category] = 0;
      categories[page.category]++;
    });
    
    console.log('\n📈 توزيع الصفحات حسب الفئة:');
    Object.entries(categories).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} صفحة`);
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إنشاء صفحات التنقل:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

createNavigationPages().catch(console.error);
