const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'moham<PERSON>',
  password: 'yemen123',
  port: 5432,
});

async function createAdminUser() {
  const client = await pool.connect();
  
  try {
    console.log('👤 إنشاء مستخدم إداري جديد...\n');

    // إنشاء دور المدير إذا لم يكن موجوداً
    const roleResult = await client.query(`
      INSERT INTO user_roles (role_name, display_name, permissions, is_active, created_date)
      VALUES ('admin', 'مدير النظام', ARRAY['all'], true, NOW())
      ON CONFLICT (role_name) DO NOTHING
      RETURNING id
    `);

    let adminRoleId;
    if (roleResult.rows.length > 0) {
      adminRoleId = roleResult.rows[0].id;
      console.log('✅ تم إنشاء دور المدير');
    } else {
      // الحصول على معرف الدور الموجود
      const existingRole = await client.query(`
        SELECT id FROM user_roles WHERE role_name = 'admin'
      `);
      adminRoleId = existingRole.rows[0].id;
      console.log('✅ دور المدير موجود بالفعل');
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // إنشاء المستخدم الإداري
    const userResult = await client.query(`
      INSERT INTO users (
        username,
        email,
        password_hash,
        role,
        permissions,
        user_type,
        status,
        is_active,
        created_date
      ) VALUES (
        'admin',
        '<EMAIL>',
        $1,
        'admin',
        ARRAY['all'],
        'admin',
        'active',
        true,
        CURRENT_DATE
      ) RETURNING id, username, email, role
    `, [hashedPassword]);

    const newUser = userResult.rows[0];

    console.log('🎉 تم إنشاء المستخدم الإداري بنجاح!');
    console.log('================================');
    console.log(`👤 اسم المستخدم: ${newUser.username}`);
    console.log(`📧 البريد الإلكتروني: ${newUser.email}`);
    console.log(`🔑 كلمة المرور: admin123`);
    console.log(`👨‍💼 الدور: ${newUser.role}`);
    console.log(`🆔 معرف المستخدم: ${newUser.id}`);
    console.log('================================');
    console.log('⚠️  تذكر تغيير كلمة المرور بعد تسجيل الدخول الأول!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الإداري:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

createAdminUser().catch(console.error);
