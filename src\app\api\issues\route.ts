import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب القضايا (مع إمكانية تصفية غير الموزعة)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const undistributed = searchParams.get('undistributed') === 'true'

    let whereClause = ''
    if (undistributed) {
      // جلب القضايا التي لا تحتوي على توزيع
      whereClause = `
        WHERE i.id NOT IN (
          SELECT DISTINCT issue_id
          FROM case_distribution
          WHERE issue_id IS NOT NULL
        )
      `
    }

    const result = await query(`
      SELECT
        i.*,
        COALESCE(c.name, i.client_name) as client_name,
        COALESCE(ct.name, i.court_name) as court_name,
        COALESCE(it.name, i.issue_type) as issue_type
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN courts ct ON i.court_id = ct.id
      LEFT JOIN issue_types it ON i.issue_type_id = it.id
      ${whereClause}
      ORDER BY i.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching issues:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات القضايا' },
      { status: 500 }
    )
  }
}

// POST - إضافة قضية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      case_number, title, description, client_id, client_name, court_name,
      issue_type, status = 'pending', amount, notes,
      contract_method = 'بالجلسة', contract_date, court_id, issue_type_id
    } = body

    console.log('Received data for creating issue:', {
      case_number, title, description, client_id, client_name, court_name,
      issue_type, status, amount, notes, contract_method, contract_date, court_id, issue_type_id
    })

    // التحقق من البيانات المطلوبة
    if (!case_number || !title || !client_name) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية والعنوان واسم الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم القضية
    const existingIssue = await query(
      'SELECT id FROM issues WHERE case_number = $1',
      [case_number]
    )

    if (existingIssue.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم القضية موجود مسبقاً' },
        { status: 400 }
      )
    }

    // إدراج القضية الجديدة
    const result = await query(`
      INSERT INTO issues (
        case_number, title, description, client_id, client_name, court_id, court_name,
        issue_type_id, issue_type, status, amount, notes, contract_method, contract_date
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *
    `, [
      case_number, title, description, client_id || null, client_name, court_id || null, court_name,
      issue_type_id || null, issue_type, status, parseFloat(amount) || 0, notes, contract_method, contract_date
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة القضية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث قضية
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, case_number, title, description, client_id, client_name, court_id, court_name,
      issue_type_id, issue_type, status, amount, next_hearing, notes, contract_method, contract_date
    } = body

    if (!id || !case_number || !title || !client_name) {
      return NextResponse.json(
        { success: false, error: 'المعرف ورقم القضية والعنوان واسم الموكل مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود القضية
    const existingIssue = await query(
      'SELECT id FROM issues WHERE id = $1',
      [id]
    )

    if (existingIssue.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    // تحديث بيانات القضية
    const result = await query(`
      UPDATE issues
      SET case_number = $1, title = $2, description = $3, client_id = $4, client_name = $5,
          court_id = $6, court_name = $7, issue_type_id = $8, issue_type = $9, status = $10,
          amount = $11, next_hearing = $12, notes = $13, contract_method = $14,
          contract_date = $15, updated_at = CURRENT_TIMESTAMP
      WHERE id = $16
      RETURNING *
    `, [
      case_number, title, description, client_id || null, client_name, court_id || null, court_name,
      issue_type_id || null, issue_type, status, parseFloat(amount) || 0, next_hearing, notes,
      contract_method, contract_date, id
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات القضية بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات القضية' },
      { status: 500 }
    )
  }
}

// DELETE - حذف قضية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف القضية مطلوب' },
        { status: 400 }
      )
    }

    // حذف القضية
    const result = await query(
      'DELETE FROM issues WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف القضية بنجاح'
    })
  } catch (error) {
    console.error('Error deleting issue:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف القضية' },
      { status: 500 }
    )
  }
}
