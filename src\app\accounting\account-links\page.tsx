'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Link2, Users, Building, Truck, Search, Edit, Trash2, Plus } from 'lucide-react'

interface AccountLink {
  id: number
  main_account_id: number
  linked_table: string
  linked_record_id: number
  sub_account_code: string
  sub_account_name: string
  parent_account_code: string
  parent_account_name: string
  parent_balance: number
  linked_record_name: string
  linked_record_balance: number
  is_active: boolean
}

interface Account {
  id: number
  account_code: string
  account_name: string
  account_type: string
  allow_transactions: boolean
}

export default function AccountLinksPage() {
  const [links, setLinks] = useState<AccountLink[]>([])
  const [accounts, setAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTable, setSelectedTable] = useState<string>('all')
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingLink, setEditingLink] = useState<AccountLink | null>(null)
  const [newAccountId, setNewAccountId] = useState('')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // جلب روابط الحسابات
      const linksResponse = await fetch('/api/accounting/account-links')
      if (linksResponse.ok) {
        const linksData = await linksResponse.json()
        setLinks(linksData.links || [])
      }

      // جلب الحسابات المتاحة للربط
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?only_transactional=false')
      if (accountsResponse.ok) {
        const accountsData = await accountsResponse.json()
        setAccounts(accountsData.accounts || [])
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEditLink = (link: AccountLink) => {
    setEditingLink(link)
    setNewAccountId(link.main_account_id.toString())
    setShowEditDialog(true)
  }

  const handleUpdateLink = async () => {
    if (!editingLink || !newAccountId) return

    try {
      const response = await fetch('/api/accounting/account-links', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: editingLink.id,
          main_account_id: parseInt(newAccountId),
          is_active: true
        }),
      })

      if (response.ok) {
        await fetchData()
        setShowEditDialog(false)
        setEditingLink(null)
        setNewAccountId('')
      } else {
        const errorData = await response.json()
        alert(`خطأ: ${errorData.error}`)
      }
    } catch (error) {
      console.error('خطأ في تحديث الربط:', error)
      alert('حدث خطأ في تحديث الربط')
    }
  }

  const getTableIcon = (table: string) => {
    switch (table) {
      case 'clients': return <Users className="h-4 w-4 text-blue-600" />
      case 'employees': return <Building className="h-4 w-4 text-green-600" />
      case 'suppliers': return <Truck className="h-4 w-4 text-orange-600" />
      default: return <Link2 className="h-4 w-4" />
    }
  }

  const getTableName = (table: string) => {
    switch (table) {
      case 'clients': return 'العملاء'
      case 'employees': return 'الموظفين'
      case 'suppliers': return 'الموردين'
      default: return table
    }
  }

  const filteredLinks = links.filter(link => {
    const matchesSearch = 
      link.linked_record_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.parent_account_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      link.sub_account_code?.includes(searchTerm)
    
    const matchesTable = selectedTable === 'all' || link.linked_table === selectedTable

    return matchesSearch && matchesTable
  })

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأدوات */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Link2 className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">ربط الحسابات</h1>
              <p className="text-gray-600">إدارة ربط العملاء والموظفين والموردين بدليل الحسابات</p>
            </div>
          </div>
        </div>

        {/* البحث والفلترة */}
        <Card>
          <CardHeader>
            <CardTitle>البحث والفلترة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-4 space-x-reverse">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الروابط..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <Select value={selectedTable} onValueChange={setSelectedTable}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="تصفية حسب النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="clients">العملاء</SelectItem>
                  <SelectItem value="employees">الموظفين</SelectItem>
                  <SelectItem value="suppliers">الموردين</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* جدول الروابط */}
        <Card>
          <CardHeader>
            <CardTitle>روابط الحسابات</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">جاري تحميل الروابط...</p>
              </div>
            ) : filteredLinks.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <Link2 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>لا توجد روابط مطابقة للبحث</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-right p-3 font-semibold text-gray-700">النوع</th>
                      <th className="text-right p-3 font-semibold text-gray-700">الاسم</th>
                      <th className="text-right p-3 font-semibold text-gray-700">كود الحساب الفرعي</th>
                      <th className="text-right p-3 font-semibold text-gray-700">الحساب الأب</th>
                      <th className="text-right p-3 font-semibold text-gray-700">رصيد الفرد</th>
                      <th className="text-right p-3 font-semibold text-gray-700">رصيد الحساب الأب</th>
                      <th className="text-center p-3 font-semibold text-gray-700">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredLinks.map((link) => (
                      <tr key={link.id} className="border-b hover:bg-gray-50">
                        <td className="p-3">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            {getTableIcon(link.linked_table)}
                            <Badge variant="outline">
                              {getTableName(link.linked_table)}
                            </Badge>
                          </div>
                        </td>
                        <td className="p-3 font-medium">{link.linked_record_name}</td>
                        <td className="p-3 font-mono text-sm">{link.sub_account_code}</td>
                        <td className="p-3">
                          <div className="flex flex-col">
                            <span className="font-medium">{link.parent_account_code}</span>
                            <span className="text-sm text-gray-600">{link.parent_account_name}</span>
                          </div>
                        </td>
                        <td className="p-3">
                          <span className={`font-medium ${
                            link.linked_record_balance > 0 ? 'text-green-600' : 
                            link.linked_record_balance < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {link.linked_record_balance?.toLocaleString() || '0'} ر.ي
                          </span>
                        </td>
                        <td className="p-3">
                          <span className={`font-medium ${
                            link.parent_balance > 0 ? 'text-green-600' : 
                            link.parent_balance < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {link.parent_balance?.toLocaleString() || '0'} ر.ي
                          </span>
                        </td>
                        <td className="p-3 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditLink(link)}
                            title="تعديل الربط"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج تعديل الربط */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل ربط الحساب</DialogTitle>
              <DialogDescription>
                تغيير الحساب الأب المرتبط بـ {editingLink?.linked_record_name}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div>
                <Label htmlFor="new_account">الحساب الأب الجديد</Label>
                <Select value={newAccountId} onValueChange={setNewAccountId}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحساب الجديد" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {accounts.map((account) => (
                      <SelectItem key={account.id} value={account.id.toString()}>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="font-mono text-sm">{account.account_code}</span>
                          <span>{account.account_name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {account.account_type}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {editingLink && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>الربط الحالي:</strong> {editingLink.parent_account_code} - {editingLink.parent_account_name}
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                إلغاء
              </Button>
              <Button type="button" onClick={handleUpdateLink}>
                تحديث الربط
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}
