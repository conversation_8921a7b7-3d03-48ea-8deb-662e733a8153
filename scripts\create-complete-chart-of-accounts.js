const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function createCompleteChartOfAccounts() {
  const client = await pool.connect();
  
  try {
    console.log('📊 إنشاء دليل الحسابات الافتراضي الكامل...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // حذف الحسابات الموجودة (اختياري)
    console.log('🧹 حذف الحسابات الموجودة...');
    await client.query('DELETE FROM chart_of_accounts');
    console.log('✅ تم حذف الحسابات الموجودة');

    // دليل الحسابات الكامل
    const accounts = [
      // 1. الأصول (Assets) - 1000-1999
      { code: '1000', name: 'الأصول', type: 'أصول', nature: 'مدين', level: 1, parent: null },
      
      // 1.1 الأصول المتداولة - 1100-1199
      { code: '1100', name: 'الأصول المتداولة', type: 'أصول', nature: 'مدين', level: 2, parent: '1000' },
      { code: '1110', name: 'النقدية والبنوك', type: 'أصول', nature: 'مدين', level: 3, parent: '1100' },
      { code: '1111', name: 'الصندوق الرئيسي', type: 'أصول', nature: 'مدين', level: 4, parent: '1110' },
      { code: '1112', name: 'صندوق فرعي', type: 'أصول', nature: 'مدين', level: 4, parent: '1110' },
      { code: '1113', name: 'البنك الأهلي اليمني', type: 'أصول', nature: 'مدين', level: 4, parent: '1110' },
      { code: '1114', name: 'بنك سبأ', type: 'أصول', nature: 'مدين', level: 4, parent: '1110' },
      { code: '1115', name: 'البنك التجاري اليمني', type: 'أصول', nature: 'مدين', level: 4, parent: '1110' },
      
      { code: '1120', name: 'العملاء والمدينون', type: 'أصول', nature: 'مدين', level: 3, parent: '1100' },
      { code: '1121', name: 'حسابات العملاء', type: 'أصول', nature: 'مدين', level: 4, parent: '1120' },
      { code: '1122', name: 'أوراق القبض', type: 'أصول', nature: 'مدين', level: 4, parent: '1120' },
      { code: '1123', name: 'مدينون متنوعون', type: 'أصول', nature: 'مدين', level: 4, parent: '1120' },
      
      { code: '1130', name: 'المخزون', type: 'أصول', nature: 'مدين', level: 3, parent: '1100' },
      { code: '1131', name: 'مخزون القرطاسية', type: 'أصول', nature: 'مدين', level: 4, parent: '1130' },
      { code: '1132', name: 'مخزون المواد المكتبية', type: 'أصول', nature: 'مدين', level: 4, parent: '1130' },
      
      { code: '1140', name: 'المصروفات المدفوعة مقدماً', type: 'أصول', nature: 'مدين', level: 3, parent: '1100' },
      { code: '1141', name: 'إيجار مدفوع مقدماً', type: 'أصول', nature: 'مدين', level: 4, parent: '1140' },
      { code: '1142', name: 'تأمين مدفوع مقدماً', type: 'أصول', nature: 'مدين', level: 4, parent: '1140' },
      
      // 1.2 الأصول الثابتة - 1200-1299
      { code: '1200', name: 'الأصول الثابتة', type: 'أصول', nature: 'مدين', level: 2, parent: '1000' },
      { code: '1210', name: 'الأثاث والمعدات', type: 'أصول', nature: 'مدين', level: 3, parent: '1200' },
      { code: '1211', name: 'أثاث المكتب', type: 'أصول', nature: 'مدين', level: 4, parent: '1210' },
      { code: '1212', name: 'أجهزة الكمبيوتر', type: 'أصول', nature: 'مدين', level: 4, parent: '1210' },
      { code: '1213', name: 'معدات مكتبية', type: 'أصول', nature: 'مدين', level: 4, parent: '1210' },
      
      { code: '1220', name: 'مجمع الإهلاك', type: 'أصول', nature: 'دائن', level: 3, parent: '1200' },
      { code: '1221', name: 'مجمع إهلاك الأثاث', type: 'أصول', nature: 'دائن', level: 4, parent: '1220' },
      { code: '1222', name: 'مجمع إهلاك أجهزة الكمبيوتر', type: 'أصول', nature: 'دائن', level: 4, parent: '1220' },
      
      // 2. الخصوم (Liabilities) - 2000-2999
      { code: '2000', name: 'الخصوم', type: 'خصوم', nature: 'دائن', level: 1, parent: null },
      
      // 2.1 الخصوم المتداولة - 2100-2199
      { code: '2100', name: 'الخصوم المتداولة', type: 'خصوم', nature: 'دائن', level: 2, parent: '2000' },
      { code: '2110', name: 'الموردون والدائنون', type: 'خصوم', nature: 'دائن', level: 3, parent: '2100' },
      { code: '2111', name: 'حسابات الموردين', type: 'خصوم', nature: 'دائن', level: 4, parent: '2110' },
      { code: '2112', name: 'أوراق الدفع', type: 'خصوم', nature: 'دائن', level: 4, parent: '2110' },
      { code: '2113', name: 'دائنون متنوعون', type: 'خصوم', nature: 'دائن', level: 4, parent: '2110' },
      
      { code: '2120', name: 'المرتبات والأجور المستحقة', type: 'خصوم', nature: 'دائن', level: 3, parent: '2100' },
      { code: '2121', name: 'مرتبات الموظفين المستحقة', type: 'خصوم', nature: 'دائن', level: 4, parent: '2120' },
      { code: '2122', name: 'بدلات مستحقة', type: 'خصوم', nature: 'دائن', level: 4, parent: '2120' },
      { code: '2123', name: 'حوافز مستحقة', type: 'خصوم', nature: 'دائن', level: 4, parent: '2120' },
      
      { code: '2130', name: 'الضرائب والرسوم المستحقة', type: 'خصوم', nature: 'دائن', level: 3, parent: '2100' },
      { code: '2131', name: 'ضريبة الدخل المستحقة', type: 'خصوم', nature: 'دائن', level: 4, parent: '2130' },
      { code: '2132', name: 'رسوم حكومية مستحقة', type: 'خصوم', nature: 'دائن', level: 4, parent: '2130' },
      
      { code: '2140', name: 'العهد والأمانات', type: 'خصوم', nature: 'دائن', level: 3, parent: '2100' },
      { code: '2141', name: 'عهد الموظفين', type: 'خصوم', nature: 'دائن', level: 4, parent: '2140' },
      { code: '2142', name: 'أمانات العملاء', type: 'خصوم', nature: 'دائن', level: 4, parent: '2140' },
      
      // 3. حقوق الملكية (Equity) - 3000-3999
      { code: '3000', name: 'حقوق الملكية', type: 'حقوق ملكية', nature: 'دائن', level: 1, parent: null },
      { code: '3100', name: 'رأس المال', type: 'حقوق ملكية', nature: 'دائن', level: 2, parent: '3000' },
      { code: '3110', name: 'رأس المال المدفوع', type: 'حقوق ملكية', nature: 'دائن', level: 3, parent: '3100' },
      { code: '3200', name: 'الأرباح المحتجزة', type: 'حقوق ملكية', nature: 'دائن', level: 2, parent: '3000' },
      { code: '3210', name: 'أرباح العام الحالي', type: 'حقوق ملكية', nature: 'دائن', level: 3, parent: '3200' },
      { code: '3220', name: 'أرباح الأعوام السابقة', type: 'حقوق ملكية', nature: 'دائن', level: 3, parent: '3200' },
      
      // 4. الإيرادات (Revenue) - 4000-4999
      { code: '4000', name: 'الإيرادات', type: 'إيرادات', nature: 'دائن', level: 1, parent: null },
      { code: '4100', name: 'إيرادات الخدمات القانونية', type: 'إيرادات', nature: 'دائن', level: 2, parent: '4000' },
      { code: '4110', name: 'أتعاب الاستشارات القانونية', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4100' },
      { code: '4120', name: 'أتعاب التمثيل القانوني', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4100' },
      { code: '4130', name: 'أتعاب صياغة العقود', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4100' },
      { code: '4140', name: 'أتعاب التوثيق', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4100' },
      
      { code: '4200', name: 'إيرادات أخرى', type: 'إيرادات', nature: 'دائن', level: 2, parent: '4000' },
      { code: '4210', name: 'إيرادات فوائد', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4200' },
      { code: '4220', name: 'إيرادات استثمارات', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4200' },
      { code: '4230', name: 'فوارق عملة موجبة', type: 'إيرادات', nature: 'دائن', level: 3, parent: '4200' },
      
      // 5. المصروفات (Expenses) - 5000-5999
      { code: '5000', name: 'المصروفات', type: 'مصروفات', nature: 'مدين', level: 1, parent: null },
      
      // 5.1 مصروفات الموظفين - 5100-5199
      { code: '5100', name: 'مصروفات الموظفين', type: 'مصروفات', nature: 'مدين', level: 2, parent: '5000' },
      { code: '5110', name: 'المرتبات والأجور', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5100' },
      { code: '5111', name: 'مرتبات الموظفين', type: 'مصروفات', nature: 'مدين', level: 4, parent: '5110' },
      { code: '5112', name: 'بدلات الموظفين', type: 'مصروفات', nature: 'مدين', level: 4, parent: '5110' },
      { code: '5113', name: 'حوافز الموظفين', type: 'مصروفات', nature: 'مدين', level: 4, parent: '5110' },
      { code: '5114', name: 'مكافآت نهاية الخدمة', type: 'مصروفات', nature: 'مدين', level: 4, parent: '5110' },
      
      { code: '5120', name: 'التأمينات الاجتماعية', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5100' },
      { code: '5121', name: 'تأمينات اجتماعية', type: 'مصروفات', nature: 'مدين', level: 4, parent: '5120' },
      { code: '5122', name: 'تأمين صحي', type: 'مصروفات', nature: 'مدين', level: 4, parent: '5120' },
      
      // 5.2 المصروفات الإدارية - 5200-5299
      { code: '5200', name: 'المصروفات الإدارية', type: 'مصروفات', nature: 'مدين', level: 2, parent: '5000' },
      { code: '5210', name: 'إيجار المكتب', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      { code: '5220', name: 'الكهرباء والماء', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      { code: '5230', name: 'الهاتف والإنترنت', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      { code: '5240', name: 'القرطاسية والمطبوعات', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      { code: '5250', name: 'صيانة وإصلاح', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      { code: '5260', name: 'مصروفات السفر', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      { code: '5270', name: 'مصروفات ضيافة', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5200' },
      
      // 5.3 مصروفات أخرى - 5300-5399
      { code: '5300', name: 'مصروفات أخرى', type: 'مصروفات', nature: 'مدين', level: 2, parent: '5000' },
      { code: '5310', name: 'الإهلاك', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5300' },
      { code: '5320', name: 'مصروفات بنكية', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5300' },
      { code: '5330', name: 'فوارق عملة سالبة', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5300' },
      { code: '5340', name: 'مصروفات متنوعة', type: 'مصروفات', nature: 'مدين', level: 3, parent: '5300' }
    ];

    console.log(`📋 سيتم إنشاء ${accounts.length} حساب`);

    let addedCount = 0;
    let parentMap = new Map();

    // إدراج الحسابات بالترتيب الهرمي
    for (const account of accounts) {
      try {
        let parentId = null;
        
        if (account.parent) {
          parentId = parentMap.get(account.parent);
          if (!parentId) {
            console.error(`❌ لم يتم العثور على الحساب الأب: ${account.parent} للحساب: ${account.code}`);
            continue;
          }
        }

        const result = await client.query(`
          INSERT INTO chart_of_accounts (
            account_code, account_name, account_type, account_nature, 
            account_level, parent_id, allow_transactions, is_active, created_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, true, CURRENT_DATE)
          RETURNING id
        `, [
          account.code, 
          account.name, 
          account.type, 
          account.nature, 
          account.level, 
          parentId, 
          account.level === 4 // السماح بالمعاملات للمستوى الرابع فقط
        ]);

        parentMap.set(account.code, result.rows[0].id);
        addedCount++;
        
        console.log(`✅ ${account.code} - ${account.name} (المستوى ${account.level})`);

      } catch (error) {
        console.error(`❌ خطأ في إضافة الحساب ${account.code}:`, error.message);
      }
    }

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم إنشاء دليل الحسابات بنجاح!');
    console.log('================================');
    console.log(`📊 إجمالي الحسابات المضافة: ${addedCount}`);
    console.log(`📈 توزيع الحسابات:`);
    
    const summary = {};
    accounts.forEach(acc => {
      if (!summary[acc.type]) summary[acc.type] = 0;
      summary[acc.type]++;
    });
    
    Object.entries(summary).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} حساب`);
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إنشاء دليل الحسابات:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

createCompleteChartOfAccounts().catch(console.error);
