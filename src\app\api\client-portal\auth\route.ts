import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// POST - تسجيل دخول العميل
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, password, action } = body

    if (action === 'login') {
      return await handleLogin(username, password)
    } else if (action === 'register') {
      const { email, clientId } = body
      return await handleRegister(username, email, password, clientId)
    } else {
      return NextResponse.json(
        { success: false, error: 'إجراء غير صحيح' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('خطأ في المصادقة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
}

async function handleLogin(username: string, password: string) {
  if (!username || !password) {
    return NextResponse.json(
      { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
      { status: 400 }
    )
  }

  try {
    // البحث عن العميل
    const clientQuery = `
      SELECT 
        cpa.*,
        c.name as client_name,
        c.phone,
        c.email as client_email
      FROM client_portal_accounts cpa
      JOIN clients c ON cpa.client_id = c.id
      WHERE cpa.username = $1 AND cpa.is_active = true
    `
    
    const result = await query(clientQuery, [username])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    const client = result.rows[0]

    // التحقق من القفل
    if (client.locked_until && new Date() < new Date(client.locked_until)) {
      return NextResponse.json(
        { success: false, error: 'الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً' },
        { status: 423 }
      )
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, client.password_hash)

    if (!isValidPassword) {
      // زيادة عدد محاولات الدخول الفاشلة
      const attempts = client.login_attempts + 1
      const lockUntil = attempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : null // قفل لمدة 30 دقيقة

      await query(`
        UPDATE client_portal_accounts 
        SET login_attempts = $1, locked_until = $2
        WHERE id = $3
      `, [attempts, lockUntil, client.id])

      return NextResponse.json(
        { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

    // إنشاء JWT token
    const token = jwt.sign(
      { 
        clientId: client.client_id,
        portalAccountId: client.id,
        username: client.username,
        type: 'client'
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    // إنشاء session token
    const sessionToken = jwt.sign(
      { clientId: client.client_id, timestamp: Date.now() },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    // حفظ الجلسة
    await query(`
      INSERT INTO client_sessions (client_id, session_token, ip_address, user_agent, expires_at)
      VALUES ($1, $2, $3, $4, $5)
    `, [
      client.client_id,
      sessionToken,
      request.headers.get('x-forwarded-for') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 ساعة
    ])

    // تحديث آخر دخول وإعادة تعيين محاولات الدخول
    await query(`
      UPDATE client_portal_accounts 
      SET last_login = CURRENT_TIMESTAMP, login_attempts = 0, locked_until = NULL
      WHERE id = $1
    `, [client.id])

    // إرجاع البيانات (بدون كلمة المرور)
    const { password_hash, ...clientData } = client

    return NextResponse.json({
      success: true,
      data: {
        client: clientData,
        token,
        sessionToken
      },
      message: 'تم تسجيل الدخول بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في تسجيل الدخول' },
      { status: 500 }
    )
  }
}

async function handleRegister(username: string, email: string, password: string, clientId: number) {
  if (!username || !email || !password || !clientId) {
    return NextResponse.json(
      { success: false, error: 'جميع البيانات مطلوبة' },
      { status: 400 }
    )
  }

  try {
    // التحقق من وجود العميل
    const clientCheck = await query('SELECT id, name FROM clients WHERE id = $1', [clientId])
    if (clientCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العميل غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود حساب مسبق
    const existingAccount = await query(`
      SELECT id FROM client_portal_accounts 
      WHERE client_id = $1 OR username = $2 OR email = $3
    `, [clientId, username, email])

    if (existingAccount.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'يوجد حساب مسجل مسبقاً لهذا العميل أو اسم المستخدم أو البريد الإلكتروني' },
        { status: 409 }
      )
    }

    // تشفير كلمة المرور
    const passwordHash = await bcrypt.hash(password, 12)

    // إنشاء الحساب
    const insertQuery = `
      INSERT INTO client_portal_accounts (client_id, username, email, password_hash)
      VALUES ($1, $2, $3, $4)
      RETURNING id, client_id, username, email, is_active, created_date
    `

    const result = await query(insertQuery, [clientId, username, email, passwordHash])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إنشاء الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء الحساب:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في إنشاء الحساب' },
      { status: 500 }
    )
  }
}

// GET - التحقق من صحة الجلسة
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any
      
      if (decoded.type !== 'client') {
        return NextResponse.json(
          { success: false, error: 'رمز مصادقة غير صحيح' },
          { status: 401 }
        )
      }

      // جلب بيانات العميل
      const clientQuery = `
        SELECT 
          cpa.id, cpa.client_id, cpa.username, cpa.email, cpa.is_active,
          cpa.language, cpa.timezone, cpa.last_login,
          c.name as client_name, c.phone, c.email as client_email
        FROM client_portal_accounts cpa
        JOIN clients c ON cpa.client_id = c.id
        WHERE cpa.id = $1 AND cpa.is_active = true
      `
      
      const result = await query(clientQuery, [decoded.portalAccountId])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'الحساب غير موجود أو غير نشط' },
          { status: 401 }
        )
      }

      return NextResponse.json({
        success: true,
        data: result.rows[0]
      })

    } catch (jwtError) {
      return NextResponse.json(
        { success: false, error: 'رمز مصادقة غير صحيح' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('خطأ في التحقق من الجلسة:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - تسجيل الخروج
export async function DELETE(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any
      
      // إلغاء تفعيل جميع الجلسات للعميل
      await query(`
        UPDATE client_sessions 
        SET is_active = false 
        WHERE client_id = $1
      `, [decoded.clientId])

      return NextResponse.json({
        success: true,
        message: 'تم تسجيل الخروج بنجاح'
      })

    } catch (jwtError) {
      return NextResponse.json({
        success: true,
        message: 'تم تسجيل الخروج'
      })
    }

  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
}