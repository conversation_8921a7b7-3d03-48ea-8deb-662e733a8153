-- إضا<PERSON>ة عمود role إذا لم يكن موجوداً
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(50) DEFAULT 'employee';

-- إضافة عمود password_hash إذا لم يكن موجوداً
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- تحديث المستخدمين الموجودين بدور افتراضي
UPDATE users SET role = 'admin' WHERE username = 'admin';
UPDATE users SET role = 'employee' WHERE role IS NULL;

-- إنشاء مستخدم admin إذا لم يكن موجوداً
INSERT INTO users (username, email, role, password_hash, employee_id, created_at, updated_at, is_active, last_login, online_status, device_id)
VALUES (
    'admin',
    '<EMAIL>',
    'admin',
    '$2b$10$rQZ9QmjKjKjKjKjKjKjKjOeKjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK', -- admin123 hashed
    NULL,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    true,
    NULL,
    false,
    NULL
)
ON CONFLICT (username) DO UPDATE SET 
    role = 'admin',
    password_hash = '$2b$10$rQZ9QmjKjKjKjKjKjKjKjOeKjKjKjKjKjKjKjKjKjKjKjKjKjKjKjK';
