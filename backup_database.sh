#!/bin/bash

# سكريپت النسخ الاحتياطي لقاعدة البيانات
# Database Backup Script

echo "🗄️  بدء النسخ الاحتياطي لقاعدة البيانات mohammi"
echo "=================================================="

# متغيرات قاعدة البيانات
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="mohammi"
DB_USER="postgres"
DB_PASSWORD="yemen123"

# إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
BACKUP_DIR="database_backups"
mkdir -p "$BACKUP_DIR"

# تاريخ ووقت النسخة الاحتياطية
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/mohammi_backup_$TIMESTAMP.sql"

echo "📅 التاريخ والوقت: $(date)"
echo "📁 مجلد النسخ الاحتياطية: $BACKUP_DIR"
echo "📄 اسم الملف: $BACKUP_FILE"
echo ""

# تصدير متغير كلمة المرور
export PGPASSWORD="$DB_PASSWORD"

echo "🔄 جاري إنشاء النسخة الاحتياطية..."

# إنشاء النسخة الاحتياطية
pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        --verbose \
        --clean \
        --no-owner \
        --no-privileges \
        --format=plain \
        --file="$BACKUP_FILE"

# التحقق من نجاح العملية
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ تم إنشاء النسخة الاحتياطية بنجاح!"
    
    # عرض معلومات الملف
    FILE_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    echo "📊 حجم الملف: $FILE_SIZE"
    echo "📍 المسار الكامل: $(pwd)/$BACKUP_FILE"
    
    # ضغط الملف (اختياري)
    echo ""
    echo "🗜️  جاري ضغط النسخة الاحتياطية..."
    gzip "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        COMPRESSED_FILE="$BACKUP_FILE.gz"
        COMPRESSED_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
        echo "✅ تم ضغط الملف بنجاح!"
        echo "📊 حجم الملف المضغوط: $COMPRESSED_SIZE"
        echo "📍 الملف المضغوط: $(pwd)/$COMPRESSED_FILE"
    else
        echo "⚠️  فشل في ضغط الملف"
    fi
    
    echo ""
    echo "📋 ملخص النسخة الاحتياطية:"
    echo "  • قاعدة البيانات: $DB_NAME"
    echo "  • الخادم: $DB_HOST:$DB_PORT"
    echo "  • التاريخ: $(date)"
    echo "  • الملف: $BACKUP_FILE"
    
else
    echo ""
    echo "❌ فشل في إنشاء النسخة الاحتياطية!"
    echo "تحقق من:"
    echo "  • اتصال قاعدة البيانات"
    echo "  • صحة بيانات الاعتماد"
    echo "  • صلاحيات الكتابة في المجلد"
    exit 1
fi

# تنظيف متغير كلمة المرور
unset PGPASSWORD

echo ""
echo "🎯 لاستعادة النسخة الاحتياطية، استخدم:"
echo "   psql -h $DB_HOST -U $DB_USER -d $DB_NAME < $BACKUP_FILE"
echo ""
echo "✅ انتهت عملية النسخ الاحتياطي"