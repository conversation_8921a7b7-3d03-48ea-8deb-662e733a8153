/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reports/route";
exports.ids = ["app/api/reports/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_mohaminew_src_app_api_reports_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/reports/route.ts */ \"(rsc)/./src/app/api/reports/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_reports_route_ts__WEBPACK_IMPORTED_MODULE_16__]);\nD_mohaminew_src_app_api_reports_route_ts__WEBPACK_IMPORTED_MODULE_16__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reports/route\",\n        pathname: \"/api/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/reports/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\reports\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_reports_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/reports/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/reports/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/reports/route.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_database__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// GET - جلب بيانات التقارير\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get('period') || 'last_6_months';\n        const reportType = searchParams.get('report_type') || 'overview';\n        const startDate = searchParams.get('start_date');\n        const endDate = searchParams.get('end_date');\n        // تحديد نطاق التواريخ بناءً على الفترة\n        let dateFilter = '';\n        let dateParams = [];\n        let paramIndex = 1;\n        if (startDate && endDate) {\n            dateFilter = `AND created_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;\n            dateParams = [\n                startDate,\n                endDate\n            ];\n            paramIndex += 2;\n        } else {\n            switch(period){\n                case 'last_month':\n                    dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '1 month'`;\n                    break;\n                case 'last_3_months':\n                    dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '3 months'`;\n                    break;\n                case 'last_6_months':\n                    dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '6 months'`;\n                    break;\n                case 'last_year':\n                    dateFilter = `AND created_date >= CURRENT_DATE - INTERVAL '1 year'`;\n                    break;\n            }\n        }\n        // الملخص العام\n        const summaryQuery = `\n      SELECT \n        (SELECT COUNT(*) FROM issues WHERE 1=1 ${dateFilter}) as total_cases,\n        (SELECT COUNT(*) FROM issues WHERE status = 'active' ${dateFilter}) as active_cases,\n        (SELECT COUNT(*) FROM issues WHERE status = 'completed' ${dateFilter}) as completed_cases,\n        (SELECT COUNT(DISTINCT client_id) FROM issues WHERE 1=1 ${dateFilter}) as total_clients,\n        (SELECT COALESCE(SUM(total_amount), 0) FROM invoices WHERE payment_status = 'paid' ${dateFilter}) as total_revenue,\n        (SELECT COUNT(*) FROM invoices WHERE payment_status = 'unpaid' ${dateFilter}) as pending_invoices,\n        (SELECT COALESCE(SUM(duration_minutes), 0) / 60.0 FROM time_entries WHERE 1=1 ${dateFilter}) as total_hours,\n        (SELECT COALESCE(SUM(duration_minutes), 0) / 60.0 FROM time_entries WHERE is_billable = true ${dateFilter}) as billable_hours\n    `;\n        const summaryResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(summaryQuery, dateParams);\n        const summary = summaryResult.rows[0];\n        // الإحصائيات الشهرية\n        const monthlyStatsQuery = `\n      SELECT \n        TO_CHAR(created_date, 'YYYY-MM') as month,\n        COUNT(*) as cases,\n        COALESCE(SUM(amount), 0) as revenue,\n        0 as hours\n      FROM issues \n      WHERE 1=1 ${dateFilter}\n      GROUP BY TO_CHAR(created_date, 'YYYY-MM')\n      ORDER BY month DESC\n      LIMIT 12\n    `;\n        const monthlyStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(monthlyStatsQuery, dateParams);\n        // إضافة بيانات الساعات للإحصائيات الشهرية\n        const monthlyHoursQuery = `\n      SELECT \n        TO_CHAR(start_time, 'YYYY-MM') as month,\n        COALESCE(SUM(duration_minutes), 0) / 60.0 as hours\n      FROM time_entries \n      WHERE 1=1 ${dateFilter.replace('created_date', 'start_time')}\n      GROUP BY TO_CHAR(start_time, 'YYYY-MM')\n    `;\n        const monthlyHoursResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(monthlyHoursQuery, dateParams);\n        const hoursMap = monthlyHoursResult.rows.reduce((acc, row)=>{\n            acc[row.month] = parseFloat(row.hours);\n            return acc;\n        }, {});\n        const monthlyStats = monthlyStatsResult.rows.map((row)=>({\n                month: row.month,\n                cases: parseInt(row.cases),\n                revenue: parseFloat(row.revenue),\n                hours: hoursMap[row.month] || 0\n            }));\n        // أنواع القضايا\n        const caseTypesQuery = `\n      SELECT \n        it.name as type,\n        COUNT(i.id) as count,\n        (COUNT(i.id) * 100.0 / (SELECT COUNT(*) FROM issues WHERE 1=1 ${dateFilter})) as percentage\n      FROM issue_types it\n      LEFT JOIN issues i ON it.id = i.issue_type_id ${dateFilter.replace('created_date', 'i.created_date')}\n      GROUP BY it.id, it.name\n      HAVING COUNT(i.id) > 0\n      ORDER BY count DESC\n    `;\n        const caseTypesResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(caseTypesQuery, dateParams);\n        const caseTypes = caseTypesResult.rows.map((row)=>({\n                type: row.type,\n                count: parseInt(row.count),\n                percentage: parseFloat(row.percentage)\n            }));\n        // إحصائيات العملاء\n        const clientStatsQuery = `\n      SELECT \n        c.name as client_name,\n        COUNT(i.id) as cases_count,\n        COALESCE(SUM(i.amount), 0) as total_amount,\n        MAX(i.updated_at) as last_activity\n      FROM clients c\n      LEFT JOIN issues i ON c.id = i.client_id ${dateFilter.replace('created_date', 'i.created_date')}\n      GROUP BY c.id, c.name\n      HAVING COUNT(i.id) > 0\n      ORDER BY total_amount DESC\n      LIMIT 10\n    `;\n        const clientStatsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(clientStatsQuery, dateParams);\n        const clientStats = clientStatsResult.rows.map((row)=>({\n                client_name: row.client_name,\n                cases_count: parseInt(row.cases_count),\n                total_amount: parseFloat(row.total_amount),\n                last_activity: row.last_activity\n            }));\n        // أداء الموظفين\n        const employeePerformanceQuery = `\n      SELECT \n        e.name as employee_name,\n        COUNT(DISTINCT i.id) as cases_handled,\n        COALESCE(SUM(te.duration_minutes), 0) / 60.0 as hours_worked,\n        COALESCE(SUM(te.billable_amount), 0) as revenue_generated,\n        CASE \n          WHEN COUNT(DISTINCT i.id) > 0 THEN \n            LEAST(100, (COUNT(DISTINCT CASE WHEN i.status = 'completed' THEN i.id END) * 100.0 / COUNT(DISTINCT i.id)))\n          ELSE 0 \n        END as efficiency_score\n      FROM employees e\n      LEFT JOIN issues i ON e.id = i.assigned_to ${dateFilter.replace('created_date', 'i.created_date')}\n      LEFT JOIN time_entries te ON e.id = te.employee_id ${dateFilter.replace('created_date', 'te.start_time')}\n      GROUP BY e.id, e.name\n      HAVING COUNT(DISTINCT i.id) > 0 OR SUM(te.duration_minutes) > 0\n      ORDER BY revenue_generated DESC\n      LIMIT 10\n    `;\n        const employeePerformanceResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(employeePerformanceQuery, dateParams.concat(dateParams));\n        const employeePerformance = employeePerformanceResult.rows.map((row)=>({\n                employee_name: row.employee_name,\n                cases_handled: parseInt(row.cases_handled),\n                hours_worked: parseFloat(row.hours_worked),\n                revenue_generated: parseFloat(row.revenue_generated),\n                efficiency_score: parseFloat(row.efficiency_score)\n            }));\n        // النظرة المالية\n        const monthlyRevenueQuery = `\n      SELECT \n        TO_CHAR(invoice_date, 'YYYY-MM') as month,\n        COALESCE(SUM(total_amount), 0) as revenue,\n        0 as expenses,\n        COALESCE(SUM(total_amount), 0) as profit\n      FROM invoices \n      WHERE payment_status = 'paid' ${dateFilter.replace('created_date', 'invoice_date')}\n      GROUP BY TO_CHAR(invoice_date, 'YYYY-MM')\n      ORDER BY month DESC\n      LIMIT 12\n    `;\n        const monthlyRevenueResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(monthlyRevenueQuery, dateParams);\n        const monthlyRevenue = monthlyRevenueResult.rows.map((row)=>({\n                month: row.month,\n                revenue: parseFloat(row.revenue),\n                expenses: parseFloat(row.expenses),\n                profit: parseFloat(row.profit)\n            }));\n        // حالة المدفوعات\n        const paymentStatusQuery = `\n      SELECT \n        COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END), 0) as paid,\n        COALESCE(SUM(CASE WHEN payment_status = 'unpaid' THEN total_amount ELSE 0 END), 0) as pending,\n        COALESCE(SUM(CASE WHEN payment_status = 'overdue' THEN total_amount ELSE 0 END), 0) as overdue\n      FROM invoices \n      WHERE 1=1 ${dateFilter.replace('created_date', 'invoice_date')}\n    `;\n        const paymentStatusResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(paymentStatusQuery, dateParams);\n        const paymentStatus = {\n            paid: parseFloat(paymentStatusResult.rows[0].paid),\n            pending: parseFloat(paymentStatusResult.rows[0].pending),\n            overdue: parseFloat(paymentStatusResult.rows[0].overdue)\n        };\n        // تجميع البيانات\n        const reportData = {\n            summary: {\n                total_cases: parseInt(summary.total_cases),\n                active_cases: parseInt(summary.active_cases),\n                completed_cases: parseInt(summary.completed_cases),\n                total_clients: parseInt(summary.total_clients),\n                total_revenue: parseFloat(summary.total_revenue),\n                pending_invoices: parseInt(summary.pending_invoices),\n                total_hours: parseFloat(summary.total_hours),\n                billable_hours: parseFloat(summary.billable_hours)\n            },\n            monthly_stats: monthlyStats,\n            case_types: caseTypes,\n            client_stats: clientStats,\n            employee_performance: employeePerformance,\n            financial_overview: {\n                monthly_revenue: monthlyRevenue,\n                payment_status: paymentStatus\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: reportData\n        });\n    } catch (error) {\n        console.error('خطأ في جلب بيانات التقارير:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب بيانات التقارير'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/reports/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeTables: () => (/* binding */ initializeTables),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Database configuration from mohammi.txt\nconst dbConfig = {\n    host: 'localhost',\n    port: 5432,\n    database: 'mohammi',\n    user: 'postgres',\n    password: 'yemen123',\n    ssl: false,\n    connectionTimeoutMillis: 5000,\n    idleTimeoutMillis: 30000,\n    max: 20\n};\n// Create connection pool\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n// Test database connection\nasync function testConnection() {\n    try {\n        const client = await pool.connect();\n        const result = await client.query('SELECT NOW()');\n        client.release();\n        console.log('✅ Database connected successfully:', result.rows[0]);\n        return true;\n    } catch (error) {\n        console.error('❌ Database connection failed:', error);\n        return false;\n    }\n}\n// Generic query function\nasync function query(text, params) {\n    let client;\n    try {\n        client = await pool.connect();\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error('Database query error:', error);\n        console.error('Query:', text);\n        console.error('Params:', params);\n        throw error;\n    } finally{\n        if (client) {\n            client.release();\n        }\n    }\n}\n// Initialize database tables\nasync function initializeTables() {\n    try {\n        // Create clients table\n        await query(`\n      CREATE TABLE IF NOT EXISTS clients (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        address TEXT,\n        id_number VARCHAR(20) UNIQUE,\n        status VARCHAR(20) DEFAULT 'active',\n        cases_count INTEGER DEFAULT 0,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create employees table\n        await query(`\n      CREATE TABLE IF NOT EXISTS employees (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        position VARCHAR(255),\n        department VARCHAR(255),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        address TEXT,\n        id_number VARCHAR(20) UNIQUE,\n        salary DECIMAL(10,2),\n        hire_date DATE,\n        status VARCHAR(20) DEFAULT 'active',\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create issue_types table\n        await query(`\n      CREATE TABLE IF NOT EXISTS issue_types (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        description TEXT,\n        color VARCHAR(50),\n        cases_count INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create issues table\n        await query(`\n      CREATE TABLE IF NOT EXISTS issues (\n        id SERIAL PRIMARY KEY,\n        case_number VARCHAR(50) UNIQUE NOT NULL,\n        title VARCHAR(255) NOT NULL,\n        description TEXT,\n        client_id INTEGER REFERENCES clients(id),\n        client_name VARCHAR(255),\n        court_name VARCHAR(255),\n        issue_type_id INTEGER REFERENCES issue_types(id),\n        issue_type VARCHAR(255),\n        status VARCHAR(50) DEFAULT 'pending',\n        amount DECIMAL(12,2),\n        next_hearing DATE,\n        notes TEXT,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create lineages table (النسب المالية)\n        await query(`\n      CREATE TABLE IF NOT EXISTS lineages (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        management_share DECIMAL(5,2) DEFAULT 0.00,\n        court_share DECIMAL(5,2) DEFAULT 0.00,\n        commission_share DECIMAL(5,2) DEFAULT 0.00,\n        other_share DECIMAL(5,2) DEFAULT 0.00,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create follows table (المتابعات)\n        await query(`\n      CREATE TABLE IF NOT EXISTS follows (\n        id SERIAL PRIMARY KEY,\n        case_id INTEGER REFERENCES issues(id),\n        case_number VARCHAR(50),\n        case_title VARCHAR(255),\n        client_name VARCHAR(255),\n        follow_type VARCHAR(50),\n        description TEXT,\n        due_date DATE,\n        status VARCHAR(50) DEFAULT 'pending',\n        priority VARCHAR(20) DEFAULT 'medium',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create movements table (الحركات المالية)\n        await query(`\n      CREATE TABLE IF NOT EXISTS movements (\n        id SERIAL PRIMARY KEY,\n        case_id INTEGER REFERENCES issues(id),\n        case_number VARCHAR(50),\n        case_title VARCHAR(255),\n        movement_type VARCHAR(20) NOT NULL, -- 'income' or 'expense'\n        category VARCHAR(255),\n        amount DECIMAL(12,2) NOT NULL,\n        description TEXT,\n        date DATE DEFAULT CURRENT_DATE,\n        reference_number VARCHAR(100),\n        status VARCHAR(50) DEFAULT 'pending',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create users table\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        username VARCHAR(100) UNIQUE NOT NULL,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        role VARCHAR(50) DEFAULT 'user',\n        status VARCHAR(20) DEFAULT 'active',\n        last_login TIMESTAMP,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create companies table\n        await query(`\n      CREATE TABLE IF NOT EXISTS companies (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        legal_name VARCHAR(255),\n        registration_number VARCHAR(100),\n        address TEXT,\n        city VARCHAR(100),\n        country VARCHAR(100),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        website VARCHAR(255),\n        tax_number VARCHAR(50),\n        commercial_register VARCHAR(50),\n        logo_url VARCHAR(500),\n        logo_right_text TEXT,\n        logo_left_text TEXT,\n        logo_image_url VARCHAR(500),\n        established_date DATE,\n        legal_form VARCHAR(100),\n        capital DECIMAL(15,2),\n        description TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create journal_entries table\n        await query(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        entry_number VARCHAR(50) UNIQUE NOT NULL,\n        description TEXT NOT NULL,\n        date DATE DEFAULT CURRENT_DATE,\n        total_debit DECIMAL(12,2) DEFAULT 0.00,\n        total_credit DECIMAL(12,2) DEFAULT 0.00,\n        status VARCHAR(50) DEFAULT 'pending',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create governorates table\n        await query(`\n      CREATE TABLE IF NOT EXISTS governorates (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        code VARCHAR(10),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create branches table\n        await query(`\n      CREATE TABLE IF NOT EXISTS branches (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        governorate_id INTEGER REFERENCES governorates(id),\n        address TEXT,\n        phone VARCHAR(20),\n        manager_name VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create courts table\n        await query(`\n      CREATE TABLE IF NOT EXISTS courts (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        type VARCHAR(100),\n        governorate_id INTEGER REFERENCES governorates(id),\n        address TEXT,\n        phone VARCHAR(20),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create announcements table\n        await query(`\n      CREATE TABLE IF NOT EXISTS announcements (\n        id SERIAL PRIMARY KEY,\n        announcement_1 TEXT,\n        announcement_2 TEXT,\n        announcement_3 TEXT,\n        announcement_4 TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create opening_balances table\n        await query(`\n      CREATE TABLE IF NOT EXISTS opening_balances (\n        id SERIAL PRIMARY KEY,\n        account_id INTEGER NOT NULL,\n        debit_balance DECIMAL(15,2) DEFAULT 0,\n        credit_balance DECIMAL(15,2) DEFAULT 0,\n        balance_date DATE NOT NULL,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create navigation_pages table for smart search\n        await query(`\n      CREATE TABLE IF NOT EXISTS navigation_pages (\n        id SERIAL PRIMARY KEY,\n        page_title VARCHAR(255) NOT NULL,\n        page_url VARCHAR(500) NOT NULL,\n        page_description TEXT,\n        category VARCHAR(100),\n        keywords TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Insert default navigation pages\n        await query(`\n      INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords) VALUES\n      ('لوحة التحكم', '/', 'الصفحة الرئيسية للنظام', 'رئيسي', 'لوحة,تحكم,رئيسي,dashboard'),\n      ('العملاء', '/clients', 'إدارة بيانات العملاء', 'إدارة', 'عملاء,clients,زبائن'),\n      ('الموظفين', '/employees', 'إدارة بيانات الموظفين', 'إدارة', 'موظفين,employees,عمال'),\n      ('أنواع القضايا', '/issue-types', 'إدارة أنواع القضايا', 'قضايا', 'أنواع,قضايا,types'),\n      ('القضايا', '/issues', 'إدارة القضايا والدعاوى', 'قضايا', 'قضايا,دعاوى,issues,cases'),\n      ('المتابعات', '/follows', 'متابعة القضايا والمهام', 'قضايا', 'متابعات,follows,مهام'),\n      ('حركة القضايا', '/movements', 'إدارة حركة القضايا', 'مالية', 'حركات,مالية,movements'),\n      ('دليل الحسابات', '/accounting/chart-of-accounts', 'دليل الحسابات المحاسبي', 'محاسبة', 'دليل,حسابات,chart,accounts'),\n      ('سندات الصرف', '/accounting/payment-vouchers', 'إدارة سندات الصرف', 'محاسبة', 'سندات,صرف,payment,vouchers'),\n      ('سندات القبض', '/accounting/receipt-vouchers', 'إدارة سندات القبض', 'محاسبة', 'سندات,قبض,receipt,vouchers'),\n      ('القيود اليومية', '/accounting/journal-entries', 'إدارة القيود اليومية', 'محاسبة', 'قيود,يومية,journal,entries'),\n      ('الأرصدة الافتتاحية', '/accounting/opening-balances', 'إدارة الأرصدة الافتتاحية', 'محاسبة', 'أرصدة,افتتاحية,opening,balances'),\n      ('التقارير المحاسبية', '/accounting/reports', 'التقارير المحاسبية', 'تقارير', 'تقارير,محاسبية,reports'),\n      ('كشف حساب', '/accounting/reports/account-statement', 'كشف حساب تفصيلي', 'تقارير', 'كشف,حساب,statement'),\n      ('تقارير القضايا', '/case-reports', 'تقارير القضايا والدعاوى', 'تقارير', 'تقارير,قضايا,cases'),\n      ('التقارير المالية', '/financial-reports', 'التقارير المالية', 'تقارير', 'تقارير,مالية,financial'),\n      ('تقارير الموظفين', '/employee-reports', 'تقارير الموظفين', 'تقارير', 'تقارير,موظفين,employees'),\n      ('بيانات الشركة', '/company', 'إدارة بيانات الشركة', 'إعدادات', 'شركة,company,بيانات'),\n      ('مراكز التكلفة', '/settings/cost-centers', 'إدارة مراكز التكلفة', 'إعدادات', 'مراكز,تكلفة,cost,centers'),\n      ('الإعلانات', '/settings/announcements', 'إدارة الإعلانات', 'إعدادات', 'إعلانات,announcements'),\n      ('المحافظات', '/governorates', 'إدارة المحافظات', 'إدارة', 'محافظات,governorates'),\n      ('الفروع', '/branches', 'إدارة الفروع', 'إدارة', 'فروع,branches'),\n      ('المحاكم', '/courts', 'إدارة المحاكم', 'إدارة', 'محاكم,courts')\n      ON CONFLICT DO NOTHING\n    `);\n        console.log('✅ All database tables initialized successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to initialize database tables:', error);\n        throw error;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();