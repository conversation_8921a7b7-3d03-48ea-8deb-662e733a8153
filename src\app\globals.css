@import "tailwindcss";
@import "../styles/fonts.css";

/* خطوط عربية من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  direction: rtl;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
  --font-cairo: var(--font-cairo);
  --font-noto-arabic: var(--font-noto-arabic);
  --background: #f8fafc;
  --foreground: #1e293b;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #475569;
  --accent: #e2e8f0;
  --accent-foreground: #334155;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;
  --radius: 0.5rem;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --radius: var(--radius);
  --font-sans: 'Khalid-Art', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

html {
  direction: rtl;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Cairo', 'Noto Sans Arabic', 'Khalid-Art', 'Segoe UI', Arial, sans-serif;
  direction: rtl;
  text-align: right;
}

/* إعدادات خاصة للنصوص العربية */
* {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
  line-height: 1.6;
  letter-spacing: 0.02em;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}
