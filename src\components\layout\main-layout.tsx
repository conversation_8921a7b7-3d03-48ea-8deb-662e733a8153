'use client'

import { useState, useEffect } from 'react'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { AnnouncementBanner } from './announcement-banner'
import { ChatWidget } from '@/components/chat/chat-widget'
import { useAuth } from '@/hooks/useAuth'

interface MainLayoutProps {
  children: React.ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const { user } = useAuth()

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed)
  }

  return (
    <div className="flex h-screen bg-gray-100" dir="rtl">
      <Sidebar isCollapsed={isSidebarCollapsed} onToggle={toggleSidebar} />
      <div className="flex-1 flex flex-col overflow-hidden transition-all duration-300">
        <AnnouncementBanner />
        <Header />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>

      {/* مكون المحادثات */}
      <ChatWidget
        userType={user?.type || 'user'}
        userId={user?.id || 1}
        userName={user?.name || 'مستخدم'}
      />
    </div>
  )
}
