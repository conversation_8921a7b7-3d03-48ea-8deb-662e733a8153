import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'
import { writeFile, mkdir } from 'fs/promises'
import path from 'path'

// POST - رفع وثيقة جديدة
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    
    const file = formData.get('file') as File
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string
    const subcategory = formData.get('subcategory') as string
    const tags = formData.get('tags') as string
    const access_level = formData.get('access_level') as string
    const is_confidential = formData.get('is_confidential') === 'true'

    if (!file || !title) {
      return NextResponse.json(
        { success: false, error: 'الملف والعنوان مطلوبان' },
        { status: 400 }
      )
    }

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'documents')
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // المجلد موجود بالفعل
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now()
    const fileExtension = path.extname(file.name)
    const fileName = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`
    const filePath = path.join(uploadDir, fileName)
    const relativePath = `/uploads/documents/${fileName}`

    // حفظ الملف
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // حفظ معلومات الملف في قاعدة البيانات
    const insertQuery = `
      INSERT INTO documents (
        title, description, file_name, file_path, file_size, file_type, mime_type,
        category, subcategory, tags, access_level, is_confidential, uploaded_by
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
      ) RETURNING *
    `

    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []

    const values = [
      title,
      description || '',
      fileName,
      relativePath,
      file.size,
      fileExtension.substring(1), // إزالة النقطة من الامتداد
      file.type,
      category || 'general',
      subcategory || '',
      tagsArray,
      access_level || 'private',
      is_confidential,
      1 // مؤقت - معرف المستخدم الذي رفع الملف
    ]

    const result = await query(insertQuery, values)

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم رفع الوثيقة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في رفع الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في رفع الوثيقة' },
      { status: 500 }
    )
  }
}