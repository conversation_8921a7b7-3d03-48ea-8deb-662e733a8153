# نظام الإدارة القانونية - دليل النقل والتثبيت
# Legal Management System - Transfer and Installation Guide

## 📋 محتويات هذا المجلد / Folder Contents

### ملفات التكوين الأساسية / Core Configuration Files
- `package.json` - تبعيات Node.js ومعلومات المشروع
- `package-lock.json` - قفل إصدارات التبعيات
- `tsconfig.json` - إعدادات TypeScript
- `next.config.ts` - إعدادات Next.js Framework
- `postcss.config.mjs` - إعدادات PostCSS للتصميم
- `eslint.config.mjs` - إعدادات ESLint لجودة الكود

### المجلدات الأساسية / Core Directories
- `src/` - الكود المصدري للتطبيق
  - `app/` - صفحات التطبيق (Next.js App Router)
  - `components/` - مكونات React القابلة لإعادة الاستخدام
  - `lib/` - مكتبات ووظائف مساعدة
  - `hooks/` - React Hooks مخصصة
  - `middleware.ts` - وسطاء Next.js
- `public/` - الملفات العامة (صور، أيقونات، خطوط)
- `database/` - سكريپتات قاعدة البيانات والهجرة
- `scripts/` - سكريپتات النظام والصيانة
- `laws/` - ملفات القوانين والتشريعات
- `fonts/` - ملفات الخطوط العربية
- `database_backups/` - النسخ الاحتياطية لقاعدة البيانات

### ملفات قاعدة البيانات / Database Files
- `database_backups/mohammi_backup_*.sql.gz` - النسخة الاحتياطية المضغوطة
- `backup_database.sh` - سكريپت النسخ الاحتياطي

## 🚀 خطوات التثبيت على الجهاز الجديد / Installation Steps on New Machine

### 1. متطلبات النظام / System Requirements
- **Windows 10/11** أو أحدث
- **Node.js** (v18.0.0 أو أحدث) - [تحميل من هنا](https://nodejs.org/)
- **PostgreSQL** (v12 أو أحدث) - [تحميل من هنا](https://www.postgresql.org/download/windows/)
- **Git** (اختياري) - [تحميل من هنا](https://git-scm.com/download/win)

### 2. تثبيت PostgreSQL على Windows
1. قم بتحميل PostgreSQL من الموقع الرسمي
2. شغل ملف التثبيت كمدير
3. اتبع معالج التثبيت
4. احفظ كلمة مرور المستخدم `postgres`
5. تأكد من تشغيل خدمة PostgreSQL

### 3. تثبيت Node.js
1. قم بتحميل Node.js LTS من الموقع الرسمي
2. شغل ملف التثبيت
3. تأكد من تحديد خيار "Add to PATH"
4. أعد تشغيل الكمبيوتر بعد التثبيت

### 4. نسخ المشروع
1. انسخ مجلد `mohaminew` إلى الموقع المطلوب على الجهاز الجديد
2. افتح Command Prompt أو PowerShell كمدير
3. انتقل إلى مجلد المشروع:
```cmd
cd C:\path\to\mohaminew
```

### 5. تثبيت التبعيات / Install Dependencies
```bash
npm install
```

### 6. إعداد قاعدة البيانات / Database Setup

#### أ. إنشاء قاعدة بيانات جديدة
```cmd
# افتح Command Prompt وشغل:
createdb -U postgres mohammi
```

#### ب. استعادة النسخة الاحتياطية
```cmd
# فك ضغط النسخة الاحتياطية
cd database_backups
gunzip mohammi_backup_*.sql.gz

# استعادة البيانات
psql -U postgres -d mohammi < mohammi_backup_*.sql
```

### 7. تحديث إعدادات قاعدة البيانات / Update Database Settings
قم بتحديث ملف `src/lib/database.ts` بإعدادات قاعدة البيانات الجديدة:

```typescript
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'YOUR_NEW_PASSWORD', // ضع كلمة المرور الجديدة هنا
  ssl: false,
  connectionTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  max: 20
}
```

### 8. تشغيل النظام / Run the System
```bash
# للتطوير
npm run dev

# للإنتاج
npm run build
npm start
```

## 🔧 إعدادات مهمة / Important Settings

### معلومات الاتصال / Connection Information
- **المنفذ الافتراضي**: 7443
- **الرابط المحلي**: http://localhost:7443
- **اسم قاعدة البيانات**: mohammi
- **مستخدم قاعدة البيانات**: postgres

### بيانات الدخول الافتراضية / Default Login Credentials
- **المستخدم**: admin
- **كلمة المرور**: admin123

## 🛠️ استكشاف الأخطاء / Troubleshooting

### مشاكل شائعة / Common Issues

#### 1. خطأ في الاتصال بقاعدة البيانات
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```
**الحل**: تأكد من تشغيل خدمة PostgreSQL

#### 2. خطأ في كلمة المرور
```
Error: password authentication failed
```
**الحل**: تحديث كلمة المرور في ملف `src/lib/database.ts`

#### 3. المنفذ مستخدم
```
Error: listen EADDRINUSE :::7443
```
**الحل**: غير المنفذ في `package.json` أو أوقف العملية المستخدمة للمنفذ

### أوامر مفيدة / Useful Commands
```bash
# فحص حالة قاعدة البيانات
psql -U postgres -d mohammi -c "SELECT version();"

# إنشاء نسخة احتياطية جديدة
./backup_database.sh

# فحص المنافذ المستخدمة
netstat -ano | findstr :7443

# إيقاف عملية تستخدم منفذ معين
taskkill /PID <PID_NUMBER> /F
```

## 📞 الدعم / Support
إذا واجهت أي مشاكل، تحقق من:
1. تثبيت جميع المتطلبات بشكل صحيح
2. تشغيل خدمة PostgreSQL
3. صحة إعدادات قاعدة البيانات
4. توفر المنفذ 7443

## 📝 ملاحظات مهمة / Important Notes
- احتفظ بنسخة احتياطية من قاعدة البيانات بانتظام
- قم بتحديث كلمات المرور الافتراضية
- تأكد من تشغيل النظام على منفذ آمن في بيئة الإنتاج
- راجع ملفات التوثيق الأخرى للمزيد من التفاصيل
