<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج أنواع القضايا -->
<form-module name="issue-types" title="أنواع القضايا" icon="tags">
    
    <list-view name="list" title="قائمة أنواع القضايا">
        <data-source>
            SELECT 
                id, 
                name, 
                description, 
                is_active, 
                created_date,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = issue_types.id) as cases_count
            FROM issue_types
            ORDER BY name
        </data-source>
        
        <columns>
            <column name="name" title="اسم النوع" width="200" sortable="true"/>
            <column name="description" title="الوصف" width="300"/>
            <column name="cases_count" title="عدد القضايا" width="100" format="number"/>
            <column name="is_active" title="نشط" width="100" format="boolean"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <filters>
            <filter name="is_active" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </options>
            </filter>
        </filters>
        
        <actions>
            <action name="add" title="إضافة نوع جديد" icon="plus" color="primary"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"/>
            <action name="delete" title="حذف" icon="delete" color="danger" confirm="true"/>
            <action name="toggle_status" title="تغيير الحالة" icon="toggle-on" color="info"/>
        </actions>
    </list-view>
    
    <form-view name="add" title="إضافة نوع قضية جديد">
        <fields>
            <field name="name" title="اسم النوع" type="text" required="true" 
                   placeholder="مثال: قضايا مدنية"/>
            <field name="description" title="وصف النوع" type="textarea" rows="4" 
                   placeholder="وصف تفصيلي لنوع القضية"/>
            <field name="is_active" title="نشط" type="checkbox" default="true"/>
        </fields>
        
        <save-action>
            INSERT INTO issue_types (name, description, is_active)
            VALUES (@name, @description, @is_active)
        </save-action>
        
        <update-action>
            UPDATE issue_types SET
                name = @name,
                description = @description,
                is_active = @is_active
            WHERE id = @id
        </update-action>
    </form-view>
    
    <form-view name="edit" title="تعديل نوع القضية">
        <fields>
            <field name="name" title="اسم النوع" type="text" required="true"/>
            <field name="description" title="وصف النوع" type="textarea" rows="4"/>
            <field name="is_active" title="نشط" type="checkbox"/>
        </fields>
        
        <load-action>
            SELECT name, description, is_active FROM issue_types WHERE id = @id
        </load-action>
        
        <update-action>
            UPDATE issue_types SET
                name = @name,
                description = @description,
                is_active = @is_active
            WHERE id = @id
        </update-action>
    </form-view>
</form-module>
