const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function fixAdminUsers() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 إصلاح أسماء المستخدمين وتحديث بيانات المدير...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. تحديث المستخدم القديم (old_admin) ليصبح له بريد مختلف
    console.log('📧 تحديث البريد الإلكتروني للمستخدم القديم...');
    
    await client.query(`
      UPDATE users 
      SET 
        username = 'old_admin',
        email = '<EMAIL>'
      WHERE id = 1
    `);
    
    console.log('✅ تم تحديث المستخدم القديم');

    // 2. تحديث المستخدم الجديد (admin2) ليصبح admin مع كلمة المرور الجديدة
    console.log('\n🔐 تحديث بيانات المدير محمد الحاشدي...');
    
    const hashedPassword = await bcrypt.hash('ana8080', 10);
    
    const updateResult = await client.query(`
      UPDATE users 
      SET 
        username = 'admin',
        password_hash = $1,
        email = '<EMAIL>',
        updated_at = NOW()
      WHERE id = 5
      RETURNING id, username, email, role, employee_id
    `, [hashedPassword]);

    const updatedUser = updateResult.rows[0];

    // 3. تحديث البريد الإلكتروني للموظف المرتبط
    console.log('\n📧 تحديث البريد الإلكتروني للموظف...');
    
    await client.query(`
      UPDATE employees 
      SET 
        email = '<EMAIL>',
        updated_at = NOW()
      WHERE id = $1
    `, [updatedUser.employee_id]);

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم تحديث بيانات المستخدم بنجاح!');
    console.log('================================');
    console.log('🔐 بيانات تسجيل الدخول الجديدة للمدير محمد الحاشدي:');
    console.log(`   👤 اسم المستخدم: ${updatedUser.username}`);
    console.log(`   🔑 كلمة المرور: ana8080`);
    console.log(`   📧 البريد الإلكتروني: ${updatedUser.email}`);
    console.log(`   👨‍💼 الدور: ${updatedUser.role}`);
    console.log(`   🆔 معرف المستخدم: ${updatedUser.id}`);
    console.log(`   🔗 مرتبط بالموظف: ${updatedUser.employee_id}`);
    
    console.log('\n================================');
    console.log('✅ يمكنك الآن تسجيل الدخول باستخدام:');
    console.log(`   اسم المستخدم: admin`);
    console.log(`   كلمة المرور: ana8080`);
    console.log('================================');

    // 4. عرض حالة جميع المستخدمين
    console.log('\n👥 حالة المستخدمين بعد التحديث:');
    
    const allUsers = await client.query(`
      SELECT id, username, email, role, employee_id, is_active
      FROM users 
      ORDER BY id
    `);

    allUsers.rows.forEach(user => {
      const status = user.is_active ? '🟢' : '🔴';
      console.log(`${status} 🆔 ${user.id} | 👤 ${user.username} | 📧 ${user.email} | 👨‍💼 ${user.role} | 🔗 موظف: ${user.employee_id || 'غير مرتبط'}`);
    });

    // 5. عرض معلومات الموظف المرتبط
    console.log('\n👤 معلومات الموظف المرتبط بالمدير الجديد:');
    
    const employeeInfo = await client.query(`
      SELECT name, position, department, phone, email, employee_number
      FROM employees 
      WHERE id = $1
    `, [updatedUser.employee_id]);

    if (employeeInfo.rows.length > 0) {
      const emp = employeeInfo.rows[0];
      console.log(`   الاسم: ${emp.name}`);
      console.log(`   المنصب: ${emp.position}`);
      console.log(`   القسم: ${emp.department}`);
      console.log(`   رقم الموظف: ${emp.employee_number}`);
      console.log(`   الهاتف: ${emp.phone}`);
      console.log(`   البريد: ${emp.email}`);
    }

    // 6. عرض الصلاحيات
    console.log('\n🔐 صلاحيات المدير الجديد:');
    
    const permissions = await client.query(`
      SELECT permissions, user_type, status
      FROM users 
      WHERE id = $1
    `, [updatedUser.id]);

    if (permissions.rows.length > 0) {
      const perms = permissions.rows[0];
      console.log(`   نوع المستخدم: ${perms.user_type}`);
      console.log(`   الصلاحيات: ${perms.permissions}`);
      console.log(`   الحالة: ${perms.status}`);
    }

    console.log('\n🔗 رابط تسجيل الدخول: http://localhost:7443/login');

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تحديث بيانات المستخدمين:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixAdminUsers().catch(console.error);
