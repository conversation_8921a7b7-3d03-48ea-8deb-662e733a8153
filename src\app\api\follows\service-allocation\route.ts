import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب تفاصيل توزيع الخدمة للمحامي في قضية معينة
export async function GET(request: NextRequest) {
  try {
    // 🚨 إيقاف فحص المصادقة مؤقتاً - السماح لجميع المستخدمين بالوصول
    /*
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'رمز المصادقة مطلوب' },
        { status: 401 }
      )
    }
    */

    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('case_id')
    const serviceId = searchParams.get('service_id')
    const lawyerId = searchParams.get('lawyer_id')
    
    if (!caseId || !serviceId || !lawyerId) {
      return NextResponse.json(
        { success: false, error: 'معرف القضية ومعرف الخدمة ومعرف المحامي مطلوبان' },
        { status: 400 }
      )
    }
    
    // جلب تفاصيل توزيع الخدمة (مع السماح لأي محامي بالوصول)
    const result = await query(`
      SELECT
        cd.id as case_distribution_id,
        cd.issue_id,
        cd.admin_amount,
        cd.remaining_amount,
        sd.id as service_distribution_id,
        sd.service_id,
        sd.percentage,
        sd.amount as allocated_amount,
        sd.lawyer_id as original_lawyer_id,
        $3 as lawyer_id, -- استخدام المحامي المطلوب بدلاً من المحامي المخصص
        s.name as service_name,
        i.case_number,
        i.title as case_title,
        i.amount as total_case_amount,
        -- حساب المبلغ المكتسب حتى الآن للمحامي المطلوب
        COALESCE(SUM(le.earned_amount), 0) as total_earned,
        -- للمبالغ الثابتة: لا يوجد مبلغ متبقي (متابعات بلا حدود)
        -- للنسب المئوية: المبلغ المتبقي = المبلغ المخصص - المبلغ المكتسب
        CASE
          WHEN sd.amount <= 100 THEN (sd.amount - COALESCE(SUM(le.earned_amount), 0))
          ELSE -1 -- -1 يعني غير محدود للمبالغ الثابتة
        END as remaining_amount_for_service
      FROM case_distribution cd
      JOIN service_distributions sd ON cd.id = sd.case_distribution_id
      JOIN services s ON sd.service_id = s.id
      JOIN issues i ON cd.issue_id = i.id
      LEFT JOIN lawyer_earnings le ON le.case_id = cd.issue_id
        AND le.service_id = sd.service_id
        AND le.lawyer_id = $3  -- استخدام المحامي المطلوب
      WHERE cd.issue_id = $1
        AND sd.service_id = $2
        -- إزالة شرط sd.lawyer_id = $3 للسماح لأي محامي
      GROUP BY cd.id, sd.id, s.name, i.case_number, i.title, i.amount, sd.lawyer_id
    `, [caseId, serviceId, lawyerId])
    
    if (result.rows.length === 0) {
      // التحقق من وجود توزيع للقضية بشكل عام
      const caseDistributionCheck = await query(`
        SELECT 
          cd.id,
          sd.service_id,
          sd.lawyer_id,
          s.name as service_name,
          e.name as lawyer_name
        FROM case_distribution cd
        LEFT JOIN service_distributions sd ON cd.id = sd.case_distribution_id
        LEFT JOIN services s ON sd.service_id = s.id
        LEFT JOIN employees e ON sd.lawyer_id = e.id
        WHERE cd.issue_id = $1
      `, [caseId])
      
      if (caseDistributionCheck.rows.length === 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'لا يوجد توزيع مالي لهذه القضية. يرجى إضافة التوزيع في صفحة توزيع القضايا أولاً.',
            errorType: 'NO_CASE_DISTRIBUTION'
          },
          { status: 404 }
        )
      }
      
      // التحقق من وجود الخدمة في التوزيع
      const serviceInDistribution = caseDistributionCheck.rows.find(row => row.service_id == serviceId)
      if (!serviceInDistribution) {
        const availableServices = caseDistributionCheck.rows
          .filter(row => row.service_id)
          .map(row => `${row.service_name}`)
          .join(', ')
        
        return NextResponse.json(
          { 
            success: false, 
            error: `الخدمة المحددة غير موجودة في توزيع هذه القضية. الخدمات المتاحة: ${availableServices}`,
            errorType: 'SERVICE_NOT_IN_DISTRIBUTION',
            availableServices: caseDistributionCheck.rows.filter(row => row.service_id)
          },
          { status: 404 }
        )
      }
      
      // 🚨 إيقاف فحص تخصيص المحامي مؤقتاً - السماح لجميع المحاميين بإضافة متابعات
      // التحقق من أن المحامي مخصص لهذه الخدمة (معطل مؤقتاً)
      /*
      if (serviceInDistribution.lawyer_id != lawyerId) {
        return NextResponse.json(
          {
            success: false,
            error: `هذه الخدمة مخصصة للمحامي: ${serviceInDistribution.lawyer_name}. لا يمكنك إضافة متابعة لخدمة غير مخصصة لك.`,
            errorType: 'WRONG_LAWYER',
            assignedLawyer: {
              id: serviceInDistribution.lawyer_id,
              name: serviceInDistribution.lawyer_name
            }
          },
          { status: 403 }
        )
      }
      */
      
      return NextResponse.json(
        { 
          success: false, 
          error: 'لم يتم العثور على توزيع مالي لهذه الخدمة في القضية المحددة.',
          errorType: 'NO_SERVICE_ALLOCATION'
        },
        { status: 404 }
      )
    }
    
    const allocation = result.rows[0]
    const allocatedAmount = parseFloat(allocation.allocated_amount)
    const totalEarned = parseFloat(allocation.total_earned)
    const remainingAmountRaw = parseFloat(allocation.remaining_amount_for_service)
    const remainingAmount = remainingAmountRaw === -1 ? 'غير محدود' : remainingAmountRaw

    // تحديد إمكانية الاستحقاق بناءً على نوع الخدمة
    let canEarn = false
    if (allocatedAmount <= 100) {
      // نسبة مئوية: يمكن الاستحقاق مرة واحدة فقط
      canEarn = totalEarned === 0
    } else {
      // مبلغ ثابت: يمكن الاستحقاق متعدد المرات (بدون حد أقصى)
      // المحامي يحصل على المبلغ كاملاً في كل متابعة
      canEarn = true // دائماً متاح للمبالغ الثابتة
    }

    return NextResponse.json({
      success: true,
      data: {
        case_distribution_id: allocation.case_distribution_id,
        service_distribution_id: allocation.service_distribution_id,
        case_id: allocation.issue_id,
        case_number: allocation.case_number,
        case_title: allocation.case_title,
        total_case_amount: parseFloat(allocation.total_case_amount),
        admin_amount: parseFloat(allocation.admin_amount),
        remaining_amount: parseFloat(allocation.remaining_amount),
        service_id: allocation.service_id,
        service_name: allocation.service_name,
        lawyer_id: allocation.lawyer_id,
        percentage: parseFloat(allocation.percentage),
        allocated_amount: allocatedAmount,
        total_earned: totalEarned,
        remaining_amount_for_service: remainingAmount,
        can_earn: canEarn,
        service_type: allocatedAmount <= 100 ? 'percentage' : 'fixed_amount'
      }
    })
  } catch (error) {
    console.error('Error fetching service allocation:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب تفاصيل توزيع الخدمة' },
      { status: 500 }
    )
  }
}

// POST - تسجيل مبلغ مكتسب للمحامي عند إضافة متابعة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      lawyer_id,
      case_id,
      service_id,
      follow_id,
      earning_percentage = 100, // نسبة الاستحقاق من المبلغ المخصص (افتراضي 100%)
      notes
    } = body

    if (!lawyer_id || !case_id || !service_id || !follow_id) {
      return NextResponse.json(
        { success: false, error: 'جميع المعاملات مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من وجود توزيع للخدمة
    const allocationResult = await query(`
      SELECT 
        sd.amount as allocated_amount,
        COALESCE(SUM(le.earned_amount), 0) as total_earned
      FROM case_distribution cd
      JOIN service_distributions sd ON cd.id = sd.case_distribution_id
      LEFT JOIN lawyer_earnings le ON le.case_id = cd.issue_id 
        AND le.service_id = sd.service_id 
        AND le.lawyer_id = sd.lawyer_id
      WHERE cd.issue_id = $1 
        AND sd.service_id = $2 
        AND sd.lawyer_id = $3
      GROUP BY sd.amount
    `, [case_id, service_id, lawyer_id])

    if (allocationResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لم يتم العثور على توزيع للخدمة المحددة' },
        { status: 404 }
      )
    }

    const allocation = allocationResult.rows[0]
    const allocatedAmount = parseFloat(allocation.allocated_amount)
    const totalEarned = parseFloat(allocation.total_earned)
    const remainingAmount = allocatedAmount - totalEarned

    // حساب المبلغ المكتسب بناءً على نوع الخدمة
    let earnedAmount

    // تحديد نوع الخدمة: نسبة مئوية أم مبلغ ثابت
    if (allocatedAmount <= 100) {
      // إذا كان المبلغ المخصص ≤ 100، فهو نسبة مئوية
      // في هذه الحالة، يُسمح بمتابعة واحدة فقط ويُسجل المبلغ كاملاً
      if (totalEarned > 0) {
        return NextResponse.json(
          { success: false, error: 'تم تسجيل مبلغ لهذه الخدمة مسبقاً. الخدمات ذات النسبة المئوية تسمح بمتابعة واحدة فقط.' },
          { status: 400 }
        )
      }
      earnedAmount = allocatedAmount // تسجيل المبلغ المخصص كاملاً
    } else {
      // إذا كان المبلغ المخصص > 100، فهو مبلغ ثابت
      // يمكن إضافة متابعات متعددة، كل متابعة تحصل على المبلغ المخصص كاملاً
      earnedAmount = allocatedAmount // المبلغ المخصص كاملاً لكل متابعة
    }

    // للنسب المئوية، التأكد من عدم وجود مبلغ مسجل مسبقاً
    if (allocatedAmount <= 100 && totalEarned > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'تم تسجيل مبلغ لهذه الخدمة مسبقاً. الخدمات ذات النسبة المئوية تسمح بمتابعة واحدة فقط.'
        },
        { status: 400 }
      )
    }

    // للمبالغ الثابتة: لا يوجد حد أقصى، يمكن إضافة متابعات متعددة
    // كل متابعة تحصل على المبلغ المخصص كاملاً

    // تسجيل المبلغ المكتسب
    const result = await query(`
      INSERT INTO lawyer_earnings (
        lawyer_id, case_id, service_id, follow_id, 
        allocated_amount, earned_amount, notes
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [
      lawyer_id, case_id, service_id, follow_id,
      allocatedAmount, earnedAmount, notes
    ])

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل المبلغ المكتسب بنجاح',
      data: {
        earning_id: result.rows[0].id,
        allocated_amount: allocatedAmount,
        earned_amount: earnedAmount,
        // للمبالغ الثابتة: لا يوجد مبلغ متبقي (يمكن إضافة متابعات بلا حدود)
        // للنسب المئوية: المبلغ المتبقي = 0 بعد المتابعة الأولى
        remaining_amount: allocatedAmount <= 100 ? 0 : 'غير محدود'
      }
    })
  } catch (error) {
    console.error('Error recording lawyer earning:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تسجيل المبلغ المكتسب' },
      { status: 500 }
    )
  }
}
