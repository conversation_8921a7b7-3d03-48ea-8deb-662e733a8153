import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الخدمات
export async function GET() {
  try {
    const result = await query(`
      SELECT
        s.*,
        l.name as lineage_name
      FROM services s
      LEFT JOIN lineages l ON s.lineage_id = l.id
      ORDER BY s.name
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching services:', error)

    // في حالة الخطأ، إرجاع بيانات افتراضية
    const sampleData = [
      {
        id: 1,
        name: 'استشارة قانونية',
        description: 'تقديم المشورة القانونية للعملاء',
        category: 'استشارات'
      },
      {
        id: 2,
        name: 'إعداد الدعوى',
        description: 'تحضير وإعداد أوراق الدعوى القضائية',
        category: 'إجراءات'
      },
      {
        id: 3,
        name: 'حضور جلسة',
        description: 'التمثيل أمام المحكمة في الجلسات',
        category: 'تمثيل'
      },
      {
        id: 4,
        name: 'متابعة القضية',
        description: 'متابعة سير القضية والإجراءات',
        category: 'متابعة'
      },
      {
        id: 5,
        name: 'إشراف عام',
        description: 'الإشراف العام على القضية',
        category: 'إدارة'
      },
      {
        id: 6,
        name: 'صياغة عقود',
        description: 'كتابة وصياغة العقود القانونية',
        category: 'صياغة'
      },
      {
        id: 8,
        name: 'مراجعة قانونية',
        description: 'مراجعة الوثائق والعقود قانونياً',
        category: 'مراجعة'
      },
      {
        id: 9,
        name: 'تنفيذ الأحكام',
        description: 'متابعة تنفيذ الأحكام القضائية',
        category: 'تنفيذ'
      },
      {
        id: 10,
        name: 'التحكيم',
        description: 'إجراءات التحكيم والوساطة',
        category: 'تحكيم'
      },
      {
        id: 11,
        name: 'البحث القانوني',
        description: 'إجراء البحوث القانونية المتخصصة',
        category: 'بحث'
      },
      {
        id: 12,
        name: 'التفاوض',
        description: 'التفاوض نيابة عن العميل',
        category: 'تفاوض'
      },
      {
        id: 13,
        name: 'الاستئناف',
        description: 'إجراءات الطعن والاستئناف',
        category: 'طعون'
      }
    ]

    return NextResponse.json({
      success: true,
      data: sampleData
    })
  }
}

// POST - إضافة خدمة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, lineage_id } = body

    if (!name || !name.trim()) {
      return NextResponse.json(
        { success: false, error: 'اسم الخدمة مطلوب' },
        { status: 400 }
      )
    }

    if (!lineage_id) {
      return NextResponse.json(
        { success: false, error: 'مجموعة النسب المالية مطلوبة' },
        { status: 400 }
      )
    }

    // محاكاة إضافة خدمة جديدة
    const newService = {
      id: Date.now(),
      name: name.trim(),
      lineage_id: Number(lineage_id),
      created_date: new Date().toISOString().split('T')[0]
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الخدمة بنجاح',
      data: newService
    })
  } catch (error) {
    console.error('Error creating service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الخدمة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث خدمة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, lineage_id } = body

    if (!id || !name || !name.trim()) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الخدمة مطلوبان' },
        { status: 400 }
      )
    }

    if (!lineage_id) {
      return NextResponse.json(
        { success: false, error: 'مجموعة النسب المالية مطلوبة' },
        { status: 400 }
      )
    }

    // محاكاة تحديث الخدمة
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الخدمة بنجاح'
    })
  } catch (error) {
    console.error('Error updating service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الخدمة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف خدمة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الخدمة مطلوب' },
        { status: 400 }
      )
    }

    // محاكاة حذف الخدمة
    return NextResponse.json({
      success: true,
      message: 'تم حذف الخدمة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting service:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الخدمة' },
      { status: 500 }
    )
  }
}
