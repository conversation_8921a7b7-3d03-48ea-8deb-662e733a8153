'use client'

import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ChevronDown, Search, User } from 'lucide-react'

interface Employee {
  id: number
  name: string
  position: string
  phone: string
  email: string
  department: string
}

interface EmployeeSelectProps {
  value: string
  onChange: (employeeId: string, employeeData: Employee | null) => void
  label?: string
  placeholder?: string
  required?: boolean
}

export function EmployeeSelect({ value, onChange, label = "المحامي", placeholder = "اختر المحامي", required = false }: EmployeeSelectProps) {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const fetchEmployees = async () => {
    setIsLoading(true)
    console.log('🔍 EmployeeSelect: بدء جلب الموظفين...')
    try {
      const response = await fetch('/api/employees')
      console.log('📡 EmployeeSelect: استجابة API:', response.status)
      const result = await response.json()
      console.log('📊 EmployeeSelect: بيانات الموظفين:', result)

      if (result.success) {
        // التحقق من وجود البيانات في المكان الصحيح
        const employeeData = result.employees || result.data || []
        if (Array.isArray(employeeData)) {
          setEmployees(employeeData)
          console.log('✅ EmployeeSelect: تم تحديث قائمة الموظفين:', employeeData.length, 'موظف')
        } else {
          console.error('❌ EmployeeSelect: البيانات ليست مصفوفة:', employeeData)
          setEmployees([])
        }
      } else {
        console.error('❌ EmployeeSelect: فشل في جلب الموظفين:', result.error)
        setEmployees([])
      }
    } catch (error) {
      console.error('💥 EmployeeSelect: خطأ في جلب الموظفين:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchEmployees()
  }, [])

  useEffect(() => {
    if (value && Array.isArray(employees) && employees.length > 0) {
      const employee = employees.find(e => e && e.id && e.id.toString() === value)
      setSelectedEmployee(employee || null)
    } else {
      setSelectedEmployee(null)
    }
  }, [value, employees])

  const filteredEmployees = Array.isArray(employees) ? employees.filter(employee => {
    if (!employee || typeof employee !== 'object') return false
    return (
      (employee.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (employee.position || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (employee.department || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  }) : []

  const handleSelect = (employee: Employee) => {
    setSelectedEmployee(employee)
    onChange(employee.id.toString(), employee)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleClear = () => {
    setSelectedEmployee(null)
    onChange('', null)
    setSearchTerm('')
  }

  return (
    <div className="relative">
      <Label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      <div className="relative">
        <div
          className="w-full px-3 py-2 border border-gray-300 rounded-md cursor-pointer bg-white flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center">
            <User className="h-4 w-4 mr-2 text-gray-400" />
            <span className={selectedEmployee ? 'text-gray-900' : 'text-gray-500'}>
              {selectedEmployee ? selectedEmployee.name : placeholder}
            </span>
          </div>
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-[80] w-full mt-1 bg-white border border-gray-300 rounded-md shadow-xl max-h-80 overflow-hidden">
            {/* شريط البحث */}
            <div className="p-3 border-b bg-gray-50">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في المحامين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 text-sm h-8 border-gray-200 focus:border-blue-500"
                  autoFocus
                />
              </div>
            </div>

            {/* قائمة المحامين */}
            <div className="max-h-60 overflow-y-auto">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500 text-sm">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : filteredEmployees.length > 0 ? (
                <>
                  {selectedEmployee && (
                    <div
                      className="p-3 hover:bg-red-50 cursor-pointer border-b text-red-600 transition-colors"
                      onClick={handleClear}
                    >
                      <div className="flex items-center">
                        <span className="text-sm font-medium">✕ إلغاء الاختيار</span>
                      </div>
                    </div>
                  )}
                  {filteredEmployees.map((employee) => (
                    <div
                      key={employee.id}
                      className="p-3 hover:bg-blue-50 cursor-pointer border-b last:border-b-0 transition-colors"
                      onClick={() => handleSelect(employee)}
                    >
                      <div className="font-medium text-gray-900 text-sm">{employee.name || 'غير محدد'}</div>
                      {employee.position && (
                        <div className="text-xs text-gray-500 mt-1">{employee.position}</div>
                      )}
                    </div>
                  ))}
                </>
              ) : (
                <div className="p-4 text-center text-gray-500 text-sm">
                  <div className="text-gray-400 mb-2">👤</div>
                  {searchTerm ? 'لا توجد نتائج للبحث' : 'لا توجد محامين'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>



      {/* حقل مخفي للقيمة */}
      <input type="hidden" value={value} name="employee_id" />
    </div>
  )
}
