{"version": 3, "sources": ["../../../../src/shared/lib/isomorphic/path.js"], "sourcesContent": ["/**\n * This module is for next.js server internal usage of path module.\n * It will use native path module for nodejs runtime.\n * It will use path-browserify polyfill for edge runtime.\n */\nlet path\n\nif (process.env.NEXT_RUNTIME === 'edge') {\n  path = require('next/dist/compiled/path-browserify')\n} else {\n  path = require('path')\n}\n\nmodule.exports = path\n"], "names": ["path", "process", "env", "NEXT_RUNTIME", "require", "module", "exports"], "mappings": "AAAA;;;;CAIC;AACD,IAAIA;AAEJ,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,OAAOI,QAAQ;AACjB,OAAO;IACLJ,OAAOI,QAAQ;AACjB;AAEAC,OAAOC,OAAO,GAAGN"}