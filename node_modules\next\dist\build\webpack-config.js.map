{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { getDefineEnv } from './define-env'\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  shouldUseReactServerCondition,\n  isWebpackDefaultLayer,\n  RSPACK_DEFAULT_LAYERS_REGEX,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin as NextFlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { RspackFlightClientEntryPlugin } from './webpack/plugins/rspack-flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createVendoredReactAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\nimport { getRspackCore, getRspackReactRefresh } from '../shared/lib/get-rspack'\nimport { RspackProfilingPlugin } from './webpack/plugins/rspack-profiling-plugin'\nimport getWebpackBundler from '../shared/lib/get-webpack-bundler'\nimport type { NextBuildContext } from './build-context'\nimport type { RootParamsLoaderOpts } from './webpack/loaders/next-root-params-loader'\nimport type { InvalidImportLoaderOpts } from './webpack/loaders/next-invalid-import-error-loader'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|dynamic-access-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nfunction getReactRefreshLoader() {\n  return process.env.NEXT_RSPACK\n    ? getRspackReactRefresh().loader\n    : require.resolve(reactRefreshLoaderName)\n}\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = getReactRefreshLoader()\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, jsConfigPath, resolvedBaseUrl } = await loadJsConfig(\n    dir,\n    config\n  )\n  const supportedBrowsers = getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api') as typeof import('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport function getCacheDirectories(\n  configs: webpack.Configuration[]\n): Set<string> {\n  return new Set(\n    configs\n      .map((cfg) => {\n        if (typeof cfg.cache === 'object' && cfg.cache.type === 'filesystem') {\n          return cfg.cache.cacheDirectory\n        }\n        return null\n      })\n      .filter((dir) => dir != null)\n  )\n}\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    noMangling,\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    isCompileMode,\n    previewProps,\n  }: {\n    previewProps: NonNullable<(typeof NextBuildContext)['previewProps']>\n    isCompileMode?: boolean\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir: string | undefined\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir: string | undefined\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    jsConfigPath?: string\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const bundler = getWebpackBundler()\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n\n  const FlightClientEntryPlugin =\n    isRspack && process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN\n      ? RspackFlightClientEntryPlugin\n      : NextFlightClientEntryPlugin\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = (\n      require('./swc') as typeof import('./swc')\n    )?.getBinaryMetadata?.()?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const excludeCache: Record<string, boolean> = {}\n  function exclude(excludePath: string): boolean {\n    const cached = excludeCache[excludePath]\n    if (cached !== undefined) {\n      return cached\n    }\n\n    const shouldExclude =\n      excludePath.includes('node_modules') &&\n      !babelIncludeRegexes.some((r) => r.test(excludePath)) &&\n      !isResourceInPackages(excludePath, finalTranspilePackages)\n\n    excludeCache[excludePath] = shouldExclude\n    return shouldExclude\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude,\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      ;(\n        require('./swc') as typeof import('./swc')\n      )?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    const useBuiltinSwcLoader = process.env.BUILTIN_SWC_LOADER\n    if (isRspack && useBuiltinSwcLoader) {\n      return {\n        loader: 'builtin:next-swc-loader',\n        options: {\n          isServer: isNodeOrEdgeCompilation,\n          rootDir: dir,\n          pagesDir,\n          appDir,\n          hasReactRefresh: dev && isClient,\n          transpilePackages: finalTranspilePackages,\n          supportedBrowsers,\n          swcCacheDir: path.join(\n            dir,\n            config?.distDir ?? '.next',\n            'cache',\n            'swc'\n          ),\n          serverReferenceHashSalt: encryptionKey,\n\n          // rspack specific options\n          pnp: Boolean(process.versions.pnp),\n          optimizeServerReact: Boolean(config.experimental.optimizeServerReact),\n          modularizeImports: config.modularizeImports,\n          decorators: Boolean(\n            jsConfig?.compilerOptions?.experimentalDecorators\n          ),\n          emitDecoratorMetadata: Boolean(\n            jsConfig?.compilerOptions?.emitDecoratorMetadata\n          ),\n          regeneratorRuntimePath: require.resolve(\n            'next/dist/compiled/regenerator-runtime'\n          ),\n\n          ...extraOptions,\n        },\n      }\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        compilerType,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  const reactRefreshLoaders = dev && isClient ? [getReactRefreshLoader()] : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.apiNode,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const reactRefreshEntry = isRspack\n    ? getRspackReactRefresh().entry\n    : require.resolve(\n        `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n      )\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: reactRefreshEntry,\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    reactRefreshEntry,\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n    ...((isRspack && jsConfigPath\n      ? {\n          // Skip paths that are routed to a .d.ts file\n          restrictions: [/^(?!.*\\.d\\.ts$).*$/],\n          tsConfig: {\n            configFile: jsConfigPath,\n          },\n        }\n      : {}) as any),\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = (require('module') as typeof import('module'))\n    .builtinModules\n\n  const bunExternals = [\n    'bun:ffi',\n    'bun:jsc',\n    'bun:sqlite',\n    'bun:test',\n    'bun:wrap',\n    'bun',\n  ]\n\n  const shouldEnableSlowModuleDetection =\n    !!config.experimental.slowModuleDetection && dev\n\n  const getParallelism = () => {\n    const override = Number(process.env.NEXT_WEBPACK_PARALLELISM)\n    if (shouldEnableSlowModuleDetection) {\n      if (override) {\n        console.warn(\n          'NEXT_WEBPACK_PARALLELISM is specified but will be ignored due to experimental.slowModuleDetection being enabled.'\n        )\n      }\n      return 1\n    }\n    return override || undefined\n  }\n\n  const telemetryPlugin =\n    !dev &&\n    isClient &&\n    new (\n      require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n    ).TelemetryPlugin(\n      new Map(\n        [\n          ['swcLoader', useSWCLoader],\n          ['swcRelay', !!config.compiler?.relay],\n          ['swcStyledComponents', !!config.compiler?.styledComponents],\n          [\n            'swcReactRemoveProperties',\n            !!config.compiler?.reactRemoveProperties,\n          ],\n          [\n            'swcExperimentalDecorators',\n            !!jsConfig?.compilerOptions?.experimentalDecorators,\n          ],\n          ['swcRemoveConsole', !!config.compiler?.removeConsole],\n          ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n          ['swcEmotion', !!config.compiler?.emotion],\n          ['transpilePackages', !!config.transpilePackages],\n          ['skipMiddlewareUrlNormalize', !!config.skipMiddlewareUrlNormalize],\n          ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n          ['modularizeImports', !!config.modularizeImports],\n          // If esmExternals is not same as default value, it represents customized usage\n          ['esmExternals', config.experimental.esmExternals !== true],\n          SWCBinaryTarget,\n        ].filter<[Feature, boolean]>(Boolean as any)\n      )\n    )\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: getParallelism(),\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ...bunExternals,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: isRspack\n            ? // using a function here causes noticable slowdown\n              // in rspack\n              /(?!polyfills|main|pages\\/_app)/\n            : (chunk: any) =>\n                !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n\n          cacheGroups: isRspack\n            ? {\n                framework: {\n                  chunks: 'all' as const,\n                  name: 'framework',\n                  layer: RSPACK_DEFAULT_LAYERS_REGEX,\n                  test: /[/]node_modules[/](react|react-dom|next[/]dist[/]compiled[/](react|react-dom)(-experimental)?)[/]/,\n                  priority: 40,\n                  enforce: true,\n                },\n                lib: {\n                  test: /[/]node_modules[/](?!.*\\.(css|scss|sass|less|styl)$)/,\n                  name: 'lib',\n                  chunks: 'all',\n                  priority: 30,\n                  minChunks: 1,\n                  reuseExistingChunk: true,\n                },\n              }\n            : {\n                framework: frameworkCacheGroup,\n                lib: libCacheGroup,\n              },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: [\n        // Minify JavaScript\n        isRspack\n          ? new (getRspackCore().SwcJsMinimizerRspackPlugin)({\n              // JS minimizer configuration\n              // options should align with crates/napi/src/minify.rs#patch_opts\n              minimizerOptions: {\n                compress: {\n                  inline: 2,\n                  global_defs: {\n                    'process.env.__NEXT_PRIVATE_MINIMIZE_MACRO_FALSE': false,\n                  },\n                },\n                mangle: !noMangling && {\n                  reserved: ['AbortSignal'],\n                  disableCharFreq: !isClient,\n                },\n              },\n            })\n          : (compiler: webpack.Compiler) => {\n              // @ts-ignore No typings yet\n              const { MinifyPlugin } =\n                require('./webpack/plugins/minify-webpack-plugin/src') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n              new MinifyPlugin({\n                noMangling,\n                disableCharFreq: !isClient,\n              }).apply(compiler)\n            },\n        // Minify CSS\n        // By default, Rspack uses LightningCSS for CSS minification.\n        // Rspack uses css-minimizer-plugin by default for compatibility.\n        isRspack &&\n        (process.env.__NEXT_TEST_MODE\n          ? config.experimental.useLightningcss\n          : config.experimental?.useLightningcss === undefined ||\n            config.experimental.useLightningcss)\n          ? new (getRspackCore().LightningCssMinimizerRspackPlugin)({\n              // CSS minimizer configuration\n              minimizerOptions: {\n                targets: supportedBrowsers,\n              },\n            })\n          : (compiler: webpack.Compiler) => {\n              const { CssMinimizerPlugin } =\n                require('./webpack/plugins/css-minimizer-plugin') as typeof import('./webpack/plugins/css-minimizer-plugin')\n              new CssMinimizerPlugin({\n                postcssOptions: {\n                  map: {\n                    // `inline: false` generates the source map in a separate file.\n                    // Otherwise, the CSS file is needlessly large.\n                    inline: false,\n                    // `annotation: false` skips appending the `sourceMappingURL`\n                    // to the end of the CSS file. Webpack already handles this.\n                    annotation: false,\n                  },\n                },\n              }).apply(compiler)\n            },\n      ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n        'next-root-params-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: shouldUseReactServerCondition,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: shouldUseReactServerCondition,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resourceQuery: {\n                  // Do not apply next-flight-loader to imports generated by the\n                  // next-metadata-image-loader, to avoid generating unnecessary\n                  // and conflicting entries in the flight client entry plugin.\n                  // These are already covered by the next-metadata-route-loader\n                  // entries.\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createVendoredReactAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isBrowser: isClient,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n\n        ...getNextRootParamsRules({\n          isRootParamsEnabled:\n            config.experimental.rootParams ??\n            // `experimental.dynamicIO` implies `experimental.rootParams`.\n            config.experimental.cacheComponents ??\n            false,\n          isClient,\n          appDir,\n          pageExtensions,\n        }),\n\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: shouldUseReactServerCondition,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createVendoredReactAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isBrowser: isClient,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createVendoredReactAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isBrowser: isClient,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createVendoredReactAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isBrowser: isClient,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiNode,\n              use: apiRoutesLayerLoaders,\n              // In Node.js, switch back to normal URL handling.\n              // We won't bundle `new URL()` cases in Node.js bundler layer.\n              parser: {\n                url: true,\n              },\n            },\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiEdge,\n              use: apiRoutesLayerLoaders,\n              // In Edge runtime, we leave the url handling by default.\n              // The new URL assets will be converted into edge assets through assets loader.\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createVendoredReactAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isBrowser: isClient,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createVendoredReactAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isBrowser: isClient,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: shouldUseReactServerCondition,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new bundler.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              case null:\n              case undefined:\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev &&\n        isClient &&\n        (isRspack\n          ? // eslint-disable-next-line\n            new (getRspackReactRefresh() as any)({\n              injectLoader: false,\n              injectEntry: false,\n              overlay: false,\n            })\n          : new ReactRefreshWebpackPlugin(webpack)),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new bundler.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n\n      new (getWebpackBundler().DefinePlugin)(\n        getDefineEnv({\n          isTurbopack: false,\n          config,\n          dev,\n          distDir,\n          projectPath: dir,\n          fetchCacheKeyPrefix,\n          hasRewrites,\n          isClient,\n          isEdgeServer,\n          isNodeServer,\n          middlewareMatchers,\n          omitNonDeterministic: isCompileMode,\n          rewrites,\n        })\n      ),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      // rspack doesn't support the parser hooks used here\n      !isRspack && (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new ((\n          require('./webpack/plugins/next-trace-entrypoints-plugin') as typeof import('./webpack/plugins/next-trace-entrypoints-plugin')\n        )\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            traceIgnores: [],\n            compilerType,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new bundler.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            __NEXT_PREVIEW_MODE_ID: previewProps.previewModeId,\n            __NEXT_PREVIEW_MODE_SIGNING_KEY: previewProps.previewModeSigningKey,\n            __NEXT_PREVIEW_MODE_ENCRYPTION_KEY:\n              previewProps.previewModeEncryptionKey,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      isRspack\n        ? new RspackProfilingPlugin({ runWebpackSpan })\n        : new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        (isRspack\n          ? new (getRspackCore().experiments.CssChunkingPlugin)({\n              strict: config.experimental.cssChunking === 'strict',\n              nextjs: true,\n            })\n          : new CssChunkingPlugin(\n              config.experimental.cssChunking === 'strict'\n            )),\n      telemetryPlugin,\n      !dev &&\n        isNodeServer &&\n        new (\n          require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n        ).TelemetryPlugin(new Map()),\n      shouldEnableSlowModuleDetection &&\n        new (\n          require('./webpack/plugins/slow-module-detection-plugin') as typeof import('./webpack/plugins/slow-module-detection-plugin')\n        ).default({\n          compilerType,\n          ...config.experimental.slowModuleDetection!,\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivityPosition:\n      config.devIndicators === false\n        ? undefined\n        : config.devIndicators.position,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    deploymentId: config.deploymentId,\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    const pluginCountBefore = webpackConfig.plugins?.length\n\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack: bundler,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (telemetryPlugin && pluginCountBefore) {\n      const pluginCountAfter = webpackConfig.plugins?.length\n      if (pluginCountAfter) {\n        const pluginsChanged = pluginCountAfter !== pluginCountBefore\n        telemetryPlugin.addUsage('webpackPlugins', pluginsChanged ? 1 : 0)\n      }\n    }\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createVendoredReactAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isBrowser: isClient,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n\nfunction getNextRootParamsRules({\n  isRootParamsEnabled,\n  isClient,\n  appDir,\n  pageExtensions,\n}: {\n  isRootParamsEnabled: boolean\n  isClient: boolean\n  appDir: string | undefined\n  pageExtensions: string[]\n}): webpack.RuleSetRule[] {\n  // Match resolved import of 'next/root-params'\n  const nextRootParamsModule = path.join(NEXT_PROJECT_ROOT, 'root-params.js')\n\n  const createInvalidImportRule = (message: string) => {\n    return {\n      resource: nextRootParamsModule,\n      loader: 'next-invalid-import-error-loader',\n      options: {\n        message,\n      } satisfies InvalidImportLoaderOpts,\n    } satisfies webpack.RuleSetRule\n  }\n\n  // Hard-error if the flag is not enabled, regardless of if we're on the server or on the client.\n  if (!isRootParamsEnabled) {\n    return [\n      createInvalidImportRule(\n        \"'next/root-params' can only be imported when `experimental.rootParams` is enabled.\"\n      ),\n    ]\n  }\n\n  // If there's no app-dir (and thus no layouts), there's no sensible way to use 'next/root-params',\n  // because we wouldn't generate any getters.\n  if (!appDir) {\n    return [\n      createInvalidImportRule(\n        \"'next/root-params' can only be used with the App Directory.\"\n      ),\n    ]\n  }\n\n  // In general, the compiler should prevent importing 'next/root-params' from client modules, but it doesn't catch everything.\n  // If an import slips through our validation, make it error.\n  const invalidClientImportRule = createInvalidImportRule(\n    \"'next/root-params' cannot be imported from a Client Component module. It should only be used from a Server Component.\"\n  )\n\n  // in the browser compilation we can skip the server rules, because we know all imports will be invalid.\n  if (isClient) {\n    return [invalidClientImportRule]\n  }\n\n  return [\n    {\n      oneOf: [\n        {\n          resource: nextRootParamsModule,\n          issuerLayer: shouldUseReactServerCondition as (\n            layer: string\n          ) => boolean,\n          loader: 'next-root-params-loader',\n          options: {\n            appDir,\n            pageExtensions,\n          } satisfies RootParamsLoaderOpts,\n        },\n        // if the rule above didn't match, we're in the SSR layer (or something else that isn't server-only).\n        invalidClientImportRule,\n      ],\n    },\n  ]\n}\n"], "names": ["NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "attachReactRefresh", "babelIncludeRegexes", "getBaseWebpackConfig", "getCacheDirectories", "hasExternalOtelApiPackage", "loadProjectInfo", "nextImageLoaderRegex", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "React", "version", "Error", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "execOnce", "devtool", "console", "warn", "yellow", "bold", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "getReactRefreshLoader", "NEXT_RSPACK", "getRspackReactRefresh", "loader", "resolve", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "alias", "dir", "config", "dev", "jsConfig", "jsConfigPath", "resolvedBaseUrl", "loadJsConfig", "supportedBrowsers", "getSupportedBrowsers", "UNSAFE_CACHE_REGEX", "configs", "Set", "map", "cfg", "cache", "cacheDirectory", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "noMangling", "clientRouterFilters", "fetchCacheKeyPrefix", "isCompileMode", "previewProps", "webpack5Config", "bundler", "getWebpackBundler", "isClient", "COMPILER_NAMES", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isRspack", "Boolean", "FlightClientEntryPlugin", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "RspackFlightClientEntryPlugin", "NextFlightClientEntryPlugin", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "bundledReactChannel", "needsExperimentalReact", "babelConfigFile", "getBabelConfigFile", "hasCustomExportOutput", "distDir", "path", "join", "useSWCLoader", "experimental", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "Log", "info", "relative", "loadBindings", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "excludeCache", "exclude", "excludePath", "cached", "shouldExclude", "test", "isResourceInPackages", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "or", "include", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "getReactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "useBuiltinSwcLoader", "BUILTIN_SWC_LOADER", "options", "isServer", "rootDir", "hasReactRefresh", "swcCacheDir", "serverReferenceHashSalt", "pnp", "versions", "optimizeServerReact", "modularizeImports", "decorators", "compilerOptions", "experimentalDecorators", "emitDecoratorMetadata", "regeneratorRuntimePath", "nextConfig", "swcServerLayerLoader", "serverComponents", "bundleLayer", "WEBPACK_LAYERS", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "apiNode", "pageExtensions", "outputPath", "SERVER_DIRECTORY", "reactServerCondition", "edgeConditionNames", "reactRefreshEntry", "entry", "clientEntries", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "NEXT_PROJECT_ROOT_DIST_CLIENT", "replace", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "resolveConfig", "extensionAlias", "createWebpackAliases", "getMainField", "plugins", "OptionalPeerDependencyResolverPlugin", "tsConfig", "configFile", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "transpilePackagesRegex", "handleExternals", "makeExternalHandler", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "bunExtern<PERSON>", "shouldEnableSlowModuleDetection", "slowModuleDetection", "getParallelism", "override", "Number", "NEXT_WEBPACK_PARALLELISM", "telemetryPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "esmExternals", "parallelism", "externalsPresets", "node", "externals", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "crypto", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "isWebpackDefaultLayer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "RSPACK_DEFAULT_LAYERS_REGEX", "lib", "runtimeChunk", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "minimize", "serverMinification", "minimizer", "getRspackCore", "SwcJsMinimizerRspackPlugin", "minimizerOptions", "compress", "inline", "global_defs", "mangle", "reserved", "disableCharFreq", "MinifyPlugin", "apply", "__NEXT_TEST_MODE", "useLightningcss", "LightningCssMinimizerRspackPlugin", "targets", "CssMinimizerPlugin", "postcssOptions", "annotation", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "createServerOnlyClientOnlyAliases", "not", "message", "shared", "resourceQuery", "WEBPACK_RESOURCE_QUERIES", "metadataRoute", "isWebpackBundledLayer", "createNextApiEsmAliases", "shouldUseReactServerCondition", "createAppRouterApiAliases", "isWebpackClientOnlyLayer", "and", "metadata", "metadataImageMeta", "createVendoredReactAliases", "<PERSON><PERSON><PERSON><PERSON>", "getNextRootParamsRules", "isRootParamsEnabled", "rootParams", "cacheComponents", "edgeSSREntry", "oneOf", "parser", "url", "apiEdge", "instrument", "images", "disableStaticImages", "issuer", "regexLikeCss", "dependency", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NEXT_PROJECT_ROOT", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "MemoryWithGcCachePlugin", "maxGenerations", "injectLoader", "injectEntry", "overlay", "ReactRefreshWebpackPlugin", "webpack", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "DefinePlugin", "getDefineEnv", "isTurbopack", "projectPath", "omitNonDeterministic", "ReactLoadablePlugin", "REACT_LOADABLE_MANIFEST", "runtimeAsset", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "DropClientPage", "TraceEntryPointsPlugin", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "PagesManifestPlugin", "isEdgeRuntime", "MiddlewarePlugin", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "__NEXT_PREVIEW_MODE_ID", "previewModeId", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeSigningKey", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY", "previewModeEncryptionKey", "BuildManifestPlugin", "RspackProfilingPlugin", "Profiling<PERSON><PERSON><PERSON>", "WellKnownErrorsPlugin", "CopyFilePlugin", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "minimized", "AppBuildManifestPlugin", "ClientReferenceManifestPlugin", "experimentalInlineCss", "inlineCss", "NextTypesPlugin", "cacheLifeConfig", "cacheLife", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "cssChunking", "experiments", "CssChunkingPlugin", "strict", "nextjs", "isImplicit", "baseUrl", "unshift", "JsConfigPathsPlugin", "edgeAsset", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivityPosition", "devIndicators", "position", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "maxMemoryGenerations", "Infinity", "compression", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "buildConfiguration", "rootDirectory", "customAppFile", "escapeStringRegexp", "isDevelopment", "targetWeb", "deploymentId", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "pluginCountBefore", "totalPages", "nextRuntime", "pluginCountAfter", "pluginsChanged", "addUsage", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "finalizeEntrypoint", "value", "nextRootParamsModule", "createInvalidImportRule", "invalidClientImportRule"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAuPaA,6BAA6B;eAA7BA;;IAbAC,yBAAyB;eAAzBA;;IAKAC,wBAAwB;eAAxBA;;IAzBAC,oBAAoB;eAApBA;;IA9BGC,kBAAkB;eAAlBA;;IAlEHC,mBAAmB;eAAnBA;;IA8Lb,OAq6EC;eAr6E6BC;;IAfdC,mBAAmB;eAAnBA;;IAXAC,yBAAyB;eAAzBA;;IA3BMC,eAAe;eAAfA;;IAHTC,oBAAoB;eAApBA;;;8DA5PK;kFACoB;4BACT;+DACV;yBACK;6DACP;2BAEY;8BACM;2BACsB;uBAQlD;4BAaA;wBAEkB;yBAEU;6DACd;wBACc;0EAI5B;4EACyB;qCACI;0CACL;4EACC;iCACA;qCACI;uCACE;qBACT;gCACE;sCACe;yCACyB;+CACzB;iCACd;qEAUzB;qBACsB;wCACU;4CACI;wCACJ;yCAEC;oCACL;wCACI;iCAEmB;yBAInD;qDAC8C;uCAO9C;wBAC+B;mCACJ;sCAI3B;8BAIA;2BAC8C;uCACf;0EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9B,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAASC,cAAK,CAACC,OAAO,IAAI,IAAI;IAChC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6D;AACrE;AAEO,MAAMZ,sBAAgC;IAC3C;IACA;IACA;IACA;CACD;AAED,MAAMa,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,OAAwB;IAC3C,OACE,0BAA0B;IAC1BA,QAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuBC,IAAAA,gBAAQ,EACnC,CAACC;IACCC,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EAAC,CAAC,8BAA8B,EAAEJ,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIK,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEF,SAASC;IACP,OAAOzB,QAAQC,GAAG,CAACyB,WAAW,GAC1BC,IAAAA,gCAAqB,IAAGC,MAAM,GAC9BtC,QAAQuC,OAAO,CAACL;AACtB;AAEO,SAAS1C,mBACdgD,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBP;KAC3BK,wBAAAA,cAAcjB,MAAM,sBAApBiB,8BAAAA,sBAAsBG,KAAK,qBAA3BH,4BAA6BI,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASL,cAAc;gBACzBI,KAAKE,GAAG,GAAG;oBAACL;oBAAoBI;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMV,iBACvB,kCAAkC;YAClC,CAACK,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMT,sBAAsBS,MAAMjB,yBAE3C;gBACA,MAAMkB,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMV;gBACxC,iCAAiC;gBACjCI,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGV;YAC1B;QACF;IACF;AACF;AAEO,MAAMnD,uBAAuB;IAClCgE,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB;AAEO,MAAMlF,4BAA4B;IACvC,GAAGE,oBAAoB;IACvBiF,OAAO;AACT;AAEO,MAAMlF,2BAA2B;IACtC,GAAGC,oBAAoB;IACvBiF,OAAO;IACPjB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB;AAEO,MAAMhF,gCAAgC;IAC3C,GAAGE,wBAAwB;IAC3BkF,OAAO;AACT;AAEO,MAAM1E,uBACX;AAEK,eAAeD,gBAAgB,EACpC4E,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAMC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMC,IAAAA,qBAAY,EACpEN,KACAC;IAEF,MAAMM,oBAAoBC,IAAAA,2BAAoB,EAACR,KAAKE;IACpD,OAAO;QACLC;QACAC;QACAC;QACAE;IACF;AACF;AAEO,SAASpF;IACd,IAAI;QACFI,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMkF,qBAAqB;AAEpB,SAASvF,oBACdwF,OAAgC;IAEhC,OAAO,IAAIC,IACTD,QACGE,GAAG,CAAC,CAACC;QACJ,IAAI,OAAOA,IAAIC,KAAK,KAAK,YAAYD,IAAIC,KAAK,CAAC/D,IAAI,KAAK,cAAc;YACpE,OAAO8D,IAAIC,KAAK,CAACC,cAAc;QACjC;QACA,OAAO;IACT,GACCzE,MAAM,CAAC,CAAC0D,MAAQA,OAAO;AAE9B;AAEe,eAAe/E,qBAC5B+E,GAAW,EACX,EACEgB,OAAO,EACPC,aAAa,EACbhB,MAAM,EACNiB,YAAY,EACZhB,MAAM,KAAK,EACXiB,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,UAAU,EACV1B,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfE,iBAAiB,EACjBuB,mBAAmB,EACnBC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,EAiCb;QAsHChC,sBAOIA,uBAwciBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBAgTfA,uBAoCAA,sBA0xBoBA,0BAiEtBA,2BA2CJE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjCpC,gCAAAA,wBAmG0BkC,uBAsBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCXiC,yBAiLcnE,uBAoDZA,wBA0FAA,6BAAAA;IAzwEF,MAAMoE,UAAUC,IAAAA,0BAAiB;IACjC,MAAMC,WAAWnB,iBAAiBoB,0BAAc,CAACC,MAAM;IACvD,MAAMC,eAAetB,iBAAiBoB,0BAAc,CAACG,UAAU;IAC/D,MAAMC,eAAexB,iBAAiBoB,0BAAc,CAACK,MAAM;IAE3D,MAAMC,WAAWC,QAAQ5G,QAAQC,GAAG,CAACyB,WAAW;IAEhD,MAAMmF,0BACJF,YAAY3G,QAAQC,GAAG,CAAC6G,kCAAkC,GACtDC,4DAA6B,GAC7BC,gDAA2B;IAEjC,uFAAuF;IACvF,MAAMC,0BAA0BR,gBAAgBF;IAEhD,MAAMW,cACJ5B,SAAS6B,WAAW,CAACC,MAAM,GAAG,KAC9B9B,SAAS+B,UAAU,CAACD,MAAM,GAAG,KAC7B9B,SAASvC,QAAQ,CAACqE,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAAC5B;IACpB,MAAM6B,0BAA0B;IAChC,MAAMC,sBAAsBC,IAAAA,8CAAsB,EAACzD,UAC/C,kBACA;IAEJ,MAAM0D,kBAAkBC,IAAAA,sCAAkB,EAAC5D;IAE3C,IAAI,CAACE,OAAO2D,IAAAA,6BAAqB,EAAC5D,SAAS;QACzCA,OAAO6D,OAAO,GAAG;IACnB;IACA,MAAMA,UAAUC,aAAI,CAACC,IAAI,CAAChE,KAAKC,OAAO6D,OAAO;IAE7C,IAAIG,eAAe,CAACN,mBAAmB1D,OAAOiE,YAAY,CAACC,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIJ,cAAc;YAEK,4BAAA,6BACnB1I;QAFF,0CAA0C;QAC1C,MAAM+I,gBACJ/I,WAAAA,QAAQ,8BADW,8BAAA,AACnBA,SACCgJ,iBAAiB,sBAFC,6BAAA,iCACnBhJ,8BADmB,2BAEKiJ,MAAM;QAChCJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAAC9G,qBAAqB,CAAC0G,gBAAgBN,iBAAiB;QAC1Dc,KAAIC,IAAI,CACN,CAAC,6EAA6E,EAAEX,aAAI,CAACY,QAAQ,CAC3F3E,KACA2D,iBACA,+CAA+C,CAAC;QAEpDpG,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAACoG,mBAAmBtB,UAAU;QAChC,MAAMuC,IAAAA,iBAAY,EAAC3E,OAAOiE,YAAY,CAACW,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvC7E,CAAAA,OAAO8E,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAACxJ;IAET,KAAK,MAAMyJ,OAAOhF,OAAOiE,YAAY,CAACgB,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAACzH,gCAAgC,CAACyG,gBAAgBhE,OAAOoF,QAAQ,EAAE;QACrEZ,KAAIC,IAAI,CACN;QAEFlH,+BAA+B;IACjC;IAEA,MAAM8H,eAAwC,CAAC;IAC/C,SAASC,QAAQC,WAAmB;QAClC,MAAMC,SAASH,YAAY,CAACE,YAAY;QACxC,IAAIC,WAAWpB,WAAW;YACxB,OAAOoB;QACT;QAEA,MAAMC,gBACJF,YAAYL,QAAQ,CAAC,mBACrB,CAACnK,oBAAoByD,IAAI,CAAC,CAACC,IAAMA,EAAEiH,IAAI,CAACH,iBACxC,CAACI,IAAAA,qCAAoB,EAACJ,aAAaV;QAErCQ,YAAY,CAACE,YAAY,GAAGE;QAC5B,OAAOA;IACT;IAEA,MAAMG,4BACJ5F,OAAOiE,YAAY,CAAC4B,WAAW,IAAI,CAAC,CAAC7F,OAAO8E,iBAAiB;IAC/D,MAAMgB,gBAAgB;QACpBJ,MAAM;YAAEK,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIH,4BAEA,CAAC,IACD;YAAEI,SAAS;gBAACjG;mBAAQhF;aAAoB;QAAC,CAAC;QAC9CuK;IACF;IAEA,MAAMW,cAAcC,IAAAA,oCAAc,EAChClC,cACAN,iBACAT,yBACAY,SACAzC,UACArB,KACC2B,UAAUN,UACXnB,KACAmC,WACApC,uBAAAA,OAAOiE,YAAY,qBAAnBjE,qBAAqBmG,aAAa,EAClCL,cAAcR,OAAO;IAGvB,MAAMc,sBAAsBH,cACxB7B,YACAiC,IAAAA,4CAAsB,GACpBrG,wBAAAA,OAAOiE,YAAY,qBAAnBjE,sBAAqBmG,aAAa,EAClCpG,KACAE,KACAgD,yBACA6C,cAAcR,OAAO;IAG3B,IAAIgB,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElBxG;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQiE,YAAY,qBAApBjE,qBAAsByG,iBAAiB,KACvC,CAACH,8BACD;gBAMC,oCACChL;YANF,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvDgL,+BAA+B;aAE7BhL,WAAAA,QAAQ,8BADT,qCAAA,AACCA,SACCoL,yBAAyB,qBAF3B,wCACCpL,UAEAwI,aAAI,CAACC,IAAI,CAACF,SAAS,CAAC,kBAAkB,EAAE8C,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,MAAMC,sBAAsB7K,QAAQC,GAAG,CAAC6K,kBAAkB;QAC1D,IAAInE,YAAYkE,qBAAqB;gBAwB7B3G,2BAGAA;YA1BN,OAAO;gBACLtC,QAAQ;gBACRmJ,SAAS;oBACPC,UAAU/D;oBACVgE,SAASlH;oBACTqB;oBACAM;oBACAwF,iBAAiBjH,OAAOmC;oBACxB0C,mBAAmBD;oBACnBvE;oBACA6G,aAAarD,aAAI,CAACC,IAAI,CACpBhE,KACAC,CAAAA,0BAAAA,OAAQ6D,OAAO,KAAI,SACnB,SACA;oBAEFuD,yBAAyBpG;oBAEzB,0BAA0B;oBAC1BqG,KAAKzE,QAAQ5G,QAAQsL,QAAQ,CAACD,GAAG;oBACjCE,qBAAqB3E,QAAQ5C,OAAOiE,YAAY,CAACsD,mBAAmB;oBACpEC,mBAAmBxH,OAAOwH,iBAAiB;oBAC3CC,YAAY7E,QACV1C,6BAAAA,4BAAAA,SAAUwH,eAAe,qBAAzBxH,0BAA2ByH,sBAAsB;oBAEnDC,uBAAuBhF,QACrB1C,6BAAAA,6BAAAA,SAAUwH,eAAe,qBAAzBxH,2BAA2B0H,qBAAqB;oBAElDC,wBAAwBvM,QAAQuC,OAAO,CACrC;oBAGF,GAAG2I,YAAY;gBACjB;YACF;QACF;QAEA,OAAO;YACL5I,QAAQ;YACRmJ,SAAS;gBACPC,UAAU/D;gBACVhC;gBACAgG,SAASlH;gBACTqB;gBACAM;gBACAwF,iBAAiBjH,OAAOmC;gBACxB0F,YAAY9H;gBACZE;gBACA4E,mBAAmBD;gBACnBvE;gBACA6G,aAAarD,aAAI,CAACC,IAAI,CAAChE,KAAKC,CAAAA,0BAAAA,OAAQ6D,OAAO,KAAI,SAAS,SAAS;gBACjEuD,yBAAyBpG;gBACzB,GAAGwF,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMuB,uBAAuBxB,aAAa;QACxCyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACC,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoB9B,aAAa;QACrCyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACI,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwBhC,aAAa;QACzCyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACM,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBlC,aAAa;QACpCyB,kBAAkB;QAClBI,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAO3E,eAAeyE,mBAAmBxC;IAC3C;IAEA,MAAM2C,wBAAwBtF,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/CyE;QACA9B;QACAG;KACD,CAAC/J,MAAM,CAACuG,WACT,EAAE;IAEN,MAAMiG,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cd;QACA9B;KACD,CAAC5J,MAAM,CAACuG;IAET,MAAMkG,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/CvC,aAAa;YACXyB,kBAAkB;YAClBC,aAAaC,yBAAc,CAACa,UAAU;QACxC;QACA9C;KACD,CAAC5J,MAAM,CAACuG;IAET,MAAMoG,sBAAsB/I,OAAOmC,WAAW;QAAC3E;KAAwB,GAAG,EAAE;IAE5E,2CAA2C;IAC3C,MAAMwL,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvBpL,QAAQ;YACV;eACI0F,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/C4F,iBAAiBX,wBAAwBF;gBACzCpC;gBACAG;aACD,CAAC/J,MAAM,CAACuG,WACT,EAAE;SACP;IAED,MAAMwG,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwBtF,eAC1BuC,aAAa;QACXyB,kBAAkB;QAClBC,aAAaC,yBAAc,CAACqB,OAAO;IACrC,KACAb,eAAeC,KAAK;IAExB,MAAMa,iBAAiBxJ,OAAOwJ,cAAc;IAE5C,MAAMC,aAAaxG,0BACfa,aAAI,CAACC,IAAI,CAACF,SAAS6F,4BAAgB,IACnC7F;IAEJ,MAAM8F,uBAAuB;QAC3B;WACIpH,eAAeqH,2BAAkB,GAAG,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAMC,oBAAoBlH,WACtBhF,IAAAA,gCAAqB,IAAGmM,KAAK,GAC7BxO,QAAQuC,OAAO,CACb,CAAC,yDAAyD,CAAC;IAGjE,MAAMkM,gBAAgB3H,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAInC,MACA;YACE,CAAC+J,qDAAyC,CAAC,EAAEH;YAC7C,CAACI,2CAA+B,CAAC,EAC/B,CAAC,EAAE,CAAC,GACJnG,aAAI,CACDY,QAAQ,CACP3E,KACA+D,aAAI,CAACC,IAAI,CAACmG,2CAA6B,EAAE,OAAO,YAEjDC,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAACC,4CAAgC,CAAC,EAChC,CAAC,EAAE,CAAC,GACJtG,aAAI,CACDY,QAAQ,CACP3E,KACA+D,aAAI,CAACC,IAAI,CACPmG,2CAA6B,EAC7BjK,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzBkK,OAAO,CAAC,OAAO;QACpB,GAAI7G,YACA;YACE,CAAC+G,gDAAoC,CAAC,EAAEpK,MACpC;gBACE4J;gBACA,CAAC,EAAE,CAAC,GACF/F,aAAI,CACDY,QAAQ,CACP3E,KACA+D,aAAI,CAACC,IAAI,CACPmG,2CAA6B,EAC7B,oBAGHC,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACFrG,aAAI,CACDY,QAAQ,CACP3E,KACA+D,aAAI,CAACC,IAAI,CACPmG,2CAA6B,EAC7B,gBAGHC,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACA/F;IAEJ,MAAMkG,gBAAkD;QACtD,yCAAyC;QACzClL,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpEmL,gBAAgBvK,OAAOiE,YAAY,CAACsG,cAAc;QAClDzL,SAAS;YACP;eACG/C;SACJ;QACD+D,OAAO0K,IAAAA,2CAAoB,EAAC;YAC1B3G;YACAzB;YACAG;YACAtC;YACAD;YACAoB;YACAM;YACA3B;YACAsB;QACF;QACA,GAAIe,WACA;YACErD,UAAU;gBACR/C,SAASV,QAAQuC,OAAO,CAAC;YAC3B;QACF,IACAuG,SAAS;QACb,oFAAoF;QACpF7E,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;QACvC,GAAIsB,gBAAgB;YAClBrD,gBAAgB0K,2BAAkB;QACpC,CAAC;QACDc,SAAS;YACPjI,eAAe,IAAIkI,yEAAoC,KAAKvG;SAC7D,CAAC/H,MAAM,CAACuG;QACT,GAAKD,YAAYxC,eACb;YACE,6CAA6C;YAC7CN,cAAc;gBAAC;aAAqB;YACpC+K,UAAU;gBACRC,YAAY1K;YACd;QACF,IACA,CAAC,CAAC;IACR;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAM2K,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAItK;IACrC,iDAAiD;IACjD,MAAMuK,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIJ,yBAAyBK,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAF,yBAAyBM,GAAG,CAACJ;YAE7B,MAAMK,kBAAkBjQ,QAAQuC,OAAO,CAAC,GAAGqN,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAY1H,aAAI,CAACC,IAAI,CAACwH,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMlG,QAAQ,CAACsG,YAAY;YAC/BJ,MAAMjG,IAAI,CAACqG;YACX,MAAMC,eAAenQ,QAAQiQ,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQlP,OAAOmP,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACI5H,YACA;YACE,CAAC,wBAAwB,EAAEE,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACDyH,eAAeC,aAAanL,KAAKgL;IACnC;IACAE,eAAe,QAAQlL,KAAK+K;IAE5B,MAAMe,cAAc7L,OAAO6L,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAI7L,OAAO8L,sBAAsB,IAAIjH,wBAAwB;QAC3D,MAAMkH,2BAA2BlH,uBAAuBxI,MAAM,CAAC,CAAC2I;gBAC9DhF;oBAAAA,iCAAAA,OAAO8L,sBAAsB,qBAA7B9L,+BAA+BkF,QAAQ,CAACF;;QAE1C,IAAI+G,yBAAyB3I,MAAM,GAAG,GAAG;YACvC,MAAM,qBAIL,CAJK,IAAIzH,MACR,CAAC,8FAA8F,EAAEoQ,yBAAyBhI,IAAI,CAC5H,OACC,GAHC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAMiI,yBAAyB3Q,kBAAkB0J,MAAM,IACjD/E,OAAO8L,sBAAsB,IAAI,EAAE,EACvCzP,MAAM,CAAC,CAAC2I,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAMiH,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3BrL,GAAG,CAAC,CAACrE,IAAMA,EAAE6N,OAAO,CAAC,OAAO,YAC5BpG,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMoI,yBAAyB,IAAID,OACjC,CAAC,2BAA2B,EAAErH,0CAAAA,uBAC1BlE,GAAG,CAAC,CAACrE,IAAMA,EAAE6N,OAAO,CAAC,OAAO,YAC7BpG,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMqI,kBAAkBC,IAAAA,oCAAmB,EAAC;QAC1CrM;QACAiM;QACAK,oBAAoBzH;QACpB9E;IACF;IAEA,MAAMwM,sBAAsB,IAAIL,OAAO,CAAC,IAAI,EAAE1C,eAAezF,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAMyI,yBAAyB;QAAC1G,cAAcJ,IAAI;QAAE6G;KAAoB;IAExE,MAAME,iBAAiB,AAACnR,QAAQ,UAC7BmR,cAAc;IAEjB,MAAMC,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAMC,kCACJ,CAAC,CAAC3M,OAAOiE,YAAY,CAAC2I,mBAAmB,IAAI3M;IAE/C,MAAM4M,iBAAiB;QACrB,MAAMC,WAAWC,OAAO/Q,QAAQC,GAAG,CAAC+Q,wBAAwB;QAC5D,IAAIL,iCAAiC;YACnC,IAAIG,UAAU;gBACZ5P,QAAQC,IAAI,CACV;YAEJ;YACA,OAAO;QACT;QACA,OAAO2P,YAAY1I;IACrB;IAEA,MAAM6I,kBACJ,CAAChN,OACDmC,YACA,IAAI,AACF9G,CAAAA,QAAQ,sDAAqD,EAC7D4R,eAAe,CACf,IAAIC,IACF;QACE;YAAC;YAAanJ;SAAa;QAC3B;YAAC;YAAY,CAAC,GAAChE,mBAAAA,OAAOoF,QAAQ,qBAAfpF,iBAAiBoN,KAAK;SAAC;QACtC;YAAC;YAAuB,CAAC,GAACpN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBqN,gBAAgB;SAAC;QAC5D;YACE;YACA,CAAC,GAACrN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBsN,qBAAqB;SACzC;QACD;YACE;YACA,CAAC,EAACpN,6BAAAA,4BAAAA,SAAUwH,eAAe,qBAAzBxH,0BAA2ByH,sBAAsB;SACpD;QACD;YAAC;YAAoB,CAAC,GAAC3H,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBuN,aAAa;SAAC;QACtD;YAAC;YAAmB,CAAC,EAACrN,6BAAAA,6BAAAA,SAAUwH,eAAe,qBAAzBxH,2BAA2BsN,eAAe;SAAC;QACjE;YAAC;YAAc,CAAC,GAACxN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiByN,OAAO;SAAC;QAC1C;YAAC;YAAqB,CAAC,CAACzN,OAAO8E,iBAAiB;SAAC;QACjD;YAAC;YAA8B,CAAC,CAAC9E,OAAO0N,0BAA0B;SAAC;QACnE;YAAC;YAA6B,CAAC,CAAC1N,OAAO2N,yBAAyB;SAAC;QACjE;YAAC;YAAqB,CAAC,CAAC3N,OAAOwH,iBAAiB;SAAC;QACjD,+EAA+E;QAC/E;YAAC;YAAgBxH,OAAOiE,YAAY,CAAC2J,YAAY,KAAK;SAAK;QAC3DzJ;KACD,CAAC9H,MAAM,CAAqBuG;IAInC,IAAI9E,gBAAuC;QACzC+P,aAAahB;QACb,GAAIpK,eAAe;YAAEqL,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACE5L,YAAYG,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACA0L,IAAAA,0CAAwB;gBACxBC,qDAAmC;aACpC,GACD,EAAE;SACP,GACD;eACKzB;eACAC;YACH,CAAC,EACCyB,OAAO,EACPC,OAAO,EACPvP,cAAc,EACdwP,WAAW,EACXC,UAAU,EAqBX,GACClC,gBACE+B,SACAC,SACAvP,gBACAwP,YAAYE,WAAW,EACvB,CAACxH;oBACC,MAAMyH,kBAAkBF,WAAWvH;oBACnC,OAAO,CAAC0H,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAAC9Q,SAAS+Q;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAOjR,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAMmR,QAAQ,SAAStJ,IAAI,CAACoJ,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkCjS,IAAI,MACtC,WACA,UAAU4I,IAAI,CAACoJ;gCACnBjR,QAAQ;oCAACiR;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAAClP;YACfmP,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAIrP,KAAK;oBACP,IAAIwC,cAAc;wBAChB;;;;;YAKA,GACA,MAAM8M,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBpK,MAAM;oCACNqK,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBxE,MAAM,CAAC7O;wCACL,MAAMsT,WAAWtT,QAAOuT,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAOC,eAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACN;4CAC9CG,KAAKG,MAAM,CAACN;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKI,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAInO,gBAAgBF,cAAc;oBAChC,OAAO;wBACLsO,UAAU,GAAGtO,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1DsN,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMc,sBAAsB;oBAC1BjB,QAAQ;oBACRnE,MAAM;oBACN,6DAA6D;oBAC7DqF,OAAOC,4BAAqB;oBAC5BtL,MAAK7I,OAAW;wBACd,MAAMoU,WAAWpU,QAAOuT,gBAAgB,oBAAvBvT,QAAOuT,gBAAgB,MAAvBvT;wBACjB,OAAOoU,WACHlG,uBAAuBvM,IAAI,CAAC,CAAC0S,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpB5L,MAAK7I,OAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,QAAOC,IAAI,qBAAXD,aAAasU,UAAU,CAAC,WACzBtU,QAAO0U,IAAI,KAAK,UAChB,oBAAoB7L,IAAI,CAAC7I,QAAOuT,gBAAgB,MAAM;oBAE1D;oBACA1E,MAAK7O,OAKJ;wBACC,MAAMyT,OAAOC,eAAM,CAACC,UAAU,CAAC;wBAC/B,IAAI5T,YAAYC,UAAS;4BACvBA,QAAO2U,UAAU,CAAClB;wBACpB,OAAO;4BACL,IAAI,CAACzT,QAAO4U,QAAQ,EAAE;gCACpB,MAAM,qBAEL,CAFK,IAAI9V,MACR,CAAC,iCAAiC,EAAEkB,QAAOC,IAAI,CAAC,uBAAuB,CAAC,GADpE,qBAAA;2CAAA;gDAAA;kDAAA;gCAEN;4BACF;4BACAwT,KAAKG,MAAM,CAAC5T,QAAO4U,QAAQ,CAAC;gCAAEtD,SAASpO;4BAAI;wBAC7C;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAIlD,QAAOkU,KAAK,EAAE;4BAChBT,KAAKG,MAAM,CAAC5T,QAAOkU,KAAK;wBAC1B;wBAEA,OAAOT,KAAKI,MAAM,CAAC,OAAOgB,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVpB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQlN,WAEJ,YAAY;oBACZ,mCACA,CAACgP,QACC,CAAC,iCAAiCjM,IAAI,CAACiM,MAAMjG,IAAI;oBAEvDiE,aAAahN,WACT;wBACEiP,WAAW;4BACT/B,QAAQ;4BACRnE,MAAM;4BACNqF,OAAOc,kCAA2B;4BAClCnM,MAAM;4BACN0L,UAAU;4BACVC,SAAS;wBACX;wBACAS,KAAK;4BACHpM,MAAM;4BACNgG,MAAM;4BACNmE,QAAQ;4BACRuB,UAAU;4BACVpB,WAAW;4BACXF,oBAAoB;wBACtB;oBACF,IACA;wBACE8B,WAAWd;wBACXgB,KAAKR;oBACP;oBACJpB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACAgC,cAAc3P,WACV;gBAAEsJ,MAAMsG,+CAAmC;YAAC,IAC5C5N;YAEJ6N,UACE,CAAChS,OACAmC,CAAAA,YACCG,gBACCE,gBAAgBzC,OAAOiE,YAAY,CAACiO,kBAAkB;YAC3DC,WAAW;gBACT,oBAAoB;gBACpBxP,WACI,IAAKyP,CAAAA,IAAAA,wBAAa,GAAC,EAAEC,0BAA0B,CAAE;oBAC/C,6BAA6B;oBAC7B,iEAAiE;oBACjEC,kBAAkB;wBAChBC,UAAU;4BACRC,QAAQ;4BACRC,aAAa;gCACX,mDAAmD;4BACrD;wBACF;wBACAC,QAAQ,CAAC9Q,cAAc;4BACrB+Q,UAAU;gCAAC;6BAAc;4BACzBC,iBAAiB,CAACxQ;wBACpB;oBACF;gBACF,KACA,CAACgD;oBACC,4BAA4B;oBAC5B,MAAM,EAAEyN,YAAY,EAAE,GACpBvX,QAAQ;oBACV,IAAIuX,aAAa;wBACfjR;wBACAgR,iBAAiB,CAACxQ;oBACpB,GAAG0Q,KAAK,CAAC1N;gBACX;gBACJ,aAAa;gBACb,6DAA6D;gBAC7D,iEAAiE;gBACjEzC,YACC3G,CAAAA,QAAQC,GAAG,CAAC8W,gBAAgB,GACzB/S,OAAOiE,YAAY,CAAC+O,eAAe,GACnChT,EAAAA,wBAAAA,OAAOiE,YAAY,qBAAnBjE,sBAAqBgT,eAAe,MAAK5O,aACzCpE,OAAOiE,YAAY,CAAC+O,eAAe,AAAD,IAClC,IAAKZ,CAAAA,IAAAA,wBAAa,GAAC,EAAEa,iCAAiC,CAAE;oBACtD,8BAA8B;oBAC9BX,kBAAkB;wBAChBY,SAAS5S;oBACX;gBACF,KACA,CAAC8E;oBACC,MAAM,EAAE+N,kBAAkB,EAAE,GAC1B7X,QAAQ;oBACV,IAAI6X,mBAAmB;wBACrBC,gBAAgB;4BACdzS,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/C6R,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5Da,YAAY;4BACd;wBACF;oBACF,GAAGP,KAAK,CAAC1N;gBACX;aACL;QACH;QACA+I,SAASpO;QACT,8CAA8C;QAC9C+J,OAAO;YACL,OAAO;gBACL,GAAIC,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAG7I,WAAW;YAChB;QACF;QACAoS,cAAc9W,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnBgX,IAAI,GAAEvT,uBAAAA,OAAOsT,YAAY,qBAAnBtT,qBAAqBwT,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACV1T,OAAO2T,WAAW,GACd3T,OAAO2T,WAAW,CAACC,QAAQ,CAAC,OAC1B5T,OAAO2T,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7B7T,OAAO2T,WAAW,GACpB,GACL,OAAO,CAAC;YACT7P,MAAM,CAAC7D,OAAOwC,eAAeqB,aAAI,CAACC,IAAI,CAAC0F,YAAY,YAAYA;YAC/D,oCAAoC;YACpCoH,UAAU5N,0BACNhD,OAAOsC,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAEpB,gBAAgB,cAAc,GAAG,MAAM,EACtDlB,MAAM,KAAK,iBACZ,GAAG,CAAC;YACT6T,SAAS1R,YAAYG,eAAe,SAAS6B;YAC7C2P,eAAe3R,YAAYG,eAAe,WAAW;YACrDyR,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAejR,0BACX,cACA,CAAC,cAAc,EAAE9B,gBAAgB,cAAc,KAC7ClB,MAAM,WAAW,uBAClB,GAAG,CAAC;YACTkU,+BAA+B;YAC/BC,oBAAoBvI;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3FwI,+BAA+BpU,MAC3B,6BACAmE;YACJkQ,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACb5W,SAASyM;QACToK,eAAe;YACb,+BAA+B;YAC/B5U,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAAC6U,MAAM,CACN,CAAC7U,OAAOlC;gBACN,4DAA4D;gBAC5DkC,KAAK,CAAClC,OAAO,GAAGkG,aAAI,CAACC,IAAI,CAAC6Q,WAAW,WAAW,WAAWhX;gBAE3D,OAAOkC;YACT,GACA,CAAC;YAEHhB,SAAS;gBACP;mBACG/C;aACJ;YACD2O,SAAS,EAAE;QACb;QACA7N,QAAQ;YACNoB,OAAO;gBACL,+EAA+E;gBAC/E;oBACEsQ,aAAa;wBACXxI,IAAI;+BACCmC,yBAAc,CAAC2M,KAAK,CAACC,UAAU;+BAC/B5M,yBAAc,CAAC2M,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAlX,SAAS;wBACP,6CAA6C;wBAC7CiC,OAAOkV,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA;oBACEzG,aAAa;wBACX0G,KAAK;+BACA/M,yBAAc,CAAC2M,KAAK,CAACC,UAAU;+BAC/B5M,yBAAc,CAAC2M,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAlX,SAAS;wBACP,6CAA6C;wBAC7CiC,OAAOkV,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACEtP,MAAM;wBACJ;wBACA;qBACD;oBACD9H,QAAQ;oBACR2Q,aAAa;wBACXxI,IAAImC,yBAAc,CAAC2M,KAAK,CAACC,UAAU;oBACrC;oBACA/N,SAAS;wBACPmO,SACE;oBACJ;gBACF;gBACA;oBACExP,MAAM;wBACJ;wBACA;qBACD;oBACD9H,QAAQ;oBACR2Q,aAAa;wBACX0G,KAAK;+BACA/M,yBAAc,CAAC2M,KAAK,CAACC,UAAU;+BAC/B5M,yBAAc,CAAC2M,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAhO,SAAS;wBACPmO,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACExP,MAAM;wBACJ;wBACA;qBACD;oBACD9H,QAAQ;oBACR2Q,aAAa;wBACXxI,IAAImC,yBAAc,CAAC2M,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACItS,eACA,EAAE,GACF;oBACE;wBACEiD,MAAM;wBACN9H,QAAQ;oBACV;iBACD;mBACD0F,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACVyN,OAAO7I,yBAAc,CAACiN,MAAM;wBAC5BzP,MAAM5J;oBACR;oBACA,4CAA4C;oBAC5C;wBACEsZ,eAAe,IAAIlJ,OACjBmJ,mCAAwB,CAACC,aAAa;wBAExCvE,OAAO7I,yBAAc,CAACC,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C4I,OAAO7I,yBAAc,CAACI,mBAAmB;wBACzC5C,MAAM;oBACR;oBACA;wBACE6I,aAAagH,4BAAqB;wBAClC1X,SAAS;4BACPiC,OAAO0V,IAAAA,8CAAuB;wBAChC;oBACF;oBACA;wBACEjH,aAAakH,oCAA6B;wBAC1C5X,SAAS;4BACPiC,OAAO4V,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;oBACA;wBACEnH,aAAaoH,+BAAwB;wBACrC9X,SAAS;4BACPiC,OAAO4V,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;iBACD,GACD,EAAE;mBACFpS,aAAa,CAAClB,WACd;oBACE;wBACEmM,aAAakH,oCAA6B;wBAC1C/P,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzBkQ,KAAK;gCACHpJ;gCACA;oCACEyI,KAAK;wCAAChJ;wCAA4BnQ;qCAAmB;gCACvD;6BACD;wBACH;wBACAsZ,eAAe;4BACb,8DAA8D;4BAC9D,8DAA8D;4BAC9D,6DAA6D;4BAC7D,8DAA8D;4BAC9D,WAAW;4BACXH,KAAK;gCACH,IAAI/I,OAAOmJ,mCAAwB,CAACQ,QAAQ;gCAC5C,IAAI3J,OAAOmJ,mCAAwB,CAACS,iBAAiB;6BACtD;wBACH;wBACAjY,SAAS;4BACP0B,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;4BACvC/B,gBAAgByK;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9B7J,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;gCACrD,iCAAiC;gCACjCnC;gCACA0P,OAAO7I,yBAAc,CAACC,qBAAqB;gCAC3C6N,WAAW5T;gCACXG;4BACF;wBACF;wBACAlE,KAAK;oBACP;iBACD,GACD,EAAE;mBAEH4X,uBAAuB;oBACxBC,qBACElW,OAAOiE,YAAY,CAACkS,UAAU,IAC9B,8DAA8D;oBAC9DnW,OAAOiE,YAAY,CAACmS,eAAe,IACnC;oBACFhU;oBACAV;oBACA8H;gBACF;gBAEA,kDAAkD;gBAClD,yDAAyD;mBACrD,CAACxJ,OAAOiE,YAAY,CAACvE,cAAc,GACnC;oBACE;wBACEgG,MAAM;wBACN7H,SAAS;4BACP6B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACF4D,aAAaf,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACE6S,eAAe,IAAIlJ,OACjBmJ,mCAAwB,CAACgB,YAAY;wBAEvCtF,OAAO7I,yBAAc,CAACC,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACF7E,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClEgT,OAAO;4BACL;gCACE/H,aAAakH,oCAA6B;gCAC1C/P,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzBkQ,KAAK;wCACHpJ;wCACA;4CACEyI,KAAK;gDAAChJ;gDAA4BnQ;6CAAmB;wCACvD;qCACD;gCACH;gCACA+B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5DiC,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;wCACrDnC;wCACA0P,OAAO7I,yBAAc,CAACC,qBAAqB;wCAC3C6N,WAAW5T;wCACXG;oCACF;gCACF;4BACF;4BACA;gCACEmD,MAAM8G;gCACN+B,aAAarG,yBAAc,CAACI,mBAAmB;gCAC/CzK,SAAS;oCACPiC,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;wCACrDnC;wCACA0P,OAAO7I,yBAAc,CAACI,mBAAmB;wCACzC0N,WAAW5T;wCACXG;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACEmD,MAAM8G;wBACN+B,aAAarG,yBAAc,CAACM,eAAe;wBAC3C3K,SAAS;4BACPiC,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;gCACrDnC;gCACA0P,OAAO7I,yBAAc,CAACM,eAAe;gCACrCwN,WAAW5T;gCACXG;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7Ee,aAAarD,OAAOmC,WACpB;oBACE;wBACEsD,MAAMI,cAAcJ,IAAI;wBACxBJ,SAAS;4BACP,+CAA+C;4BAC/CQ,cAAcR,OAAO;4BACrB6G;4BACAtQ;yBACD;wBACD0S,aAAarG,yBAAc,CAACM,eAAe;wBAC3CnK,KAAK2K;wBACLnL,SAAS;4BACP0B,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACEqV,OAAO;wBACL;4BACE,GAAGxQ,aAAa;4BAChByI,aAAarG,yBAAc,CAACqB,OAAO;4BACnClL,KAAKiL;4BACL,kDAAkD;4BAClD,8DAA8D;4BAC9DiN,QAAQ;gCACNC,KAAK;4BACP;wBACF;wBACA;4BACE,GAAG1Q,aAAa;4BAChByI,aAAarG,yBAAc,CAACuO,OAAO;4BACnCpY,KAAKiL;wBAGP;wBACA;4BACE5D,MAAMI,cAAcJ,IAAI;4BACxB6I,aAAarG,yBAAc,CAACa,UAAU;4BACtC1K,KAAKyK;4BACLjL,SAAS;gCACP0B,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;gCACvC/B,gBAAgByK;gCAChB7J,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;oCACrDnC;oCACA0P,OAAO7I,yBAAc,CAACa,UAAU;oCAChCiN,WAAW5T;oCACXG;gCACF;4BACF;wBACF;wBACA;4BACEmD,MAAMI,cAAcJ,IAAI;4BACxB6I,aAAarG,yBAAc,CAACwO,UAAU;4BACtCrY,KAAKwK;4BACLhL,SAAS;gCACP0B,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;gCACvC/B,gBAAgByK;gCAChB7J,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;oCACrDnC;oCACA0P,OAAO7I,yBAAc,CAACwO,UAAU;oCAChCV,WAAW5T;oCACXG;gCACF;4BACF;wBACF;2BACIe,YACA;4BACE;gCACEoC,MAAMI,cAAcJ,IAAI;gCACxB6I,aAAakH,oCAA6B;gCAC1CnQ,SAASxJ;gCACTuC,KAAKuK;4BACP;4BACA;gCACElD,MAAMI,cAAcJ,IAAI;gCACxB0P,eAAe,IAAIlJ,OACjBmJ,mCAAwB,CAACgB,YAAY;gCAEvChY,KAAKuK;4BACP;4BACA;gCACElD,MAAMI,cAAcJ,IAAI;gCACxB6I,aAAarG,yBAAc,CAACM,eAAe;gCAC3C,uEAAuE;gCACvElD,SAAS1J;gCACTyC,KAAK+K;gCACLvL,SAAS;oCACP0B,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;gCACzC;4BACF;4BACA;gCACEyE,MAAMI,cAAcJ,IAAI;gCACxB6I,aAAarG,yBAAc,CAACI,mBAAmB;gCAC/ChD,SAASxJ;gCACTuC,KAAKgL;gCACLxL,SAAS;oCACP0B,YAAYkL,IAAAA,qBAAY,EAACxJ,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAG6E,aAAa;4BAChBzH,KAAK;mCACA2K;gCACHN,eAAeC,KAAK;gCACpBvC;6BACD,CAAC/J,MAAM,CAACuG;wBACX;qBACD;gBACH;mBAEI,CAAC5C,OAAO2W,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACElR,MAAMtK;wBACNwC,QAAQ;wBACRiZ,QAAQ;4BAAE5B,KAAK6B,iBAAY;wBAAC;wBAC5BC,YAAY;4BAAE9B,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAI/I,OAAOmJ,mCAAwB,CAACQ,QAAQ;gCAC5C,IAAI3J,OAAOmJ,mCAAwB,CAACC,aAAa;gCACjD,IAAIpJ,OAAOmJ,mCAAwB,CAACS,iBAAiB;6BACtD;wBACH;wBACA/O,SAAS;4BACPiQ,OAAO/W;4BACPgB;4BACAgW,UAAUjX,OAAOiX,QAAQ;4BACzBtD,aAAa3T,OAAO2T,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACFpR,eACA;oBACE;wBACE1E,SAAS;4BACPkB,UAAU;gCACR/C,SAASV,QAAQuC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACDuE,WACE;oBACE;wBACEvE,SAAS;4BACPkB,UACEiB,OAAOiE,YAAY,CAACiT,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACX9G,QAAQ;gCACR+G,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJ3T,MAAM;gCACN4T,UAAU;gCACV1b,SAAS;gCACT2b,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQ7b,QAAQuC,OAAO,CACrB;gCAEFuZ,QAAQ9b,QAAQuC,OAAO,CACrB;gCAEFwZ,WAAW/b,QAAQuC,OAAO,CACxB;gCAEF0S,QAAQjV,QAAQuC,OAAO,CACrB;gCAEFyZ,QAAQhc,QAAQuC,OAAO,CACrB;gCAEF0Z,MAAMjc,QAAQuC,OAAO,CACnB;gCAEF2Z,OAAOlc,QAAQuC,OAAO,CACpB;gCAEF4Z,IAAInc,QAAQuC,OAAO,CACjB;gCAEFiG,MAAMxI,QAAQuC,OAAO,CACnB;gCAEF6Z,UAAUpc,QAAQuC,OAAO,CACvB;gCAEF7B,SAASV,QAAQuC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5B8Z,aAAarc,QAAQuC,OAAO,CAC1B;gCAEF+Z,QAAQtc,QAAQuC,OAAO,CACrB;gCAEFga,gBAAgBvc,QAAQuC,OAAO,CAC7B;gCAEFia,KAAKxc,QAAQuC,OAAO,CAAC;gCACrBka,QAAQzc,QAAQuC,OAAO,CACrB;gCAEFma,KAAK1c,QAAQuC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/Boa,MAAM3c,QAAQuC,OAAO,CAAC;gCACtBqa,IAAI5c,QAAQuC,OAAO,CACjB;gCAEFsa,MAAM7c,QAAQuC,OAAO,CACnB;gCAEFua,QAAQ9c,QAAQuC,OAAO,CACrB;gCAEFwa,cAAc/c,QAAQuC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7B6H,MAAM;oBACN4S,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACE5S,MAAM;oBACN4S,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5D5S,MAAM;oBACNrH,KAAK,CAAC,EAAE+W,aAAa,EAA6B;4BAE9CA;wBADF,MAAMmD,QAAQ,AACZnD,CAAAA,EAAAA,uBAAAA,cAAc1F,KAAK,CAAC,uCAApB0F,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChDjZ,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEyB,QAAQ;gCACRmJ,SAAS;oCACPwR;oCACApR,aAAarD,aAAI,CAACC,IAAI,CACpBhE,KACAC,CAAAA,0BAAAA,OAAQ6D,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChB2U,OAAO,wBAAwBpD;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACEvX,SAAS;wBACPiC,OAAO;4BACL2Y,MAAMC,+BAAiB;wBACzB;oBACF;gBACF;aACD;QACH;QACAhO,SAAS;YACPjI,gBACE,IAAIP,QAAQyW,6BAA6B,CACvC,6BACA,SAAU1H,QAAQ;gBAChB,MAAM2H,aAAa9U,aAAI,CAAC+U,QAAQ,CAC9B5H,SAAS7C,OAAO,EAChB;gBAEF,MAAM2C,QAAQE,SAAS5C,WAAW,CAACE,WAAW;gBAC9C,IAAIuK;gBAEJ,OAAQ/H;oBACN,KAAK7I,yBAAc,CAACI,mBAAmB;oBACvC,KAAKJ,yBAAc,CAACC,qBAAqB;oBACzC,KAAKD,yBAAc,CAACM,eAAe;oBACnC,KAAKN,yBAAc,CAAC6Q,aAAa;wBAC/BD,UAAU;wBACV;oBACF,KAAK;oBACL,KAAK1U;oBACL;wBACE0U,UAAU;gBACd;gBACA7H,SAAS7C,OAAO,GAAG,CAAC,+BAA+B,EAAE0K,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJ3Y,OAAO,IAAI+Y,gDAAuB,CAAC;gBAAEC,gBAAgB;YAAE;YACvDhZ,OACEmC,YACCO,CAAAA,WAEG,IAAKhF,CAAAA,IAAAA,gCAAqB,GAAC,EAAU;gBACnCub,cAAc;gBACdC,aAAa;gBACbC,SAAS;YACX,KACA,IAAIC,kCAAyB,CAACC,gBAAO,CAAA;YAC3C,6GAA6G;YAC5GlX,CAAAA,YAAYG,YAAW,KACtB,IAAIL,QAAQqX,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAACle,QAAQuC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAIuE,YAAY;oBAAEpG,SAAS;wBAACV,QAAQuC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YAEF,IAAKsE,CAAAA,IAAAA,0BAAiB,GAAC,EAAEsX,YAAY,CACnCC,IAAAA,uBAAY,EAAC;gBACXC,aAAa;gBACb3Z;gBACAC;gBACA4D;gBACA+V,aAAa7Z;gBACb+B;gBACAoB;gBACAd;gBACAG;gBACAE;gBACAd;gBACAkY,sBAAsB9X;gBACtBT;YACF;YAEFc,YACE,IAAI0X,wCAAmB,CAAC;gBACtBjJ,UAAUkJ,mCAAuB;gBACjC3Y;gBACAM;gBACAsY,cAAc,CAAC,OAAO,EAAEC,8CAAkC,CAAC,GAAG,CAAC;gBAC/Dha;YACF;YACF,oDAAoD;YACpD,CAAC0C,YAAaP,CAAAA,YAAYG,YAAW,KAAM,IAAI2X,wCAAc;YAC7DzX,gBACE,CAACxC,OACD,IAAK,AACH3E,CAAAA,QAAQ,kDAAiD,EAExD6e,sBAAsB,CACvB;gBACElT,SAASlH;gBACT2B,QAAQA;gBACRN,UAAUA;gBACVwM,cAAc5N,OAAOiE,YAAY,CAAC2J,YAAY;gBAC9CwM,uBAAuBpa,OAAOoa,qBAAqB;gBACnDC,eAAe/W;gBACfgX,cAAc,EAAE;gBAChBrZ;YACF;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClEjB,OAAOua,2BAA2B,IAChC,IAAIrY,QAAQsY,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACEza,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAE0a,6BAA6B,EAAE,GACrCrf,QAAQ;gBACV,MAAMsf,aAAoB;oBACxB,IAAID,8BAA8B;wBAChC3S,kBAAkB1E;oBACpB;iBACD;gBAED,IAAIlB,YAAYG,cAAc;oBAC5BqY,WAAWzV,IAAI,CAAC,IAAIjD,QAAQ2Y,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAAC3a,OACC,IAAIiC,QAAQsY,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACFzX,2BACE,IAAI6X,4BAAmB,CAAC;gBACtB7a;gBACAoa,eAAe/W;gBACfyX,eAAexY;gBACfsB,SAAS,CAAC5D,MAAM4D,UAAUO;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxD7B,gBACE,IAAIyY,yBAAgB,CAAC;gBACnB/a;gBACAgb,YAAY,CAAChb,OAAO,CAAC,GAACD,2BAAAA,OAAOiE,YAAY,CAACiX,GAAG,qBAAvBlb,yBAAyBmb,SAAS;gBACxD7Z;gBACA8Z,kBAAkB;oBAChBC,iBAAiBta;oBACjBua,oCAAoCta;oBACpCua,wBAAwBvZ,aAAawZ,aAAa;oBAClDC,iCAAiCzZ,aAAa0Z,qBAAqB;oBACnEC,oCACE3Z,aAAa4Z,wBAAwB;gBACzC;YACF;YACFxZ,YACE,IAAIyZ,4BAAmB,CAAC;gBACtB9a;gBACAO;gBACAH;gBACAkZ,eAAe/W;gBACfzB;YACF;YACFc,WACI,IAAImZ,4CAAqB,CAAC;gBAAEra;YAAe,KAC3C,IAAIsa,gCAAe,CAAC;gBAAEta;gBAAgBwF,SAASlH;YAAI;YACvD,IAAIic,4CAAqB;YACzB5Z,YACE,IAAI6Z,8BAAc,CAAC;gBACjB,yDAAyD;gBACzDC,UAAU5gB,QAAQuC,OAAO,CAAC;gBAC1Bse,UAAUngB,QAAQC,GAAG,CAACmgB,cAAc;gBACpC1Q,MAAM,CAAC,uBAAuB,EAAEzL,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzDgS,UAAU;gBACVxN,MAAM;oBACJ,CAAC4X,wDAA4C,CAAC,EAAE;oBAChD,gCAAgC;oBAChCC,WAAW;gBACb;YACF;YACFhZ,aAAalB,YAAY,IAAIma,8CAAsB,CAAC;gBAAEtc;YAAI;YAC1DqD,aACGlB,CAAAA,WACG,IAAIoa,mDAA6B,CAAC;gBAChCvc;gBACAyB;gBACA+a,uBAAuB,CAAC,CAACzc,OAAOiE,YAAY,CAACyY,SAAS;YACxD,KACA,IAAI7Z,wBAAwB;gBAC1BnB;gBACAzB;gBACAsC;gBACAvB;YACF,EAAC;YACPsC,aACE,CAAClB,YACD,IAAIua,gCAAe,CAAC;gBAClB5c;gBACA8D,SAAS7D,OAAO6D,OAAO;gBACvBnC;gBACAzB;gBACAsC;gBACAiH,gBAAgBxJ,OAAOwJ,cAAc;gBACrCoT,iBAAiB5c,OAAOiE,YAAY,CAAC4Y,SAAS;gBAC9Ctb;gBACAC;YACF;YACF,CAACvB,OACCmC,YACA,CAAC,GAACpC,4BAAAA,OAAOiE,YAAY,CAACiX,GAAG,qBAAvBlb,0BAAyBmb,SAAS,KACpC,IAAI2B,sDAA0B,CAAC9c,OAAOiE,YAAY,CAACiX,GAAG,CAACC,SAAS;YAClE/Y,YACE,IAAI2a,8CAAsB,CAAC;gBACzBrb;YACF;YACF,CAACzB,OACCmC,YACApC,OAAOiE,YAAY,CAAC+Y,WAAW,IAC9Bra,CAAAA,WACG,IAAKyP,CAAAA,IAAAA,wBAAa,GAAC,EAAE6K,WAAW,CAACC,iBAAiB,CAAE;gBAClDC,QAAQnd,OAAOiE,YAAY,CAAC+Y,WAAW,KAAK;gBAC5CI,QAAQ;YACV,KACA,IAAIF,oCAAiB,CACnBld,OAAOiE,YAAY,CAAC+Y,WAAW,KAAK,SACtC;YACN/P;YACA,CAAChN,OACCwC,gBACA,IAAI,AACFnH,CAAAA,QAAQ,sDAAqD,EAC7D4R,eAAe,CAAC,IAAIC;YACxBR,mCACE,IAAI,AACFrR,CAAAA,QAAQ,iDAAgD,EACxDqV,OAAO,CAAC;gBACR1P;gBACA,GAAGjB,OAAOiE,YAAY,CAAC2I,mBAAmB;YAC5C;SACH,CAACvQ,MAAM,CAACuG;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAIxC,mBAAmB,CAACA,gBAAgBid,UAAU,EAAE;YAClDvf,gCAAAA;SAAAA,0BAAAA,cAAcD,OAAO,sBAArBC,iCAAAA,wBAAuBgB,OAAO,qBAA9BhB,+BAAgCqH,IAAI,CAAC/E,gBAAgBkd,OAAO;IAC9D;KAIAxf,yBAAAA,cAAcD,OAAO,sBAArBC,iCAAAA,uBAAuB4M,OAAO,qBAA9B5M,+BAAgCyf,OAAO,CACrC,IAAIC,wCAAmB,CACrBtd,CAAAA,6BAAAA,6BAAAA,SAAUwH,eAAe,qBAAzBxH,2BAA2BkL,KAAK,KAAI,CAAC,GACrChL;IAIJ,MAAM6B,iBAAiBnE;IAEvB,IAAIyE,cAAc;YAChBN,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAepF,MAAM,sBAArBoF,+BAAAA,uBAAuBhE,KAAK,qBAA5BgE,6BAA8Bsb,OAAO,CAAC;YACpC7X,MAAM;YACN9H,QAAQ;YACRd,MAAM;YACNsY,eAAe;QACjB;SACAnT,0BAAAA,eAAepF,MAAM,sBAArBoF,gCAAAA,wBAAuBhE,KAAK,qBAA5BgE,8BAA8Bsb,OAAO,CAAC;YACpCxG,YAAY;YACZnZ,QAAQ;YACRd,MAAM;YACNiU,OAAO7I,yBAAc,CAACuV,SAAS;QACjC;SACAxb,0BAAAA,eAAepF,MAAM,sBAArBoF,gCAAAA,wBAAuBhE,KAAK,qBAA5BgE,8BAA8Bsb,OAAO,CAAC;YACpChP,aAAarG,yBAAc,CAACuV,SAAS;YACrC3gB,MAAM;QACR;IACF;IAEAmF,eAAegb,WAAW,GAAG;QAC3BS,QAAQ;QACRC,iBAAiB;QACjBC,WAAWtf,MAAMC,OAAO,CAACyB,OAAOiE,YAAY,CAAC4Z,UAAU,IACnD;YACEC,aAAa9d,OAAOiE,YAAY,CAAC4Z,UAAU;YAC3CE,eAAeja,aAAI,CAACC,IAAI,CAAChE,KAAK;YAC9Bie,kBAAkBla,aAAI,CAACC,IAAI,CAAChE,KAAK;QACnC,IACAC,OAAOiE,YAAY,CAAC4Z,UAAU,GAC5B;YACEE,eAAeja,aAAI,CAACC,IAAI,CAAChE,KAAK;YAC9Bie,kBAAkBla,aAAI,CAACC,IAAI,CAAChE,KAAK;YACjC,GAAGC,OAAOiE,YAAY,CAAC4Z,UAAU;QACnC,IACAzZ;IACR;IAEAnC,eAAepF,MAAM,CAAE0Z,MAAM,GAAG;QAC9B0H,YAAY;YACVzH,KAAK;QACP;IACF;IACAvU,eAAepF,MAAM,CAAEqhB,SAAS,GAAG;QACjCC,OAAO;YACLtN,UAAU;QACZ;IACF;IAEA,IAAI,CAAC5O,eAAewR,MAAM,EAAE;QAC1BxR,eAAewR,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIrR,UAAU;QACZH,eAAewR,MAAM,CAAC2K,YAAY,GAAG;IACvC;IAEA,IAAIhc,YAAYG,cAAc;QAC5BN,eAAewR,MAAM,CAAC4K,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpDpc,eAAeqc,QAAQ,GAAG,CAAC;IAC3B,IAAItiB,QAAQsL,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCpF,eAAeqc,QAAQ,CAACC,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLtc,eAAeqc,QAAQ,CAACC,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAIviB,QAAQsL,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCpF,eAAeqc,QAAQ,CAACE,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAIve,KAAK;QACP,IAAI,CAACgC,eAAeiN,YAAY,EAAE;YAChCjN,eAAeiN,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAAC5L,WAAW;YACdrB,eAAeiN,YAAY,CAACuP,eAAe,GAAG;QAChD;QACAxc,eAAeiN,YAAY,CAACwP,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChC5Z,sBAAsB,EAAEjF,2BAAAA,wBAAAA,OAAQiE,YAAY,qBAApBjE,sBAAsBiF,sBAAsB;QACpE4G,aAAa7L,OAAO6L,WAAW;QAC/BrC,gBAAgBA;QAChBsV,eAAe9e,OAAO8e,aAAa;QACnCC,uBACE/e,OAAOgf,aAAa,KAAK,QACrB5a,YACApE,OAAOgf,aAAa,CAACC,QAAQ;QACnCC,6BAA6B,CAAC,CAAClf,OAAOkf,2BAA2B;QACjEC,iBAAiBnf,OAAOmf,eAAe;QACvCC,aAAapf,OAAOiE,YAAY,CAACmb,WAAW;QAC5CC,mBAAmBrf,OAAOiE,YAAY,CAACob,iBAAiB;QACxDC,mBAAmBtf,OAAOiE,YAAY,CAACqb,iBAAiB;QACxDrI,UAAUjX,OAAOiX,QAAQ;QACzBsD,6BAA6Bva,OAAOua,2BAA2B;QAC/D5G,aAAa3T,OAAO2T,WAAW;QAC/BpQ;QACAwX,eAAexY;QACflB;QACAiY,SAAS,CAAC,CAACtZ,OAAOsZ,OAAO;QACzBpW;QACAqc,WAAWvb;QACXuJ,aAAa,GAAEvN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBuN,aAAa;QAC7CD,qBAAqB,GAAEtN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBsN,qBAAqB;QAC7DD,gBAAgB,GAAErN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBqN,gBAAgB;QACnDD,KAAK,GAAEpN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiBoN,KAAK;QAC7BK,OAAO,GAAEzN,oBAAAA,OAAOoF,QAAQ,qBAAfpF,kBAAiByN,OAAO;QACjCjG,mBAAmBxH,OAAOwH,iBAAiB;QAC3CgY,iBAAiBxf,OAAO2W,MAAM,CAAC8I,UAAU;QACzCC,qBAAqB1f,OAAOiE,YAAY,CAACyb,mBAAmB;QAC5DC,kBAAkB3f,OAAOiE,YAAY,CAAC0b,gBAAgB;QACtDvY,yBAAyBpG;IAC3B;IAEA,MAAMH,QAAa;QACjB/D,MAAM;QACN,mFAAmF;QACnF8iB,sBAAsB3f,MAAM,IAAI4f;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjDnkB,SAAS,GAAGkZ,UAAU,CAAC,EAAE5Y,QAAQC,GAAG,CAACmgB,cAAc,CAAC,CAAC,EAAEuC,YAAY;QACnE7d,gBAAgBgD,aAAI,CAACC,IAAI,CAACF,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClEic,aAAa7f,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAOsZ,OAAO,IAAItZ,OAAO6K,UAAU,EAAE;QACvChK,MAAMkf,iBAAiB,GAAG;YACxB/f,QAAQ;gBAACA,OAAO6K,UAAU;aAAC;YAC3B,uGAAuG;YACvGmV,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLnf,MAAMkf,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACA/d,0BAAAA,eAAeyI,OAAO,qBAAtBzI,wBAAwBkD,IAAI,CAAC,CAACC;QAC5BA,SAAS6a,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAAcxc,aAAI,CAACyc,OAAO,CAACjlB,QAAQuC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAM2iB,OAAOT,kBAAmB;gBACnC,IAAIS,IAAIrP,UAAU,CAACmP,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEAve,eAAepB,KAAK,GAAGA;IAEvB,IAAI7E,QAAQC,GAAG,CAACykB,oBAAoB,EAAE;QACpC,MAAMC,QAAQ3kB,QAAQC,GAAG,CAACykB,oBAAoB,CAACxb,QAAQ,CAAC;QACxD,MAAM0b,gBACJ5kB,QAAQC,GAAG,CAACykB,oBAAoB,CAACxb,QAAQ,CAAC;QAC5C,MAAM2b,gBACJ7kB,QAAQC,GAAG,CAACykB,oBAAoB,CAACxb,QAAQ,CAAC;QAC5C,MAAM4b,gBACJ9kB,QAAQC,GAAG,CAACykB,oBAAoB,CAACxb,QAAQ,CAAC;QAC5C,MAAM6b,gBACJ/kB,QAAQC,GAAG,CAACykB,oBAAoB,CAACxb,QAAQ,CAAC;QAE5C,MAAM8b,UACJ,AAACJ,iBAAiBxe,YAAcye,iBAAiB5d;QACnD,MAAMge,UACJ,AAACH,iBAAiB1e,YAAc2e,iBAAiB9d;QAEnD,MAAMie,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvB1e,eAAekf,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzB/e,eAAeyI,OAAO,CAAEvF,IAAI,CAAC,CAACC;gBAC5BA,SAAS6a,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/CljB,QAAQokB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClBhf,eAAeyI,OAAO,CAAEvF,IAAI,CAAC,CAACC;gBAC5BA,SAAS6a,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/CljB,QAAQokB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJtI,gBAAO,CAACsI,cAAc;YACxB3f,eAAeyI,OAAO,CAAEvF,IAAI,CAC1B,IAAIyc,eAAe;gBACjBZ,SAAS;YACX;YAEF/e,eAAe+e,OAAO,GAAG;QAC3B;IACF;IAEAljB,gBAAgB,MAAM+jB,IAAAA,0BAAkB,EAAC/jB,eAAe;QACtDwC;QACAwhB,eAAe/hB;QACfgiB,eAAe3gB,WACX,IAAI8K,OAAO8V,IAAAA,gCAAkB,EAACle,aAAI,CAACC,IAAI,CAAC3C,UAAU,CAAC,IAAI,CAAC,MACxDgD;QACJd;QACA2e,eAAehiB;QACf+G,UAAU/D;QACV8X,eAAexY;QACf2f,WAAW9f,YAAYG;QACvBoR,aAAa3T,OAAO2T,WAAW,IAAI;QACnCwO,cAAcniB,OAAOmiB,YAAY;QACjCC,aAAapiB,OAAOoiB,WAAW;QAC/BlD,6BAA6Blf,OAAOkf,2BAA2B;QAC/DmD,QAAQriB,OAAOqiB,MAAM;QACrBpe,cAAcjE,OAAOiE,YAAY;QACjC2S,qBAAqB5W,OAAO2W,MAAM,CAACC,mBAAmB;QACtD9R,mBAAmB9E,OAAO8E,iBAAiB;QAC3C6a,kBAAkB3f,OAAOiE,YAAY,CAAC0b,gBAAgB;IACxD;IAEA,0BAA0B;IAC1B7hB,cAAc+C,KAAK,CAAC6K,IAAI,GAAG,GAAG5N,cAAc4N,IAAI,CAAC,CAAC,EAAE5N,cAAcwkB,IAAI,GACpEnhB,gBAAgB,cAAc,IAC9B;IAEF,IAAIlB,KAAK;QACP,IAAInC,cAAcjB,MAAM,EAAE;YACxBiB,cAAcjB,MAAM,CAAC0lB,WAAW,GAAG,CAAC1lB,UAClC,CAAC2D,mBAAmBkF,IAAI,CAAC7I,QAAOoU,QAAQ;QAC5C,OAAO;YACLnT,cAAcjB,MAAM,GAAG;gBACrB0lB,aAAa,CAAC1lB,UAAgB,CAAC2D,mBAAmBkF,IAAI,CAAC7I,QAAOoU,QAAQ;YACxE;QACF;IACF;IAEA,IAAIuR,kBAAkB1kB,cAAcb,OAAO;IAC3C,IAAI,OAAO+C,OAAOsZ,OAAO,KAAK,YAAY;YACdxb,wBA0CtBmE,6BAKKA;QA/CT,MAAMwgB,qBAAoB3kB,yBAAAA,cAAc4M,OAAO,qBAArB5M,uBAAuBsF,MAAM;QAEvDtF,gBAAgBkC,OAAOsZ,OAAO,CAACxb,eAAe;YAC5CiC;YACAE;YACA+G,UAAU/D;YACVlC;YACAf;YACA0I;YACAga,YAAYlmB,OAAOmP,IAAI,CAACzK,aAAakC,MAAM;YAC3CkW,SAASpX;YACT,GAAIe,0BACA;gBACE0f,aAAapgB,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAI0K,mBAAmBwV,mBAAmB;gBACf3kB;YAAzB,MAAM8kB,oBAAmB9kB,0BAAAA,cAAc4M,OAAO,qBAArB5M,wBAAuBsF,MAAM;YACtD,IAAIwf,kBAAkB;gBACpB,MAAMC,iBAAiBD,qBAAqBH;gBAC5CxV,gBAAgB6V,QAAQ,CAAC,kBAAkBD,iBAAiB,IAAI;YAClE;QACF;QAEA,IAAI,CAAC/kB,eAAe;YAClB,MAAM,qBAGL,CAHK,IAAInC,MACR,CAAC,6GAA6G,EAAEqE,OAAO+iB,cAAc,CAAC,GAAG,CAAC,GACxI,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAI9iB,OAAOuiB,oBAAoB1kB,cAAcb,OAAO,EAAE;YACpDa,cAAcb,OAAO,GAAGulB;YACxBzlB,qBAAqBylB;QACvB;QAEA,wDAAwD;QACxD,MAAMvgB,iBAAiBnE;QAEvB,0EAA0E;QAC1E,IAAImE,EAAAA,8BAAAA,eAAegb,WAAW,qBAA1Bhb,4BAA4B+gB,eAAe,MAAK,MAAM;YACxD/gB,eAAegb,WAAW,CAAC+F,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAOhhB,+BAAAA,eAAegb,WAAW,qBAA1Bhb,6BAA4B+gB,eAAe,MAAK,YACvD/gB,eAAegb,WAAW,CAAC+F,eAAe,CAACC,OAAO,KAAK,OACvD;YACAhhB,eAAegb,WAAW,CAAC+F,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAACnlB,cAAsBolB,IAAI,KAAK,YAAY;YACrDhmB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMc,QAAQH,EAAAA,wBAAAA,cAAcjB,MAAM,qBAApBiB,sBAAsBG,KAAK,KAAI,EAAE;IAE/C,MAAMklB,gBAAgBllB,MAAMmlB,IAAI,CAC9B,CAACjlB,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAKP,MAAM,KAAK,uBAChB,UAAUO,QACVA,KAAKuH,IAAI,YAAYwG,UACrB/N,KAAKuH,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAIyd,iBAAiB7f,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1CrF,MAAMkH,IAAI,CAAC;YACTO,MAAMyd,cAAczd,IAAI;YACxB4Q,OAAO;gBACLpO,yBAAc,CAACC,qBAAqB;gBACpCD,yBAAc,CAACI,mBAAmB;gBAClCJ,yBAAc,CAACM,eAAe;aAC/B,CAAC7H,GAAG,CAAC,CAACoQ,QAAW,CAAA;oBAChBxC,aAAawC;oBACblT,SAAS;wBACPiC,OAAOiW,IAAAA,iDAA0B,EAACvS,qBAAqB;4BACrDnC;4BACA0P;4BACAiF,WAAW5T;4BACXG;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAACvC,OAAO2W,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAMyM,gBAAgBplB,MAAMmlB,IAAI,CAC9B,CAACjlB,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAKP,MAAM,KAAK;QAExD,IAAIulB,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAc3d,IAAI,GAAG;QACvB;IACF;IAEA,IACE1F,OAAOiE,YAAY,CAACqf,SAAS,MAC7BxlB,yBAAAA,cAAcjB,MAAM,qBAApBiB,uBAAsBG,KAAK,KAC3BH,cAAc4M,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAM6Y,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjBle,SAASie;YACT1M,QAAQ0M;YACRzmB,MAAM;QACR;QAEA,MAAM2mB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAMvlB,QAAQL,cAAcjB,MAAM,CAACoB,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKN,OAAO,EAAE;gBAChB4lB,SAASte,IAAI,CAAChH;YAChB,OAAO;gBACL,IACEA,KAAKmY,KAAK,IACV,CAAEnY,CAAAA,KAAKuH,IAAI,IAAIvH,KAAKmH,OAAO,IAAInH,KAAK8S,QAAQ,IAAI9S,KAAK0Y,MAAM,AAAD,GAC1D;oBACA1Y,KAAKmY,KAAK,CAACpY,OAAO,CAAC,CAACO,IAAMilB,WAAWve,IAAI,CAAC1G;gBAC5C,OAAO;oBACLilB,WAAWve,IAAI,CAAChH;gBAClB;YACF;QACF;QAEAL,cAAcjB,MAAM,CAACoB,KAAK,GAAG;eACvBwlB;YACJ;gBACEnN,OAAO;uBAAIoN;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAOxjB,OAAO2jB,oBAAoB,KAAK,YAAY;QACrD,MAAM5c,UAAU/G,OAAO2jB,oBAAoB,CAAC;YAC1CrQ,cAAcxV,cAAcwV,YAAY;QAC1C;QACA,IAAIvM,QAAQuM,YAAY,EAAE;YACxBxV,cAAcwV,YAAY,GAAGvM,QAAQuM,YAAY;QACnD;IACF;IAEA,SAASsQ,YAAYzlB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAM0lB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAI1lB,gBAAgB+N,UAAU2X,UAAUrlB,IAAI,CAAC,CAACslB,QAAU3lB,KAAKuH,IAAI,CAACoe,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAO3lB,SAAS,YAAY;YAC9B,IACE0lB,UAAUrlB,IAAI,CAAC,CAACslB;gBACd,IAAI;oBACF,IAAI3lB,KAAK2lB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAIxlB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAAColB,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJjmB,EAAAA,yBAAAA,cAAcjB,MAAM,sBAApBiB,8BAAAA,uBAAsBG,KAAK,qBAA3BH,4BAA6BU,IAAI,CAC/B,CAACL,OAAcylB,YAAYzlB,KAAKuH,IAAI,KAAKke,YAAYzlB,KAAK6H,OAAO,OAC9D;IAEP,IAAI+d,kBAAkB;YAYhBjmB,8BAAAA,wBAWAA,yBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAImF,yBAAyB;YAC3B/F,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EACF,8FAEF;QAEN;QAEA,KAAIS,yBAAAA,cAAcjB,MAAM,sBAApBiB,+BAAAA,uBAAsBG,KAAK,qBAA3BH,6BAA6BsF,MAAM,EAAE;YACvC,6BAA6B;YAC7BtF,cAAcjB,MAAM,CAACoB,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAE6X,KAAK,GAAG;oBAC1B7X,EAAE6X,KAAK,GAAG7X,EAAE6X,KAAK,CAACja,MAAM,CACtB,CAAC2nB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAIpmB,0BAAAA,cAAc4M,OAAO,qBAArB5M,wBAAuBsF,MAAM,EAAE;YACjC,gCAAgC;YAChCtF,cAAc4M,OAAO,GAAG5M,cAAc4M,OAAO,CAACrO,MAAM,CAClD,CAACC,IAAM,AAACA,EAAU6nB,iBAAiB,KAAK;QAE5C;QACA,KAAIrmB,8BAAAA,cAAcoR,YAAY,sBAA1BpR,wCAAAA,4BAA4BqU,SAAS,qBAArCrU,sCAAuCsF,MAAM,EAAE;YACjD,uBAAuB;YACvBtF,cAAcoR,YAAY,CAACiD,SAAS,GAClCrU,cAAcoR,YAAY,CAACiD,SAAS,CAAC9V,MAAM,CACzC,CAAC+nB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,IAAIlkB,OAAOmC,UAAU;QACnBtH,mBAAmBgD,eAAe4K,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAM0b,gBAAqBvmB,cAAcgM,KAAK;IAC9C,IAAI,OAAOua,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAMxa,QACJ,OAAOua,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACEta,iBACAzL,MAAMC,OAAO,CAACuL,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAAC1G,MAAM,GAAG,GAC1B;gBACA,MAAMmhB,eAAexa,aAAa,CAChCK,4CAAgC,CACjC;gBACDN,KAAK,CAACM,4CAAgC,CAAC,GAAG;uBACrCN,KAAK,CAAC,UAAU;oBACnBya;iBACD;YACH;YACA,OAAOza,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAM4B,QAAQlP,OAAOmP,IAAI,CAAC7B,OAAQ;gBACrCA,KAAK,CAAC4B,KAAK,GAAG8Y,IAAAA,2BAAkB,EAAC;oBAC/BC,OAAO3a,KAAK,CAAC4B,KAAK;oBAClBzK;oBACAyK;oBACApI;gBACF;YACF;YAEA,OAAOwG;QACT;QACA,sCAAsC;QACtChM,cAAcgM,KAAK,GAAGwa;IACxB;IAEA,IAAI,CAACrkB,OAAO,OAAOnC,cAAcgM,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7BhM,cAAcgM,KAAK,GAAG,MAAMhM,cAAcgM,KAAK;IACjD;IAEA,OAAOhM;AACT;AAEA,SAASmY,uBAAuB,EAC9BC,mBAAmB,EACnB9T,QAAQ,EACRV,MAAM,EACN8H,cAAc,EAMf;IACC,8CAA8C;IAC9C,MAAMkb,uBAAuB5gB,aAAI,CAACC,IAAI,CAAC2U,+BAAiB,EAAE;IAE1D,MAAMiM,0BAA0B,CAACzP;QAC/B,OAAO;YACLjE,UAAUyT;YACV9mB,QAAQ;YACRmJ,SAAS;gBACPmO;YACF;QACF;IACF;IAEA,gGAAgG;IAChG,IAAI,CAACgB,qBAAqB;QACxB,OAAO;YACLyO,wBACE;SAEH;IACH;IAEA,kGAAkG;IAClG,4CAA4C;IAC5C,IAAI,CAACjjB,QAAQ;QACX,OAAO;YACLijB,wBACE;SAEH;IACH;IAEA,6HAA6H;IAC7H,4DAA4D;IAC5D,MAAMC,0BAA0BD,wBAC9B;IAGF,wGAAwG;IACxG,IAAIviB,UAAU;QACZ,OAAO;YAACwiB;SAAwB;IAClC;IAEA,OAAO;QACL;YACEtO,OAAO;gBACL;oBACErF,UAAUyT;oBACVnW,aAAakH,oCAA6B;oBAG1C7X,QAAQ;oBACRmJ,SAAS;wBACPrF;wBACA8H;oBACF;gBACF;gBACA,qGAAqG;gBACrGob;aACD;QACH;KACD;AACH", "ignoreList": [0]}