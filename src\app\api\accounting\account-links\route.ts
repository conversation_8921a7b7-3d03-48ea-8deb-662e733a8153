import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب روابط الحسابات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const linkedTable = searchParams.get('linked_table')
    const accountId = searchParams.get('account_id')

    let sql = `
      SELECT 
        al.*,
        coa.account_code,
        coa.account_name as parent_account_name,
        coa.current_balance as parent_balance,
        CASE 
          WHEN al.linked_table = 'clients' THEN c.name
          WHEN al.linked_table = 'employees' THEN e.name  
          WHEN al.linked_table = 'suppliers' THEN s.name
        END as linked_record_name,
        CASE 
          WHEN al.linked_table = 'clients' THEN c.current_balance
          WHEN al.linked_table = 'employees' THEN e.current_balance
          WHEN al.linked_table = 'suppliers' THEN s.current_balance
        END as linked_record_balance
      FROM account_links al
      JOIN chart_of_accounts coa ON al.main_account_id = coa.id
      LEFT JOIN clients c ON al.linked_table = 'clients' AND al.linked_record_id = c.id
      LEFT JOIN employees e ON al.linked_table = 'employees' AND al.linked_record_id = e.id
      LEFT JOIN suppliers s ON al.linked_table = 'suppliers' AND al.linked_record_id = s.id
      WHERE al.is_active = true
    `

    const params: any[] = []
    let paramIndex = 1

    if (linkedTable) {
      sql += ` AND al.linked_table = $${paramIndex}`
      params.push(linkedTable)
      paramIndex++
    }

    if (accountId) {
      sql += ` AND al.main_account_id = $${paramIndex}`
      params.push(parseInt(accountId))
      paramIndex++
    }

    sql += ` ORDER BY al.linked_table, al.sub_account_name`

    const result = await query(sql, params)

    return NextResponse.json({
      success: true,
      links: result.rows
    })

  } catch (error) {
    console.error('خطأ في جلب روابط الحسابات:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب روابط الحسابات'
    }, { status: 500 })
  }
}

// POST - إنشاء أو تحديث ربط حساب
export async function POST(request: NextRequest) {
  try {
    const {
      main_account_id,
      linked_table,
      linked_record_id,
      sub_account_code,
      sub_account_name,
      is_active = true
    } = await request.json()

    // التحقق من صحة البيانات
    if (!main_account_id || !linked_table || !linked_record_id) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة'
      }, { status: 400 })
    }

    // التحقق من وجود الربط مسبقاً
    const existingLink = await query(`
      SELECT id FROM account_links 
      WHERE linked_table = $1 AND linked_record_id = $2
    `, [linked_table, linked_record_id])

    if (existingLink.rows.length > 0) {
      // تحديث الربط الموجود
      const result = await query(`
        UPDATE account_links 
        SET main_account_id = $1, 
            sub_account_code = $2,
            sub_account_name = $3,
            is_active = $4,
            updated_at = CURRENT_TIMESTAMP
        WHERE linked_table = $5 AND linked_record_id = $6
        RETURNING *
      `, [main_account_id, sub_account_code, sub_account_name, is_active, linked_table, linked_record_id])

      // تحديث رصيد الحساب الأب
      await query('SELECT update_parent_account_balance($1)', [main_account_id])

      return NextResponse.json({
        success: true,
        message: 'تم تحديث ربط الحساب بنجاح',
        link: result.rows[0]
      })
    } else {
      // إنشاء ربط جديد
      const result = await query(`
        INSERT INTO account_links (
          main_account_id, linked_table, linked_record_id,
          sub_account_code, sub_account_name, is_active
        ) VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `, [main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, is_active])

      // تحديث رصيد الحساب الأب
      await query('SELECT update_parent_account_balance($1)', [main_account_id])

      return NextResponse.json({
        success: true,
        message: 'تم إنشاء ربط الحساب بنجاح',
        link: result.rows[0]
      })
    }

  } catch (error) {
    console.error('خطأ في إنشاء/تحديث ربط الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء/تحديث ربط الحساب'
    }, { status: 500 })
  }
}

// PUT - تحديث ربط حساب
export async function PUT(request: NextRequest) {
  try {
    const {
      id,
      main_account_id,
      is_active
    } = await request.json()

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الربط مطلوب'
      }, { status: 400 })
    }

    // الحصول على الربط القديم
    const oldLink = await query('SELECT main_account_id FROM account_links WHERE id = $1', [id])
    const oldAccountId = oldLink.rows[0]?.main_account_id

    // تحديث الربط
    const result = await query(`
      UPDATE account_links 
      SET main_account_id = $1, 
          is_active = $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [main_account_id, is_active, id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'ربط الحساب غير موجود'
      }, { status: 404 })
    }

    // تحديث رصيد الحساب القديم والجديد
    if (oldAccountId) {
      await query('SELECT update_parent_account_balance($1)', [oldAccountId])
    }
    if (main_account_id && main_account_id !== oldAccountId) {
      await query('SELECT update_parent_account_balance($1)', [main_account_id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث ربط الحساب بنجاح',
      link: result.rows[0]
    })

  } catch (error) {
    console.error('خطأ في تحديث ربط الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث ربط الحساب'
    }, { status: 500 })
  }
}

// DELETE - حذف ربط حساب
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الربط مطلوب'
      }, { status: 400 })
    }

    // الحصول على معرف الحساب الأب قبل الحذف
    const linkData = await query('SELECT main_account_id FROM account_links WHERE id = $1', [id])
    const accountId = linkData.rows[0]?.main_account_id

    // حذف الربط
    const result = await query('DELETE FROM account_links WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'ربط الحساب غير موجود'
      }, { status: 404 })
    }

    // تحديث رصيد الحساب الأب
    if (accountId) {
      await query('SELECT update_parent_account_balance($1)', [accountId])
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف ربط الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف ربط الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف ربط الحساب'
    }, { status: 500 })
  }
}
