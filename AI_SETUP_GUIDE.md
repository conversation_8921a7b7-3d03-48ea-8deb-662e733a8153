# 🤖 دليل إعداد نظام الذكاء الاصطناعي

## نظرة عامة

تم إنشاء نظام ذكاء اصطناعي متكامل يربط نافذة المحادثات مع النماذج المحلية (CodeLlama:13b و CodeGeeX) لتقديم ردود تلقائية ذكية للعملاء.

## 🎯 الميزات الرئيسية

### 1. الرد التلقائي الذكي
- رد تلقائي على رسائل العملاء خلال 30 ثانية
- استخدام النماذج المحلية (CodeLlama أو CodeGeeX)
- تخصيص الردود حسب السياق القانوني

### 2. التحكم الذكي
- **ساعات العمل**: رد فقط في أوقات العمل المحددة
- **الكلمات المحفزة**: يرد عند وجود كلمات مثل "مساعدة", "استفسار"
- **الكلمات المستبعدة**: لا يرد عند وجود "عاجل", "طارئ" (للحالات التي تحتاج تدخل بشري)
- **حد أقصى**: 5 ردود لكل محادثة يومياً

### 3. واجهة محسنة
- تمييز رسائل الذكاء الاصطناعي بتصميم مميز
- شارة "المساعد الذكي" مع أيقونات
- ألوان متدرجة للرسائل الذكية

## 🚀 خطوات الإعداد السريع

### الخطوة 1: تثبيت النماذج
```bash
# تشغيل سكريبت الإعداد التلقائي
cd /home/<USER>/Downloads/legal-system
./setup-ai-models.sh
```

### الخطوة 2: التحقق من التثبيت
```bash
# فحص حالة Ollama
curl http://localhost:11434/api/tags

# فحص النماذج المثبتة
ollama list
```

### الخطوة 3: تخصيص الإعدادات
1. اذهب إلى: `http://localhost:7443/admin/ai-settings`
2. تأكد من تفعيل النظام
3. اختر النموذج المناسب (CodeLlama أو CodeGeeX)
4. عدل ساعات العمل والكلمات المفتاحية

### الخطوة 4: اختبار النظام
1. استخدم زر "اختبار النموذج" في صفحة الإعدادات
2. أرسل رسالة من حساب عميل تحتوي على كلمة "مساعدة"
3. انتظر الرد التلقائي خلال 30 ثانية

## 📋 الملفات المُنشأة

### APIs الجديدة
- `/src/app/api/ai/local-models/route.ts` - إدارة النماذج المحلية
- `/src/app/api/ai/auto-reply/route.ts` - الرد التلقائي
- `/src/app/api/accounting/default-accounts/route.ts` - الحسابات الأساسية

### الصفحات
- `/src/app/admin/ai-settings/page.tsx` - إعدادات الذكاء الاصطناعي

### المكونات المحدثة
- `/src/components/chat/chat-widget.tsx` - واجهة المحادثات المحسنة

### ملفات الإعداد
- `setup-ai-models.sh` - سكريبت تثبيت النماذج
- `test_ai_chat_system.html` - صفحة اختبار شاملة

## 🔧 التخصيص المتقدم

### تعديل الردود
يمكنك تخصيص ردود النظام من صفحة الإعدادات:
- رسالة الترحيب
- رسالة خارج ساعات العمل  
- رسالة الوصول للحد الأقصى

### إضافة نماذج جديدة
لإضافة نموذج جديد، عدل ملف `/src/app/api/ai/local-models/route.ts`:
```typescript
const LOCAL_MODELS = {
  // النماذج الحالية...
  newmodel: {
    name: 'النموذج الجديد',
    endpoint: 'http://localhost:11434/api/generate',
    model: 'model-name',
    description: 'وصف النموذج'
  }
}
```

## 🧪 الاختبار

### اختبار سريع
```bash
# اختبار API النماذج
curl -X POST http://localhost:7443/api/ai/local-models \
  -H "Content-Type: application/json" \
  -d '{"message": "مرحبا", "model": "codellama"}'
```

### اختبار شامل
زر صفحة الاختبار: `http://localhost:7443/test_ai_chat_system.html`

## ⚠️ استكشاف الأخطاء

### مشكلة: النماذج غير متاحة
```bash
# إعادة تشغيل Ollama
sudo systemctl restart ollama

# فحص الحالة
sudo systemctl status ollama
```

### مشكلة: بطء في الردود
- استخدم نموذج أصغر (CodeLlama:7b بدلاً من 13b)
- تأكد من توفر ذاكرة كافية (8GB+)

### مشكلة: لا يرد النظام
- تحقق من الكلمات المحفزة في الإعدادات
- تأكد من أن الرسالة في ساعات العمل
- فحص عدد الردود اليومية

## 📊 المراقبة

### لوحة التحكم
- `/admin/ai-settings` - حالة النماذج والإعدادات
- `/accounting/default-accounts` - الحسابات الأساسية المحدثة

### السجلات
```bash
# سجلات Ollama
journalctl -u ollama -f

# سجلات التطبيق
# تحقق من console.log في المتصفح
```

## 🎉 النتيجة النهائية

بعد الإعداد، ستحصل على:
- ✅ رد تلقائي ذكي للعملاء
- ✅ واجهة محادثات محسنة
- ✅ تحكم كامل في الإعدادات
- ✅ نماذج تعمل محلياً (خصوصية كاملة)
- ✅ تكامل مع نظام المحادثات الحالي

---

**ملاحظة**: النظام يعمل بالكامل محلياً ولا يرسل أي بيانات لخوادم خارجية، مما يضمن خصوصية وأمان المحادثات.