const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function swapAdminUsers() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 تبديل أسماء المستخدمين وتحديث بيانات المدير...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // 1. تحديث المستخدم القديم (admin) ليصبح old_admin
    console.log('🔄 تحديث المستخدم القديم...');
    
    await client.query(`
      UPDATE users 
      SET username = 'old_admin'
      WHERE id = 1 AND username = 'admin'
    `);
    
    console.log('✅ تم تحديث المستخدم القديم إلى "old_admin"');

    // 2. تحديث المستخدم الجديد (admin2) ليصبح admin مع كلمة المرور الجديدة
    console.log('\n🔐 تحديث بيانات المدير محمد الحاشدي...');
    
    const hashedPassword = await bcrypt.hash('ana8080', 10);
    
    const updateResult = await client.query(`
      UPDATE users 
      SET 
        username = 'admin',
        password_hash = $1,
        email = '<EMAIL>',
        updated_at = NOW()
      WHERE id = 5
      RETURNING id, username, email, role, employee_id
    `, [hashedPassword]);

    const updatedUser = updateResult.rows[0];

    // 3. تحديث البريد الإلكتروني للموظف المرتبط
    console.log('\n📧 تحديث البريد الإلكتروني للموظف...');
    
    await client.query(`
      UPDATE employees 
      SET 
        email = '<EMAIL>',
        updated_at = NOW()
      WHERE id = $1
    `, [updatedUser.employee_id]);

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم تحديث بيانات المستخدم بنجاح!');
    console.log('================================');
    console.log('🔐 بيانات تسجيل الدخول الجديدة للمدير محمد الحاشدي:');
    console.log(`   👤 اسم المستخدم: ${updatedUser.username}`);
    console.log(`   🔑 كلمة المرور: ana8080`);
    console.log(`   📧 البريد الإلكتروني: ${updatedUser.email}`);
    console.log(`   👨‍💼 الدور: ${updatedUser.role}`);
    console.log(`   🆔 معرف المستخدم: ${updatedUser.id}`);
    console.log(`   🔗 مرتبط بالموظف: ${updatedUser.employee_id}`);
    
    console.log('\n================================');
    console.log('✅ يمكنك الآن تسجيل الدخول باستخدام:');
    console.log(`   اسم المستخدم: admin`);
    console.log(`   كلمة المرور: ana8080`);
    console.log('================================');

    // 4. عرض حالة جميع المستخدمين
    console.log('\n👥 حالة المستخدمين بعد التحديث:');
    
    const allUsers = await client.query(`
      SELECT id, username, email, role, employee_id 
      FROM users 
      ORDER BY id
    `);

    allUsers.rows.forEach(user => {
      console.log(`🆔 ${user.id} | 👤 ${user.username} | 📧 ${user.email} | 👨‍💼 ${user.role} | 🔗 موظف: ${user.employee_id || 'غير مرتبط'}`);
    });

    // 5. عرض معلومات الموظف المرتبط
    console.log('\n👤 معلومات الموظف المرتبط:');
    
    const employeeInfo = await client.query(`
      SELECT name, position, department, phone, email 
      FROM employees 
      WHERE id = $1
    `, [updatedUser.employee_id]);

    if (employeeInfo.rows.length > 0) {
      const emp = employeeInfo.rows[0];
      console.log(`   الاسم: ${emp.name}`);
      console.log(`   المنصب: ${emp.position}`);
      console.log(`   القسم: ${emp.department}`);
      console.log(`   الهاتف: ${emp.phone}`);
      console.log(`   البريد: ${emp.email}`);
    }

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تحديث بيانات المستخدمين:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

swapAdminUsers().catch(console.error);
