{"version": 3, "sources": ["../../../src/client/components/unrecognized-action-error.ts"], "sourcesContent": ["export class UnrecognizedActionError extends Error {\n  constructor(...args: ConstructorParameters<typeof Error>) {\n    super(...args)\n    this.name = 'UnrecognizedActionError'\n  }\n}\n\n/**\n * Check whether a server action call failed because the server action was not recognized by the server.\n * This can happen if the client and the server are not from the same deployment.\n *\n * Example usage:\n * ```ts\n * try {\n *   await myServerAction();\n * } catch (err) {\n *   if (unstable_isUnrecognizedActionError(err)) {\n *     // The client is from a different deployment than the server.\n *     // Reloading the page will fix this mismatch.\n *     window.alert(\"Please refresh the page and try again\");\n *     return;\n *   }\n * }\n * ```\n * */\nexport function unstable_isUnrecognizedActionError(\n  error: unknown\n): error is UnrecognizedActionError {\n  return !!(\n    error &&\n    typeof error === 'object' &&\n    error instanceof UnrecognizedActionError\n  )\n}\n"], "names": ["UnrecognizedActionError", "Error", "constructor", "args", "name", "unstable_isUnrecognizedActionError", "error"], "mappings": "AAAA,OAAO,MAAMA,gCAAgCC;IAC3CC,YAAY,GAAGC,IAAyC,CAAE;QACxD,KAAK,IAAIA;QACT,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAEA;;;;;;;;;;;;;;;;;GAiBG,GACH,OAAO,SAASC,mCACdC,KAAc;IAEd,OAAO,CAAC,CACNA,CAAAA,SACA,OAAOA,UAAU,YACjBA,iBAAiBN,uBAAsB;AAE3C", "ignoreList": [0]}