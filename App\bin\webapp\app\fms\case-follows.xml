<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج متابعة القضايا -->
<form-module name="case-follows" title="متابعة القضايا" icon="calendar">
    
    <list-view name="list" title="قائمة المتابعات">
        <data-source>
            SELECT 
                f.id,
                i.case_number,
                i.title as case_title,
                s.name as service_name,
                f.report,
                f.date_field,
                f.status,
                e.name as user_name,
                f.created_date
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            JOIN services s ON f.service_id = s.id
            LEFT JOIN employees e ON f.user_id = e.id
            ORDER BY f.date_field DESC, f.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="case_title" title="عنوان القضية" width="200"/>
            <column name="service_name" title="نوع الخدمة" width="150"/>
            <column name="report" title="تقرير المتابعة" width="300" truncate="50"/>
            <column name="date_field" title="تاريخ المتابعة" width="120" format="date"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="user_name" title="المستخدم" width="120"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="datetime"/>
        </columns>
        
        <filters>
            <filter name="case_id" title="القضية" type="lookup" 
                   lookup-table="issues" lookup-field="case_number + ' - ' + title"/>
            <filter name="service_id" title="نوع الخدمة" type="lookup" 
                   lookup-table="services" lookup-field="name"/>
            <filter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </filter>
            <filter name="date_from" title="من تاريخ" type="date"/>
            <filter name="date_to" title="إلى تاريخ" type="date"/>
        </filters>
        
        <actions>
            <action name="add" title="إضافة متابعة جديدة" icon="plus" color="primary"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"/>
            <action name="delete" title="حذف" icon="delete" color="danger" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
        </actions>
    </list-view>
    
    <form-view name="add" title="إضافة متابعة جديدة">
        <fields>
            <field name="case_id" title="القضية" type="lookup" required="true"
                   lookup-table="issues" lookup-field="case_number + ' - ' + title" 
                   lookup-display="case_number + ' - ' + title + ' (' + client_name + ')'"/>
            <field name="service_id" title="نوع الخدمة" type="lookup" required="true"
                   lookup-table="services" lookup-field="name"/>
            <field name="report" title="تقرير المتابعة" type="textarea" required="true" rows="6"
                   placeholder="اكتب تقرير مفصل عن المتابعة..."/>
            <field name="date_field" title="تاريخ المتابعة" type="date" default="today"/>
            <field name="status" title="حالة المتابعة" type="select" default="pending">
                <options>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </field>
            <field name="notes" title="ملاحظات إضافية" type="textarea" rows="3"/>
        </fields>
        
        <save-action>
            INSERT INTO follows (
                case_id, service_id, user_id, report, date_field, status, notes
            ) VALUES (
                @case_id, @service_id, @current_user_id, @report, @date_field, @status, @notes
            )
        </save-action>
        
        <update-action>
            UPDATE follows SET
                case_id = @case_id,
                service_id = @service_id,
                report = @report,
                date_field = @date_field,
                status = @status,
                notes = @notes
            WHERE id = @id
        </update-action>
    </form-view>
</form-module>
