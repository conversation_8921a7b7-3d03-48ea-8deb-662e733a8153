#!/bin/bash

# Legal System Server Stop Script
# سكريبت إيقاف خادم النظام القانوني

echo "🛑 إيقاف خادم النظام القانوني..."

# الانتقال إلى مجلد المشروع
cd /home/<USER>/Downloads/legal-system

# إيقاف العملية باستخدام PID إذا كان متوفراً
if [ -f "server.pid" ]; then
    PID=$(cat server.pid)
    if ps -p $PID > /dev/null 2>&1; then
        echo "🔄 إيقاف العملية $PID..."
        kill $PID
        sleep 2
        
        # التحقق من إيقاف العملية
        if ps -p $PID > /dev/null 2>&1; then
            echo "⚠️ العملية لم تتوقف، استخدام القوة..."
            kill -9 $PID
        fi
        
        echo "✅ تم إيقاف العملية $PID"
    else
        echo "⚠️ العملية $PID غير موجودة"
    fi
    
    rm -f server.pid
else
    echo "⚠️ ملف PID غير موجود"
fi

# إيقاف أي عمليات أخرى متعلقة بالخادم
echo "🧹 تنظيف العمليات المتبقية..."
pkill -f "next dev" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "next start" 2>/dev/null || true
pkill -f "npm start" 2>/dev/null || true

echo "✅ تم إيقاف جميع العمليات المتعلقة بالخادم"

# التحقق من المنفذ
if lsof -i :7443 >/dev/null 2>&1; then
    echo "⚠️ المنفذ 7443 لا يزال مستخدماً"
    echo "العمليات التي تستخدم المنفذ:"
    lsof -i :7443
else
    echo "✅ المنفذ 7443 متاح الآن"
fi
