const fs = require('fs').promises
const path = require('path')

const LAWS_DIRECTORY = '/home/<USER>/Downloads/legal-system/laws'

// دالة تنظيف وإصلاح ترميز الملف
async function fixFileEncoding(filePath) {
  try {
    console.log(`🔧 معالجة الملف: ${path.basename(filePath)}`)
    
    // قراءة الملف بطرق مختلفة للتعامل مع مشاكل الترميز
    let content
    try {
      content = await fs.readFile(filePath, 'utf8')
    } catch (error) {
      // محاولة قراءة بترميز مختلف
      try {
        content = await fs.readFile(filePath, 'latin1')
        console.log(`⚠️ تم استخدام ترميز latin1 للملف: ${path.basename(filePath)}`)
      } catch (error2) {
        console.log(`❌ فشل في قراءة الملف: ${path.basename(filePath)}`)
        return false
      }
    }
    
    // تنظيف المحتوى
    let cleanContent = content
      // إزالة BOM إذا وجد
      .replace(/^\uFEFF/, '')
      // توحيد نهايات الأسطر
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // استبدال المسافات غير المكسورة
      .replace(/\u00A0/g, ' ')
      // إزالة المسافات الزائدة
      .replace(/[ \t]+/g, ' ')
      // إزالة الأسطر الفارغة المتعددة
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      // تنظيف بداية ونهاية الملف
      .trim()
    
    // إضافة معلومات الملف في البداية إذا لم تكن موجودة
    if (!cleanContent.includes('================')) {
      const fileName = path.basename(filePath, '.txt')
        .replace(/agoyemen\.net_\d+_/, '')
        .replace(/-/g, ' ')
      
      cleanContent = `${fileName}\n${'='.repeat(50)}\n\n${cleanContent}`
    }
    
    // كتابة الملف المنظف
    await fs.writeFile(filePath, cleanContent, 'utf8')
    console.log(`✅ تم إصلاح الملف: ${path.basename(filePath)}`)
    return true
    
  } catch (error) {
    console.error(`❌ خطأ في معالجة الملف ${filePath}:`, error.message)
    return false
  }
}

// دالة معالجة جميع الملفات في مجلد
async function processDirectory(dirPath) {
  try {
    const items = await fs.readdir(dirPath, { withFileTypes: true })
    let processedCount = 0
    let errorCount = 0
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name)
      
      if (item.isDirectory()) {
        // معالجة المجلدات الفرعية
        console.log(`📁 معالجة المجلد: ${item.name}`)
        const subResult = await processDirectory(fullPath)
        processedCount += subResult.processed
        errorCount += subResult.errors
      } else if (item.isFile() && item.name.endsWith('.txt')) {
        // معالجة الملفات النصية
        const success = await fixFileEncoding(fullPath)
        if (success) {
          processedCount++
        } else {
          errorCount++
        }
      }
    }
    
    return { processed: processedCount, errors: errorCount }
    
  } catch (error) {
    console.error(`❌ خطأ في قراءة المجلد ${dirPath}:`, error.message)
    return { processed: 0, errors: 1 }
  }
}

// دالة إنشاء نسخة احتياطية
async function createBackup() {
  const backupDir = `${LAWS_DIRECTORY}_backup_${new Date().toISOString().slice(0, 10)}`
  try {
    await fs.cp(LAWS_DIRECTORY, backupDir, { recursive: true })
    console.log(`💾 تم إنشاء نسخة احتياطية في: ${backupDir}`)
    return true
  } catch (error) {
    console.error('❌ فشل في إنشاء النسخة الاحتياطية:', error.message)
    return false
  }
}

// الدالة الرئيسية
async function main() {
  console.log('🚀 بدء إصلاح ترميز الملفات القانونية...')
  console.log('=' .repeat(60))
  
  // إنشاء نسخة احتياطية
  console.log('💾 إنشاء نسخة احتياطية...')
  const backupSuccess = await createBackup()
  if (!backupSuccess) {
    console.log('⚠️ تحذير: لم يتم إنشاء نسخة احتياطية')
  }
  
  // معالجة الملفات
  console.log('\n🔧 بدء معالجة الملفات...')
  const result = await processDirectory(LAWS_DIRECTORY)
  
  // النتائج
  console.log('\n' + '='.repeat(60))
  console.log('📊 النتائج النهائية:')
  console.log(`✅ تم إصلاح ${result.processed} ملف بنجاح`)
  console.log(`❌ فشل في معالجة ${result.errors} ملف`)
  console.log('🎉 انتهت عملية الإصلاح!')
}

// تشغيل الأداة
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { fixFileEncoding, processDirectory }
