const { Pool } = require('pg');

// إعدادات قاعدة البيانات
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function clearAllData() {
  const client = await pool.connect();
  
  try {
    console.log('🗑️  بدء حذف جميع البيانات التجريبية...\n');

    // تعطيل فحص المفاتيح الخارجية مؤقتاً
    await client.query('SET session_replication_role = replica;');
    console.log('✅ تم تعطيل فحص المفاتيح الخارجية مؤقتاً');

    // قائمة الجداول بالترتيب الصحيح للحذف (من الأطفال إلى الآباء)
    const tables = [
      // جداول المتابعات والتوزيعات
      'follows',
      'case_distributions', 
      'hearings',
      
      // جداول المحاسبة والمالية
      'receipt_vouchers',
      'payment_vouchers',
      'journal_entries',
      'account_links',
      'main_accounts',
      
      // جداول القضايا والخدمات
      'issue_services',
      'issues',
      'services',
      
      // جداول الأشخاص والكيانات
      'clients',
      'employees',
      'suppliers',
      'users',
      
      // جداول الإعدادات والمراجع
      'courts',
      'governorates',
      'chart_of_accounts',
      'percentages',
      'navigation_pages',
      'user_roles',
      
      // جداول النظام
      'sessions'
    ];

    let totalDeleted = 0;

    // حذف البيانات من كل جدول
    for (const table of tables) {
      try {
        // التحقق من وجود الجدول
        const tableExists = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);

        if (!tableExists.rows[0].exists) {
          console.log(`⚠️  الجدول ${table} غير موجود - تم تخطيه`);
          continue;
        }

        // عد الصفوف قبل الحذف
        const countResult = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
        const rowCount = parseInt(countResult.rows[0].count);

        if (rowCount === 0) {
          console.log(`📭 الجدول ${table} فارغ بالفعل`);
          continue;
        }

        // حذف جميع البيانات
        await client.query(`DELETE FROM ${table}`);
        
        // إعادة تعيين تسلسل المفاتيح الأساسية إذا كان موجوداً
        try {
          await client.query(`ALTER SEQUENCE ${table}_id_seq RESTART WITH 1`);
        } catch (seqError) {
          // بعض الجداول قد لا تحتوي على تسلسل id
        }

        totalDeleted += rowCount;
        console.log(`🗑️  تم حذف ${rowCount} صف من جدول ${table}`);

      } catch (error) {
        console.error(`❌ خطأ في حذف بيانات جدول ${table}:`, error.message);
      }
    }

    // إعادة تفعيل فحص المفاتيح الخارجية
    await client.query('SET session_replication_role = DEFAULT;');
    console.log('✅ تم إعادة تفعيل فحص المفاتيح الخارجية');

    console.log(`\n🎉 تم الانتهاء من حذف البيانات!`);
    console.log(`📊 إجمالي الصفوف المحذوفة: ${totalDeleted}`);
    console.log(`🔄 تم إعادة تعيين جميع التسلسلات إلى 1`);

  } catch (error) {
    console.error('❌ خطأ عام في عملية الحذف:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// تشغيل السكريبت
clearAllData().catch(console.error);
