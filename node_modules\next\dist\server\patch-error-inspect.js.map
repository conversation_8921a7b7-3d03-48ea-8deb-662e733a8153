{"version": 3, "sources": ["../../src/server/patch-error-inspect.ts"], "sourcesContent": ["import {\n  findSourceMap as nativeFindSourceMap,\n  type SourceMapPayload,\n} from 'module'\nimport * as path from 'path'\nimport * as url from 'url'\nimport type * as util from 'util'\nimport { SourceMapConsumer as SyncSourceMapConsumer } from 'next/dist/compiled/source-map'\nimport type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport { parseStack } from '../client/components/react-dev-overlay/server/middleware-webpack'\nimport { getOriginalCodeFrame } from '../client/components/react-dev-overlay/server/shared'\nimport { workUnitAsyncStorage } from './app-render/work-unit-async-storage.external'\nimport { dim } from '../lib/picocolors'\n\ntype FindSourceMapPayload = (\n  sourceURL: string\n) => ModernSourceMapPayload | undefined\n// Find a source map using the bundler's API.\n// This is only a fallback for when Node.js fails to due to bugs e.g. https://github.com/nodejs/node/issues/52102\n// TODO: Remove once all supported Node.js versions are fixed.\n// TODO(veil): Set from Webpack as well\nlet bundlerFindSourceMapPayload: FindSourceMapPayload = () => undefined\n\nexport function setBundlerFindSourceMapImplementation(\n  findSourceMapImplementation: FindSourceMapPayload\n): void {\n  bundlerFindSourceMapPayload = findSourceMapImplementation\n}\n\n/**\n * https://tc39.es/source-map/#index-map\n */\ninterface IndexSourceMapSection {\n  offset: {\n    line: number\n    column: number\n  }\n  map: ModernRawSourceMap\n}\n\n// TODO(veil): Upstream types\ninterface IndexSourceMap {\n  version: number\n  file: string\n  sections: IndexSourceMapSection[]\n}\n\ninterface ModernRawSourceMap extends SourceMapPayload {\n  ignoreList?: number[]\n}\n\nexport type ModernSourceMapPayload = ModernRawSourceMap | IndexSourceMap\n\ninterface IgnoreableStackFrame extends StackFrame {\n  ignored: boolean\n}\n\ntype SourceMapCache = Map<\n  string,\n  null | { map: SyncSourceMapConsumer; payload: ModernSourceMapPayload }\n>\n\nfunction frameToString(frame: StackFrame): string {\n  let sourceLocation = frame.lineNumber !== null ? `:${frame.lineNumber}` : ''\n  if (frame.column !== null && sourceLocation !== '') {\n    sourceLocation += `:${frame.column}`\n  }\n\n  let fileLocation: string | null\n  if (\n    frame.file !== null &&\n    frame.file.startsWith('file://') &&\n    URL.canParse(frame.file)\n  ) {\n    // If not relative to CWD, the path is ambiguous to IDEs and clicking will prompt to select the file first.\n    // In a multi-app repo, this leads to potentially larger file names but will make clicking snappy.\n    // There's no tradeoff for the cases where `dir` in `next dev [dir]` is omitted\n    // since relative to cwd is both the shortest and snappiest.\n    fileLocation = path.relative(process.cwd(), url.fileURLToPath(frame.file))\n  } else if (frame.file !== null && frame.file.startsWith('/')) {\n    fileLocation = path.relative(process.cwd(), frame.file)\n  } else {\n    fileLocation = frame.file\n  }\n\n  return frame.methodName\n    ? `    at ${frame.methodName} (${fileLocation}${sourceLocation})`\n    : `    at ${fileLocation}${sourceLocation}`\n}\n\nfunction computeErrorName(error: Error): string {\n  // TODO: Node.js seems to use a different algorithm\n  // class ReadonlyRequestCookiesError extends Error {}` would read `ReadonlyRequestCookiesError: [...]`\n  // in the stack i.e. seems like under certain conditions it favors the constructor name.\n  return error.name || 'Error'\n}\n\nfunction prepareUnsourcemappedStackTrace(\n  error: Error,\n  structuredStackTrace: any[]\n): string {\n  const name = computeErrorName(error)\n  const message = error.message || ''\n  let stack = name + ': ' + message\n  for (let i = 0; i < structuredStackTrace.length; i++) {\n    stack += '\\n    at ' + structuredStackTrace[i].toString()\n  }\n  return stack\n}\n\nfunction shouldIgnoreListGeneratedFrame(file: string): boolean {\n  return file.startsWith('node:') || file.includes('node_modules')\n}\n\nfunction shouldIgnoreListOriginalFrame(file: string): boolean {\n  return file.includes('node_modules')\n}\n\n/**\n * Finds the sourcemap payload applicable to a given frame.\n * Equal to the input unless an Index Source Map is used.\n */\nfunction findApplicableSourceMapPayload(\n  frame: StackFrame,\n  payload: ModernSourceMapPayload\n): ModernRawSourceMap | undefined {\n  if ('sections' in payload) {\n    const frameLine = frame.lineNumber ?? 0\n    const frameColumn = frame.column ?? 0\n    // Sections must not overlap and must be sorted: https://tc39.es/source-map/#section-object\n    // Therefore the last section that has an offset less than or equal to the frame is the applicable one.\n    // TODO(veil): Binary search\n    let section: IndexSourceMapSection | undefined = payload.sections[0]\n    for (\n      let i = 0;\n      i < payload.sections.length &&\n      payload.sections[i].offset.line <= frameLine &&\n      payload.sections[i].offset.column <= frameColumn;\n      i++\n    ) {\n      section = payload.sections[i]\n    }\n\n    return section === undefined ? undefined : section.map\n  } else {\n    return payload\n  }\n}\n\ninterface SourcemappableStackFrame extends StackFrame {\n  file: NonNullable<StackFrame['file']>\n}\n\ninterface SourceMappedFrame {\n  stack: IgnoreableStackFrame\n  // DEV only\n  code: string | null\n}\n\nfunction createUnsourcemappedFrame(\n  frame: SourcemappableStackFrame\n): SourceMappedFrame {\n  return {\n    stack: {\n      arguments: frame.arguments,\n      column: frame.column,\n      file: frame.file,\n      lineNumber: frame.lineNumber,\n      methodName: frame.methodName,\n      ignored: shouldIgnoreListGeneratedFrame(frame.file),\n    },\n    code: null,\n  }\n}\n\n/**\n * @param frame\n * @param sourceMapCache\n * @returns The original frame if not sourcemapped.\n */\nfunction getSourcemappedFrameIfPossible(\n  frame: SourcemappableStackFrame,\n  sourceMapCache: SourceMapCache,\n  inspectOptions: util.InspectOptions\n): {\n  stack: IgnoreableStackFrame\n  // DEV only\n  code: string | null\n} {\n  const sourceMapCacheEntry = sourceMapCache.get(frame.file)\n  let sourceMapConsumer: SyncSourceMapConsumer\n  let sourceMapPayload: ModernSourceMapPayload\n  if (sourceMapCacheEntry === undefined) {\n    let sourceURL = frame.file\n    // e.g. \"/APP/.next/server/chunks/ssr/[root-of-the-server]__2934a0._.js\"\n    // will be keyed by Node.js as \"file:///APP/.next/server/chunks/ssr/[root-of-the-server]__2934a0._.js\".\n    // This is likely caused by `callsite.toString()` in `Error.prepareStackTrace converting file URLs to paths.\n    if (sourceURL.startsWith('/')) {\n      sourceURL = url.pathToFileURL(frame.file).toString()\n    }\n    let maybeSourceMapPayload: ModernSourceMapPayload | undefined\n    try {\n      const sourceMap = nativeFindSourceMap(sourceURL)\n      maybeSourceMapPayload = sourceMap?.payload\n    } catch (cause) {\n      // We should not log an actual error instance here because that will re-enter\n      // this codepath during error inspection and could lead to infinite recursion.\n      console.error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n      // If loading fails once, it'll fail every time.\n      // So set the cache to avoid duplicate errors.\n      sourceMapCache.set(frame.file, null)\n      // Don't even fall back to the bundler because it might be not as strict\n      // with regards to parsing and then we fail later once we consume the\n      // source map payload.\n      // This essentially avoids a redundant error where we fail here and then\n      // later on consumption because the bundler just handed back an invalid\n      // source map.\n      return createUnsourcemappedFrame(frame)\n    }\n    if (maybeSourceMapPayload === undefined) {\n      maybeSourceMapPayload = bundlerFindSourceMapPayload(sourceURL)\n    }\n\n    if (maybeSourceMapPayload === undefined) {\n      return createUnsourcemappedFrame(frame)\n    }\n    sourceMapPayload = maybeSourceMapPayload\n    try {\n      sourceMapConsumer = new SyncSourceMapConsumer(\n        // @ts-expect-error -- Module.SourceMap['version'] is number but SyncSourceMapConsumer wants a string\n        sourceMapPayload\n      )\n    } catch (cause) {\n      // We should not log an actual error instance here because that will re-enter\n      // this codepath during error inspection and could lead to infinite recursion.\n      console.error(\n        `${sourceURL}: Invalid source map. Only conformant source maps can be used to find the original code. Cause: ${cause}`\n      )\n      // If creating the consumer fails once, it'll fail every time.\n      // So set the cache to avoid duplicate errors.\n      sourceMapCache.set(frame.file, null)\n      return createUnsourcemappedFrame(frame)\n    }\n    sourceMapCache.set(frame.file, {\n      map: sourceMapConsumer,\n      payload: sourceMapPayload,\n    })\n  } else if (sourceMapCacheEntry === null) {\n    // We failed earlier getting the payload or consumer.\n    // Just return an unsourcemapped frame.\n    // Errors will already be logged.\n    return createUnsourcemappedFrame(frame)\n  } else {\n    sourceMapConsumer = sourceMapCacheEntry.map\n    sourceMapPayload = sourceMapCacheEntry.payload\n  }\n\n  const sourcePosition = sourceMapConsumer.originalPositionFor({\n    column: frame.column ?? 0,\n    line: frame.lineNumber ?? 1,\n  })\n\n  if (sourcePosition.source === null) {\n    return {\n      stack: {\n        arguments: frame.arguments,\n        column: frame.column,\n        file: frame.file,\n        lineNumber: frame.lineNumber,\n        methodName: frame.methodName,\n        ignored: shouldIgnoreListGeneratedFrame(frame.file),\n      },\n      code: null,\n    }\n  }\n\n  const sourceContent: string | null =\n    sourceMapConsumer.sourceContentFor(\n      sourcePosition.source,\n      /* returnNullOnMissing */ true\n    ) ?? null\n\n  const applicableSourceMap = findApplicableSourceMapPayload(\n    frame,\n    sourceMapPayload\n  )\n  // TODO(veil): Upstream a method to sourcemap consumer that immediately says if a frame is ignored or not.\n  let ignored = false\n  if (applicableSourceMap === undefined) {\n    console.error('No applicable source map found in sections for frame', frame)\n  } else if (shouldIgnoreListOriginalFrame(sourcePosition.source)) {\n    // Externals may be libraries that don't ship ignoreLists.\n    // This is really taking control away from libraries.\n    // They should still ship `ignoreList` so that attached debuggers ignore-list their frames.\n    // TODO: Maybe only ignore library sourcemaps if `ignoreList` is absent?\n    // Though keep in mind that Turbopack omits empty `ignoreList`.\n    // So if we establish this convention, we should communicate it to the ecosystem.\n    ignored = true\n  } else {\n    // TODO: O(n^2). Consider moving `ignoreList` into a Set\n    const sourceIndex = applicableSourceMap.sources.indexOf(\n      sourcePosition.source\n    )\n    ignored = applicableSourceMap.ignoreList?.includes(sourceIndex) ?? false\n  }\n\n  const originalFrame: IgnoreableStackFrame = {\n    // We ignore the sourcemapped name since it won't be the correct name.\n    // The callsite will point to the column of the variable name instead of the\n    // name of the enclosing function.\n    // TODO(NDX-531): Spy on prepareStackTrace to get the enclosing line number for method name mapping.\n    methodName: frame.methodName\n      ?.replace('__WEBPACK_DEFAULT_EXPORT__', 'default')\n      ?.replace('__webpack_exports__.', ''),\n    column: sourcePosition.column,\n    file: sourcePosition.source,\n    lineNumber: sourcePosition.line,\n    // TODO: c&p from async createOriginalStackFrame but why not frame.arguments?\n    arguments: [],\n    ignored,\n  }\n\n  const codeFrame =\n    process.env.NODE_ENV !== 'production'\n      ? getOriginalCodeFrame(\n          originalFrame,\n          sourceContent,\n          inspectOptions.colors\n        )\n      : null\n\n  return {\n    stack: originalFrame,\n    code: codeFrame,\n  }\n}\n\nfunction parseAndSourceMap(\n  error: Error,\n  inspectOptions: util.InspectOptions\n): string {\n  // TODO(veil): Expose as CLI arg or config option. Useful for local debugging.\n  const showIgnoreListed = false\n  // We overwrote Error.prepareStackTrace earlier so error.stack is not sourcemapped.\n  let unparsedStack = String(error.stack)\n  // We could just read it from `error.stack`.\n  // This works around cases where a 3rd party `Error.prepareStackTrace` implementation\n  // doesn't implement the name computation correctly.\n  const errorName = computeErrorName(error)\n\n  let idx = unparsedStack.indexOf('react-stack-bottom-frame')\n  if (idx !== -1) {\n    idx = unparsedStack.lastIndexOf('\\n', idx)\n  }\n  if (idx !== -1 && !showIgnoreListed) {\n    // Cut off everything after the bottom frame since it'll be React internals.\n    unparsedStack = unparsedStack.slice(0, idx)\n  }\n\n  const unsourcemappedStack = parseStack(unparsedStack)\n  const sourceMapCache: SourceMapCache = new Map()\n\n  let sourceMappedStack = ''\n  let sourceFrameDEV: null | string = null\n  for (const frame of unsourcemappedStack) {\n    if (frame.file === null) {\n      sourceMappedStack += '\\n' + frameToString(frame)\n    } else {\n      const sourcemappedFrame = getSourcemappedFrameIfPossible(\n        // We narrowed this earlier by bailing if `frame.file` is null.\n        frame as SourcemappableStackFrame,\n        sourceMapCache,\n        inspectOptions\n      )\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        sourcemappedFrame.code !== null &&\n        sourceFrameDEV === null &&\n        // TODO: Is this the right choice?\n        !sourcemappedFrame.stack.ignored\n      ) {\n        sourceFrameDEV = sourcemappedFrame.code\n      }\n      if (!sourcemappedFrame.stack.ignored) {\n        // TODO: Consider what happens if every frame is ignore listed.\n        sourceMappedStack += '\\n' + frameToString(sourcemappedFrame.stack)\n      } else if (showIgnoreListed && !inspectOptions.colors) {\n        sourceMappedStack += '\\n' + frameToString(sourcemappedFrame.stack)\n      } else if (showIgnoreListed) {\n        sourceMappedStack += '\\n' + dim(frameToString(sourcemappedFrame.stack))\n      }\n    }\n  }\n\n  return (\n    errorName +\n    ': ' +\n    error.message +\n    sourceMappedStack +\n    (sourceFrameDEV !== null ? '\\n' + sourceFrameDEV : '')\n  )\n}\n\nfunction sourceMapError(\n  this: void,\n  error: Error,\n  inspectOptions: util.InspectOptions\n): Error {\n  // Create a new Error object with the source mapping applied and then use native\n  // Node.js formatting on the result.\n  const newError =\n    error.cause !== undefined\n      ? // Setting an undefined `cause` would print `[cause]: undefined`\n        new Error(error.message, { cause: error.cause })\n      : new Error(error.message)\n\n  // TODO: Ensure `class MyError extends Error {}` prints `MyError` as the name\n  newError.stack = parseAndSourceMap(error, inspectOptions)\n\n  for (const key in error) {\n    if (!Object.prototype.hasOwnProperty.call(newError, key)) {\n      // @ts-expect-error -- We're copying all enumerable properties.\n      // So they definitely exist on `this` and obviously have no type on `newError` (yet)\n      newError[key] = error[key]\n    }\n  }\n\n  return newError\n}\n\nexport function patchErrorInspectNodeJS(\n  errorConstructor: ErrorConstructor\n): void {\n  const inspectSymbol = Symbol.for('nodejs.util.inspect.custom')\n\n  errorConstructor.prepareStackTrace = prepareUnsourcemappedStackTrace\n\n  // @ts-expect-error -- TODO upstream types\n  // eslint-disable-next-line no-extend-native -- We're not extending but overriding.\n  errorConstructor.prototype[inspectSymbol] = function (\n    depth: number,\n    inspectOptions: util.InspectOptions,\n    inspect: typeof util.inspect\n  ): string {\n    // avoid false-positive dynamic i/o warnings e.g. due to usage of `Math.random` in `source-map`.\n    return workUnitAsyncStorage.exit(() => {\n      const newError = sourceMapError(this, inspectOptions)\n\n      const originalCustomInspect = (newError as any)[inspectSymbol]\n      // Prevent infinite recursion.\n      // { customInspect: false } would result in `error.cause` not using our inspect.\n      Object.defineProperty(newError, inspectSymbol, {\n        value: undefined,\n        enumerable: false,\n        writable: true,\n      })\n      try {\n        return inspect(newError, {\n          ...inspectOptions,\n          depth:\n            (inspectOptions.depth ??\n              // Default in Node.js\n              2) - depth,\n        })\n      } finally {\n        ;(newError as any)[inspectSymbol] = originalCustomInspect\n      }\n    })\n  }\n}\n\nexport function patchErrorInspectEdgeLite(\n  errorConstructor: ErrorConstructor\n): void {\n  const inspectSymbol = Symbol.for('edge-runtime.inspect.custom')\n\n  errorConstructor.prepareStackTrace = prepareUnsourcemappedStackTrace\n\n  // @ts-expect-error -- TODO upstream types\n  // eslint-disable-next-line no-extend-native -- We're not extending but overriding.\n  errorConstructor.prototype[inspectSymbol] = function ({\n    format,\n  }: {\n    format: (...args: unknown[]) => string\n  }): string {\n    // avoid false-positive dynamic i/o warnings e.g. due to usage of `Math.random` in `source-map`.\n    return workUnitAsyncStorage.exit(() => {\n      const newError = sourceMapError(this, {})\n\n      const originalCustomInspect = (newError as any)[inspectSymbol]\n      // Prevent infinite recursion.\n      Object.defineProperty(newError, inspectSymbol, {\n        value: undefined,\n        enumerable: false,\n        writable: true,\n      })\n      try {\n        return format(newError)\n      } finally {\n        ;(newError as any)[inspectSymbol] = originalCustomInspect\n      }\n    })\n  }\n}\n"], "names": ["patchErrorInspectEdgeLite", "patchErrorInspectNodeJS", "setBundlerFindSourceMapImplementation", "bundlerFindSourceMapPayload", "undefined", "findSourceMapImplementation", "frameToString", "frame", "sourceLocation", "lineNumber", "column", "fileLocation", "file", "startsWith", "URL", "canParse", "path", "relative", "process", "cwd", "url", "fileURLToPath", "methodName", "computeErrorName", "error", "name", "prepareUnsourcemappedStackTrace", "structuredStackTrace", "message", "stack", "i", "length", "toString", "shouldIgnoreListGeneratedFrame", "includes", "shouldIgnoreListOriginalFrame", "findApplicableSourceMapPayload", "payload", "frameLine", "frameColumn", "section", "sections", "offset", "line", "map", "createUnsourcemappedFrame", "arguments", "ignored", "code", "getSourcemappedFrameIfPossible", "sourceMapCache", "inspectOptions", "sourceMapCacheEntry", "get", "sourceMapConsumer", "sourceMapPayload", "sourceURL", "pathToFileURL", "maybeSourceMapPayload", "sourceMap", "nativeFindSourceMap", "cause", "console", "set", "SyncSourceMapConsumer", "sourcePosition", "originalPositionFor", "source", "sourceContent", "sourceContentFor", "applicableSourceMap", "sourceIndex", "sources", "indexOf", "ignoreList", "originalFrame", "replace", "codeFrame", "env", "NODE_ENV", "getOriginalCodeFrame", "colors", "parseAndSourceMap", "showIgnoreListed", "unparsedStack", "String", "errorName", "idx", "lastIndexOf", "slice", "unsourcemappedStack", "parseStack", "Map", "sourceMappedStack", "sourceFrameDEV", "sourcemappedFrame", "dim", "sourceMapError", "newError", "Error", "key", "Object", "prototype", "hasOwnProperty", "call", "errorConstructor", "inspectSymbol", "Symbol", "for", "prepareStackTrace", "depth", "inspect", "workUnitAsyncStorage", "exit", "originalCustomInspect", "defineProperty", "value", "enumerable", "writable", "format"], "mappings": ";;;;;;;;;;;;;;;;IA0dgBA,yBAAyB;eAAzBA;;IAzCAC,uBAAuB;eAAvBA;;IA1ZAC,qCAAqC;eAArCA;;;wBApBT;8DACe;6DACD;2BAEsC;mCAEhC;wBACU;8CACA;4BACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKpB,6CAA6C;AAC7C,iHAAiH;AACjH,8DAA8D;AAC9D,uCAAuC;AACvC,IAAIC,8BAAoD,IAAMC;AAEvD,SAASF,sCACdG,2BAAiD;IAEjDF,8BAA8BE;AAChC;AAmCA,SAASC,cAAcC,KAAiB;IACtC,IAAIC,iBAAiBD,MAAME,UAAU,KAAK,OAAO,CAAC,CAAC,EAAEF,MAAME,UAAU,EAAE,GAAG;IAC1E,IAAIF,MAAMG,MAAM,KAAK,QAAQF,mBAAmB,IAAI;QAClDA,kBAAkB,CAAC,CAAC,EAAED,MAAMG,MAAM,EAAE;IACtC;IAEA,IAAIC;IACJ,IACEJ,MAAMK,IAAI,KAAK,QACfL,MAAMK,IAAI,CAACC,UAAU,CAAC,cACtBC,IAAIC,QAAQ,CAACR,MAAMK,IAAI,GACvB;QACA,2GAA2G;QAC3G,kGAAkG;QAClG,+EAA+E;QAC/E,4DAA4D;QAC5DD,eAAeK,MAAKC,QAAQ,CAACC,QAAQC,GAAG,IAAIC,KAAIC,aAAa,CAACd,MAAMK,IAAI;IAC1E,OAAO,IAAIL,MAAMK,IAAI,KAAK,QAAQL,MAAMK,IAAI,CAACC,UAAU,CAAC,MAAM;QAC5DF,eAAeK,MAAKC,QAAQ,CAACC,QAAQC,GAAG,IAAIZ,MAAMK,IAAI;IACxD,OAAO;QACLD,eAAeJ,MAAMK,IAAI;IAC3B;IAEA,OAAOL,MAAMe,UAAU,GACnB,CAAC,OAAO,EAAEf,MAAMe,UAAU,CAAC,EAAE,EAAEX,eAAeH,eAAe,CAAC,CAAC,GAC/D,CAAC,OAAO,EAAEG,eAAeH,gBAAgB;AAC/C;AAEA,SAASe,iBAAiBC,KAAY;IACpC,mDAAmD;IACnD,sGAAsG;IACtG,wFAAwF;IACxF,OAAOA,MAAMC,IAAI,IAAI;AACvB;AAEA,SAASC,gCACPF,KAAY,EACZG,oBAA2B;IAE3B,MAAMF,OAAOF,iBAAiBC;IAC9B,MAAMI,UAAUJ,MAAMI,OAAO,IAAI;IACjC,IAAIC,QAAQJ,OAAO,OAAOG;IAC1B,IAAK,IAAIE,IAAI,GAAGA,IAAIH,qBAAqBI,MAAM,EAAED,IAAK;QACpDD,SAAS,cAAcF,oBAAoB,CAACG,EAAE,CAACE,QAAQ;IACzD;IACA,OAAOH;AACT;AAEA,SAASI,+BAA+BrB,IAAY;IAClD,OAAOA,KAAKC,UAAU,CAAC,YAAYD,KAAKsB,QAAQ,CAAC;AACnD;AAEA,SAASC,8BAA8BvB,IAAY;IACjD,OAAOA,KAAKsB,QAAQ,CAAC;AACvB;AAEA;;;CAGC,GACD,SAASE,+BACP7B,KAAiB,EACjB8B,OAA+B;IAE/B,IAAI,cAAcA,SAAS;QACzB,MAAMC,YAAY/B,MAAME,UAAU,IAAI;QACtC,MAAM8B,cAAchC,MAAMG,MAAM,IAAI;QACpC,2FAA2F;QAC3F,uGAAuG;QACvG,4BAA4B;QAC5B,IAAI8B,UAA6CH,QAAQI,QAAQ,CAAC,EAAE;QACpE,IACE,IAAIX,IAAI,GACRA,IAAIO,QAAQI,QAAQ,CAACV,MAAM,IAC3BM,QAAQI,QAAQ,CAACX,EAAE,CAACY,MAAM,CAACC,IAAI,IAAIL,aACnCD,QAAQI,QAAQ,CAACX,EAAE,CAACY,MAAM,CAAChC,MAAM,IAAI6B,aACrCT,IACA;YACAU,UAAUH,QAAQI,QAAQ,CAACX,EAAE;QAC/B;QAEA,OAAOU,YAAYpC,YAAYA,YAAYoC,QAAQI,GAAG;IACxD,OAAO;QACL,OAAOP;IACT;AACF;AAYA,SAASQ,0BACPtC,KAA+B;IAE/B,OAAO;QACLsB,OAAO;YACLiB,WAAWvC,MAAMuC,SAAS;YAC1BpC,QAAQH,MAAMG,MAAM;YACpBE,MAAML,MAAMK,IAAI;YAChBH,YAAYF,MAAME,UAAU;YAC5Ba,YAAYf,MAAMe,UAAU;YAC5ByB,SAASd,+BAA+B1B,MAAMK,IAAI;QACpD;QACAoC,MAAM;IACR;AACF;AAEA;;;;CAIC,GACD,SAASC,+BACP1C,KAA+B,EAC/B2C,cAA8B,EAC9BC,cAAmC;QAkIrB5C,2BAAAA;IA5Hd,MAAM6C,sBAAsBF,eAAeG,GAAG,CAAC9C,MAAMK,IAAI;IACzD,IAAI0C;IACJ,IAAIC;IACJ,IAAIH,wBAAwBhD,WAAW;QACrC,IAAIoD,YAAYjD,MAAMK,IAAI;QAC1B,wEAAwE;QACxE,uGAAuG;QACvG,4GAA4G;QAC5G,IAAI4C,UAAU3C,UAAU,CAAC,MAAM;YAC7B2C,YAAYpC,KAAIqC,aAAa,CAAClD,MAAMK,IAAI,EAAEoB,QAAQ;QACpD;QACA,IAAI0B;QACJ,IAAI;YACF,MAAMC,YAAYC,IAAAA,qBAAmB,EAACJ;YACtCE,wBAAwBC,6BAAAA,UAAWtB,OAAO;QAC5C,EAAE,OAAOwB,OAAO;YACd,6EAA6E;YAC7E,8EAA8E;YAC9EC,QAAQtC,KAAK,CACX,GAAGgC,UAAU,gGAAgG,EAAEK,OAAO;YAExH,gDAAgD;YAChD,8CAA8C;YAC9CX,eAAea,GAAG,CAACxD,MAAMK,IAAI,EAAE;YAC/B,wEAAwE;YACxE,qEAAqE;YACrE,sBAAsB;YACtB,wEAAwE;YACxE,uEAAuE;YACvE,cAAc;YACd,OAAOiC,0BAA0BtC;QACnC;QACA,IAAImD,0BAA0BtD,WAAW;YACvCsD,wBAAwBvD,4BAA4BqD;QACtD;QAEA,IAAIE,0BAA0BtD,WAAW;YACvC,OAAOyC,0BAA0BtC;QACnC;QACAgD,mBAAmBG;QACnB,IAAI;YACFJ,oBAAoB,IAAIU,4BAAqB,CAC3C,qGAAqG;YACrGT;QAEJ,EAAE,OAAOM,OAAO;YACd,6EAA6E;YAC7E,8EAA8E;YAC9EC,QAAQtC,KAAK,CACX,GAAGgC,UAAU,gGAAgG,EAAEK,OAAO;YAExH,8DAA8D;YAC9D,8CAA8C;YAC9CX,eAAea,GAAG,CAACxD,MAAMK,IAAI,EAAE;YAC/B,OAAOiC,0BAA0BtC;QACnC;QACA2C,eAAea,GAAG,CAACxD,MAAMK,IAAI,EAAE;YAC7BgC,KAAKU;YACLjB,SAASkB;QACX;IACF,OAAO,IAAIH,wBAAwB,MAAM;QACvC,qDAAqD;QACrD,uCAAuC;QACvC,iCAAiC;QACjC,OAAOP,0BAA0BtC;IACnC,OAAO;QACL+C,oBAAoBF,oBAAoBR,GAAG;QAC3CW,mBAAmBH,oBAAoBf,OAAO;IAChD;IAEA,MAAM4B,iBAAiBX,kBAAkBY,mBAAmB,CAAC;QAC3DxD,QAAQH,MAAMG,MAAM,IAAI;QACxBiC,MAAMpC,MAAME,UAAU,IAAI;IAC5B;IAEA,IAAIwD,eAAeE,MAAM,KAAK,MAAM;QAClC,OAAO;YACLtC,OAAO;gBACLiB,WAAWvC,MAAMuC,SAAS;gBAC1BpC,QAAQH,MAAMG,MAAM;gBACpBE,MAAML,MAAMK,IAAI;gBAChBH,YAAYF,MAAME,UAAU;gBAC5Ba,YAAYf,MAAMe,UAAU;gBAC5ByB,SAASd,+BAA+B1B,MAAMK,IAAI;YACpD;YACAoC,MAAM;QACR;IACF;IAEA,MAAMoB,gBACJd,kBAAkBe,gBAAgB,CAChCJ,eAAeE,MAAM,EACrB,uBAAuB,GAAG,SACvB;IAEP,MAAMG,sBAAsBlC,+BAC1B7B,OACAgD;IAEF,0GAA0G;IAC1G,IAAIR,UAAU;IACd,IAAIuB,wBAAwBlE,WAAW;QACrC0D,QAAQtC,KAAK,CAAC,wDAAwDjB;IACxE,OAAO,IAAI4B,8BAA8B8B,eAAeE,MAAM,GAAG;QAC/D,0DAA0D;QAC1D,qDAAqD;QACrD,2FAA2F;QAC3F,wEAAwE;QACxE,+DAA+D;QAC/D,iFAAiF;QACjFpB,UAAU;IACZ,OAAO;YAKKuB;QAJV,wDAAwD;QACxD,MAAMC,cAAcD,oBAAoBE,OAAO,CAACC,OAAO,CACrDR,eAAeE,MAAM;QAEvBpB,UAAUuB,EAAAA,kCAAAA,oBAAoBI,UAAU,qBAA9BJ,gCAAgCpC,QAAQ,CAACqC,iBAAgB;IACrE;IAEA,MAAMI,gBAAsC;QAC1C,sEAAsE;QACtE,4EAA4E;QAC5E,kCAAkC;QAClC,oGAAoG;QACpGrD,UAAU,GAAEf,oBAAAA,MAAMe,UAAU,sBAAhBf,4BAAAA,kBACRqE,OAAO,CAAC,8BAA8B,+BAD9BrE,0BAERqE,OAAO,CAAC,wBAAwB;QACpClE,QAAQuD,eAAevD,MAAM;QAC7BE,MAAMqD,eAAeE,MAAM;QAC3B1D,YAAYwD,eAAetB,IAAI;QAC/B,6EAA6E;QAC7EG,WAAW,EAAE;QACbC;IACF;IAEA,MAAM8B,YACJ3D,QAAQ4D,GAAG,CAACC,QAAQ,KAAK,eACrBC,IAAAA,4BAAoB,EAClBL,eACAP,eACAjB,eAAe8B,MAAM,IAEvB;IAEN,OAAO;QACLpD,OAAO8C;QACP3B,MAAM6B;IACR;AACF;AAEA,SAASK,kBACP1D,KAAY,EACZ2B,cAAmC;IAEnC,8EAA8E;IAC9E,MAAMgC,mBAAmB;IACzB,mFAAmF;IACnF,IAAIC,gBAAgBC,OAAO7D,MAAMK,KAAK;IACtC,4CAA4C;IAC5C,qFAAqF;IACrF,oDAAoD;IACpD,MAAMyD,YAAY/D,iBAAiBC;IAEnC,IAAI+D,MAAMH,cAAcX,OAAO,CAAC;IAChC,IAAIc,QAAQ,CAAC,GAAG;QACdA,MAAMH,cAAcI,WAAW,CAAC,MAAMD;IACxC;IACA,IAAIA,QAAQ,CAAC,KAAK,CAACJ,kBAAkB;QACnC,4EAA4E;QAC5EC,gBAAgBA,cAAcK,KAAK,CAAC,GAAGF;IACzC;IAEA,MAAMG,sBAAsBC,IAAAA,6BAAU,EAACP;IACvC,MAAMlC,iBAAiC,IAAI0C;IAE3C,IAAIC,oBAAoB;IACxB,IAAIC,iBAAgC;IACpC,KAAK,MAAMvF,SAASmF,oBAAqB;QACvC,IAAInF,MAAMK,IAAI,KAAK,MAAM;YACvBiF,qBAAqB,OAAOvF,cAAcC;QAC5C,OAAO;YACL,MAAMwF,oBAAoB9C,+BACxB,+DAA+D;YAC/D1C,OACA2C,gBACAC;YAGF,IACEjC,QAAQ4D,GAAG,CAACC,QAAQ,KAAK,gBACzBgB,kBAAkB/C,IAAI,KAAK,QAC3B8C,mBAAmB,QACnB,kCAAkC;YAClC,CAACC,kBAAkBlE,KAAK,CAACkB,OAAO,EAChC;gBACA+C,iBAAiBC,kBAAkB/C,IAAI;YACzC;YACA,IAAI,CAAC+C,kBAAkBlE,KAAK,CAACkB,OAAO,EAAE;gBACpC,+DAA+D;gBAC/D8C,qBAAqB,OAAOvF,cAAcyF,kBAAkBlE,KAAK;YACnE,OAAO,IAAIsD,oBAAoB,CAAChC,eAAe8B,MAAM,EAAE;gBACrDY,qBAAqB,OAAOvF,cAAcyF,kBAAkBlE,KAAK;YACnE,OAAO,IAAIsD,kBAAkB;gBAC3BU,qBAAqB,OAAOG,IAAAA,eAAG,EAAC1F,cAAcyF,kBAAkBlE,KAAK;YACvE;QACF;IACF;IAEA,OACEyD,YACA,OACA9D,MAAMI,OAAO,GACbiE,oBACCC,CAAAA,mBAAmB,OAAO,OAAOA,iBAAiB,EAAC;AAExD;AAEA,SAASG,eAEPzE,KAAY,EACZ2B,cAAmC;IAEnC,gFAAgF;IAChF,oCAAoC;IACpC,MAAM+C,WACJ1E,MAAMqC,KAAK,KAAKzD,YAEZ,qBAAgD,CAAhD,IAAI+F,MAAM3E,MAAMI,OAAO,EAAE;QAAEiC,OAAOrC,MAAMqC,KAAK;IAAC,IAA9C,qBAAA;eAAA;oBAAA;sBAAA;IAA+C,KAC/C,qBAAwB,CAAxB,IAAIsC,MAAM3E,MAAMI,OAAO,GAAvB,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;IAE7B,6EAA6E;IAC7EsE,SAASrE,KAAK,GAAGqD,kBAAkB1D,OAAO2B;IAE1C,IAAK,MAAMiD,OAAO5E,MAAO;QACvB,IAAI,CAAC6E,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,UAAUE,MAAM;YACxD,+DAA+D;YAC/D,oFAAoF;YACpFF,QAAQ,CAACE,IAAI,GAAG5E,KAAK,CAAC4E,IAAI;QAC5B;IACF;IAEA,OAAOF;AACT;AAEO,SAASjG,wBACdwG,gBAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,GAAG,CAAC;IAEjCH,iBAAiBI,iBAAiB,GAAGnF;IAErC,0CAA0C;IAC1C,mFAAmF;IACnF+E,iBAAiBH,SAAS,CAACI,cAAc,GAAG,SAC1CI,KAAa,EACb3D,cAAmC,EACnC4D,OAA4B;QAE5B,gGAAgG;QAChG,OAAOC,kDAAoB,CAACC,IAAI,CAAC;YAC/B,MAAMf,WAAWD,eAAe,IAAI,EAAE9C;YAEtC,MAAM+D,wBAAwB,AAAChB,QAAgB,CAACQ,cAAc;YAC9D,8BAA8B;YAC9B,gFAAgF;YAChFL,OAAOc,cAAc,CAACjB,UAAUQ,eAAe;gBAC7CU,OAAOhH;gBACPiH,YAAY;gBACZC,UAAU;YACZ;YACA,IAAI;gBACF,OAAOP,QAAQb,UAAU;oBACvB,GAAG/C,cAAc;oBACjB2D,OACE,AAAC3D,CAAAA,eAAe2D,KAAK,IACnB,qBAAqB;oBACrB,CAAA,IAAKA;gBACX;YACF,SAAU;;gBACNZ,QAAgB,CAACQ,cAAc,GAAGQ;YACtC;QACF;IACF;AACF;AAEO,SAASlH,0BACdyG,gBAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,GAAG,CAAC;IAEjCH,iBAAiBI,iBAAiB,GAAGnF;IAErC,0CAA0C;IAC1C,mFAAmF;IACnF+E,iBAAiBH,SAAS,CAACI,cAAc,GAAG,SAAU,EACpDa,MAAM,EAGP;QACC,gGAAgG;QAChG,OAAOP,kDAAoB,CAACC,IAAI,CAAC;YAC/B,MAAMf,WAAWD,eAAe,IAAI,EAAE,CAAC;YAEvC,MAAMiB,wBAAwB,AAAChB,QAAgB,CAACQ,cAAc;YAC9D,8BAA8B;YAC9BL,OAAOc,cAAc,CAACjB,UAAUQ,eAAe;gBAC7CU,OAAOhH;gBACPiH,YAAY;gBACZC,UAAU;YACZ;YACA,IAAI;gBACF,OAAOC,OAAOrB;YAChB,SAAU;;gBACNA,QAAgB,CAACQ,cAAc,GAAGQ;YACtC;QACF;IACF;AACF"}