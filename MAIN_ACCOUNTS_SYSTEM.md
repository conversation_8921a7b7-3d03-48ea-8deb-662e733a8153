# نظام ربط الحسابات الرئيسية - Main Accounts Linking System

## نظرة عامة

تم إنشاء نظام ربط الحسابات الرئيسية لتحديد الحسابات الهامة من دليل الحسابات. يوفر هذا النظام صفحة واحدة بسيطة تحتوي على جدول بعمودين: اسم الحساب ورقم الحساب، مع إمكانية اختيار الحساب المناسب من دليل الحسابات.

## الحسابات الرئيسية المُعرَّفة

1. **حساب العملاء التجميعي** - الحساب التجميعي لجميع حسابات العملاء
2. **حساب الايرادات** - الحساب الرئيسي للإيرادات
3. **حساب المصروفات** - الحساب الرئيسي للمصروفات
4. **حساب رأس المال** - حساب رأس المال المستثمر
5. **حساب الموظفين التجميعي** - الحساب التجميعي لجميع حسابات الموظفين
6. **حساب الصندوق الرئيسي** - الصندوق الرئيسي للنقدية
7. **حساب الفوارق** - حساب الفوارق المحاسبية
8. **حساب فوارق العملات** - حساب فوارق أسعار الصرف
9. **حساب مصلحة الضرائب** - حساب المستحقات الضريبية
10. **حساب المرتبات** - حساب مرتبات الموظفين

## هيكل قاعدة البيانات

### جدول `main_accounts`
```sql
CREATE TABLE main_accounts (
  id SERIAL PRIMARY KEY,
  account_name VARCHAR(255) NOT NULL UNIQUE,
  account_code VARCHAR(20),
  chart_account_id INTEGER REFERENCES chart_of_accounts(id),
  is_required BOOLEAN DEFAULT TRUE,
  description TEXT,
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## الملفات المُنشأة

### 1. قاعدة البيانات
- **المسار**: `/database/create_main_accounts_table.js`
- **الوظيفة**: إنشاء جدول الحسابات الرئيسية وإدراج البيانات الأساسية

### 2. API Endpoint
- **المسار**: `/src/app/api/accounting/main-accounts/route.ts`
- **الوظائف**:
  - `GET`: جلب الحسابات الرئيسية مع دليل الحسابات
  - `POST`: حفظ ربط الحسابات الرئيسية

### 3. صفحة ربط الحسابات
- **المسار**: `/src/app/accounting/main-accounts/page.tsx`
- **الوصف**: واجهة المستخدم لربط الحسابات الرئيسية

### 4. ملف الاختبار
- **المسار**: `/test_main_accounts_system.js`
- **الوصف**: اختبار شامل للنظام

## كيفية الاستخدام

### 1. الوصول إلى النظام
```
http://localhost:7443/accounting/main-accounts
```

### 2. ربط الحسابات
1. في كل صف، ستجد اسم الحساب الرئيسي
2. بجانب كل حساب، يوجد قائمة منسدلة تحتوي على جميع الحسابات من الدليل
3. اختر الحساب المناسب من القائمة
4. انقر على "حفظ الربط" لحفظ جميع التغييرات

### 3. مراقبة الحالة
- الحسابات المربوطة تظهر بعلامة ✓ خضراء
- الحسابات غير المربوطة تظهر بعلامة تحذير برتقالية
- يتم عرض رقم الحساب واسمه عند الربط

## الميزات الرئيسية

### 1. واجهة بسيطة
- صفحة واحدة تحتوي على جميع الحسابات الرئيسية
- قوائم منسدلة لاختيار الحسابات من الدليل
- حفظ جميع التغييرات بنقرة واحدة

### 2. مراقبة الحالة
- عرض حالة كل حساب (مربوط/غير مربوط)
- إحصائيات في أسفل الصفحة
- رسائل تأكيد عند الحفظ

### 3. التحقق من البيانات
- التأكد من وجود الحساب في الدليل قبل الربط
- منع الربط بحسابات غير موجودة
- رسائل خطأ واضحة

### 4. الأداء المحسن
- فهارس على الحقول المهمة
- استعلامات محسنة
- تحديث تلقائي للبيانات

## التكامل مع النظام

### 1. مع دليل الحسابات
- يتم جلب جميع الحسابات النشطة من الدليل
- عرض رقم الحساب ونوعه ومستواه
- ربط مباشر بجدول `chart_of_accounts`

### 2. مع النظام المحاسبي
- الحسابات المربوطة متاحة للاستخدام في جميع العمليات
- يمكن الرجوع إليها من أي صفحة في النظام
- تحديث تلقائي عند تغيير الربط

## الإحصائيات الحالية

- **إجمالي الحسابات الرئيسية**: 10
- **الحسابات المربوطة**: 0 (جديد)
- **الحسابات غير المربوطة**: 10
- **الحسابات المتاحة في الدليل**: 49

## الأمان والموثوقية

- التحقق من صحة البيانات قبل الحفظ
- منع الربط بحسابات غير موجودة
- تسجيل تاريخ التحديث لكل تغيير
- رسائل خطأ واضحة للمستخدم

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **لا تظهر الحسابات في القائمة**
   - تأكد من وجود حسابات نشطة في دليل الحسابات
   - تحقق من الاتصال بقاعدة البيانات

2. **فشل في الحفظ**
   - تأكد من اختيار حسابات صحيحة
   - تحقق من صلاحيات قاعدة البيانات

3. **الصفحة لا تحمل**
   - تأكد من تشغيل الخادم
   - تحقق من وجود جدول `main_accounts`

## التطوير المستقبلي

### ميزات مقترحة
1. إمكانية إضافة حسابات رئيسية جديدة
2. تصدير/استيراد إعدادات الربط
3. تقارير عن الحسابات المربوطة
4. تاريخ التغييرات

### تحسينات مقترحة
1. بحث في قائمة الحسابات
2. تجميع الحسابات حسب النوع
3. معاينة قبل الحفظ
4. نسخ احتياطي للإعدادات

## الخلاصة

تم إنشاء نظام ربط الحسابات الرئيسية بنجاح! النظام يوفر:

✅ **صفحة واحدة بسيطة** لربط جميع الحسابات الرئيسية  
✅ **10 حسابات رئيسية** جاهزة للربط  
✅ **قوائم منسدلة** لاختيار الحسابات من الدليل  
✅ **حفظ مباشر** لجميع التغييرات  
✅ **مراقبة الحالة** في الوقت الفعلي  
✅ **تكامل كامل** مع النظام المحاسبي  

**النظام جاهز للاستخدام الفوري!** 🎉

---

**تاريخ الإنشاء**: ديسمبر 2024  
**الإصدار**: 1.0  
**الحالة**: مكتمل ✅