{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "xq21oDvuMyY1sGb0p7vJI1GJDKpKaaDDH9ZRy8bpdqU=", "__NEXT_PREVIEW_MODE_ID": "efde1e2ad56b377eb3099098422d38b6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "afdea2f32ee21b1a9b770f39136eb0339ee5ed06fc70ca6571deede5e5ae26f8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8e4a27123111ba9f20c0ab4223d7c2e896fdd80b88b4e2ccffb2b5ca7ab9a63a"}}}, "functions": {}, "sortedMiddleware": ["/"]}