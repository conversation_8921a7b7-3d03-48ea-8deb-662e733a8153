{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NPDsRSbuiQRWxgrJMGenX2YfVgFCUEPkYYRpUmm3V6o=", "__NEXT_PREVIEW_MODE_ID": "95744c6182ddf7985597bc2f808d2eff", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9214f6610d3881e078fab440c0208b088bac493fd9a2b5e89c16291240cb94bc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a77c6d6a704dcb0b2d9df923b81e7163466711b182f2008cb873da5255e63e3e"}}}, "functions": {}, "sortedMiddleware": ["/"]}