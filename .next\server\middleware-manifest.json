{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PhDm/nsdwmaRWDg3ZpNyqD+xb871qFnCbOKxJm1FU8o=", "__NEXT_PREVIEW_MODE_ID": "d4eb5c76e2c83f6222bac15a2d7902d7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cb85df80f8360342e9b25d76fc7e2960bf3c27ec38367a628b4e327553588ca0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e47791dd0558cd92c5b0e7424bd9f81cddceb928d39cc30dfe5e08d9e82bc384"}}}, "functions": {}, "sortedMiddleware": ["/"]}