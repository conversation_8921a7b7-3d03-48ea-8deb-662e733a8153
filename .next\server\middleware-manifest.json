{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "amYyXQ+wLFmZlg/0UALYxxiudYeZg20nWEm2eS7/w2U=", "__NEXT_PREVIEW_MODE_ID": "045c62a13b5263b6c5dc1e5248d15c0c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "222849900d658fa37db90f304b02af46672e639dd822b7870e1b71c12ac0ba94", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7baf703fdf227227666af1680b03288534a10b9fa630c4461f4624c6fc7c1bb7"}}}, "functions": {}, "sortedMiddleware": ["/"]}