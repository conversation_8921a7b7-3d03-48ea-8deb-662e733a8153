{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "amYyXQ+wLFmZlg/0UALYxxiudYeZg20nWEm2eS7/w2U=", "__NEXT_PREVIEW_MODE_ID": "28820f37b7ac549bd6ca24bdd99b9c25", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "66a08f06393c3307988a2c4a945b8360823a659e8096a6410b0854f31b9a31be", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f217db1203f70ce34052e203f8d480b1b110d8ec5b1f332658d14f2ae7a815f5"}}}, "functions": {}, "sortedMiddleware": ["/"]}