{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JbS2zBnHMYgy6gUmmuAB/2RLBGwiHocjLXyvONHRU70=", "__NEXT_PREVIEW_MODE_ID": "2f1745736269a285cf19e63a8f211bc5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b969916d94ad7ed06146ec5dfd4d6921b8dccd192707336ede118b9e7f953e2a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cb0ca24d6d13cff6d671935497ff4c4135723bf71ba6e99dc36d19b6341bde6e"}}}, "functions": {}, "sortedMiddleware": ["/"]}