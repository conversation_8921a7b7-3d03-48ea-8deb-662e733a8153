{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "amYyXQ+wLFmZlg/0UALYxxiudYeZg20nWEm2eS7/w2U=", "__NEXT_PREVIEW_MODE_ID": "c15f371a33e6e9ce8f6cd04d21b75f6f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3a7f73da2e57f37b8d47b99f4180d2cf3dc5dc640b94cc5b66ca244715f27d9b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8fbf76e39e411feddde7a10618bd4b0000ff390deab9acd6b597450031be52ee"}}}, "functions": {}, "sortedMiddleware": ["/"]}