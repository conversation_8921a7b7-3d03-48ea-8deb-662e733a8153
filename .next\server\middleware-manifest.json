{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PhDm/nsdwmaRWDg3ZpNyqD+xb871qFnCbOKxJm1FU8o=", "__NEXT_PREVIEW_MODE_ID": "3a537fc794d51b58995b2338a7661af9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "101fe823a78aa5c77346e13d87903a64e51bbe06add8261cedfa7040d438a03c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "54498e72d2ac6a3c2d1c47c9ae3bb53a9386491410505d19b183da2d6115f262"}}}, "functions": {}, "sortedMiddleware": ["/"]}