'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/hooks/useAuth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { VoiceInput } from '@/components/ui/voice-input'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Calendar,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  Clock,
  User,
  FileText,
  Crown
} from 'lucide-react'

interface Follow {
  id: number
  case_id: number
  case_number: string
  case_title: string
  client_name: string
  service_id: number
  service_name: string
  report: string
  date_field: string
  status: 'pending' | 'ready'
  user_id: number
  user_name?: string
  next_hearing_date?: string
  earned_amount: number
  is_approved: boolean
  approved_by?: number
  approved_date?: string
  created_date: string
}

interface Issue {
  id: number
  case_number: string
  title: string
  client_name: string
  court_name: string
  issue_type: string
  status: string
  amount: number
}

interface Service {
  id: number
  name: string
  lineage_name?: string
}

interface ServiceAllocation {
  case_distribution_id: number
  service_distribution_id: number
  case_id: number
  case_number: string
  case_title: string
  total_case_amount: number
  admin_amount: number
  remaining_amount: number
  service_id: number
  service_name: string
  lawyer_id: number
  percentage: number
  allocated_amount: number
  total_earned: number
  remaining_amount_for_service: number
  can_earn: boolean
}

interface Hearing {
  id: number
  issue_id: number
  hearing_date: string
  hearing_time: string
  court_name: string
  hearing_type: string
}

function FollowsPageContent() {
  // استخدام hook المصادقة
  const { 
    user: currentUser, 
    canAddFollows, 
    canEditFollows, 
    canDeleteFollows, 
    canApproveFollows 
  } = useAuth()

  const [follows, setFollows] = useState<Follow[]>([])
  const [issues, setIssues] = useState<Issue[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [hearings, setHearings] = useState<Hearing[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalType, setModalType] = useState<'add' | 'edit' | 'view'>('add')
  const [editingFollow, setEditingFollow] = useState<Follow | null>(null)
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null)
  const [serviceAllocation, setServiceAllocation] = useState<ServiceAllocation | null>(null)
  const [isLoadingAllocation, setIsLoadingAllocation] = useState(false)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // جلب البيانات عند تحميل المستخدم
  useEffect(() => {
    if (currentUser) {
      fetchFollows()
      fetchUserIssues()
      fetchServices()
      fetchHearings()
    }
  }, [currentUser])

  // بيانات النموذج
  const [formData, setFormData] = useState({
    case_id: '',
    service_id: '',
    report: '',
    date_field: new Date().toISOString().split('T')[0],
    status: 'pending' as 'pending' | 'ready',
    next_hearing_date: ''
  })

  const fetchFollows = async () => {
    setIsLoading(true)
    setDbError(null)

    try {
      const response = await fetch(`/api/follows?user_id=${currentUser?.id || 1}`)
      const result = await response.json()

      if (result.success) {
        setFollows(result.data)
      } else {
        setDbError(result.error || 'فشل في جلب بيانات المتابعات')
        setFollows([])
      }
    } catch (error) {
      console.error('Network error:', error)
      setDbError('فشل في الاتصال بقاعدة البيانات')
      setFollows([])
    } finally {
      setIsLoading(false)
    }
  }

  const fetchUserIssues = async () => {
    try {
      const response = await fetch(`/api/follows/user-issues?user_id=${currentUser?.id || 1}`)
      const result = await response.json()
      if (result.success) {
        setIssues(result.data)
      }
    } catch (error) {
      console.error('Error fetching user issues:', error)
    }
  }

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/services')
      const result = await response.json()
      if (result.success) {
        setServices(result.data)
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    }
  }

  const fetchHearings = async (issueId?: number) => {
    try {
      const url = issueId ? `/api/hearings?issue_id=${issueId}` : '/api/hearings'
      const response = await fetch(url)
      const result = await response.json()
      if (result.success) {
        setHearings(result.data)
      }
    } catch (error) {
      console.error('Error fetching hearings:', error)
    }
  }

  const [allocationError, setAllocationError] = useState<string | null>(null)

  const fetchServiceAllocation = async (caseId: string, serviceId: string) => {
    if (!caseId || !serviceId) return

    setIsLoadingAllocation(true)
    setAllocationError(null)
    try {
      const url = `/api/follows/service-allocation?case_id=${caseId}&service_id=${serviceId}&lawyer_id=${currentUser?.id || 1}`

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${currentUser?.token}`
        }
      })
      const result = await response.json()

      if (result.success) {
        setServiceAllocation(result.data)
      } else {
        setServiceAllocation(null)
        setAllocationError(result.error || 'خطأ في جلب بيانات التوزيع')
      }
    } catch (error) {
      console.error('Error fetching service allocation:', error)
      setServiceAllocation(null)
      setAllocationError('خطأ في الاتصال بالخادم')
    } finally {
      setIsLoadingAllocation(false)
    }
  }

  // تم نقل هذا إلى useEffect الذي يعتمد على currentUser

  // دوال التعامل مع النموذج
  const handleAddNew = () => {
    const today = new Date().toISOString().split('T')[0]
    setFormData({
      case_id: '',
      service_id: '',
      report: '',
      date_field: today,
      status: 'pending',
      next_hearing_date: today
    })
    setSelectedIssue(null)
    setServiceAllocation(null)
    setAllocationError(null)
    setModalType('add')
    setIsModalOpen(true)
  }

  const handleEdit = (follow: Follow) => {
    if (!canEditFollows) {
      alert('غير مخول لتعديل المتابعات. تحتاج إلى صلاحية إدارة المتابعات.')
      return
    }

    setFormData({
      case_id: follow.case_id.toString(),
      service_id: follow.service_id.toString(),
      report: follow.report,
      date_field: follow.date_field,
      status: follow.status,
      next_hearing_date: follow.next_hearing_date || ''
    })
    setEditingFollow(follow)
    setSelectedIssue(issues.find(i => i.id === follow.case_id) || null)
    setModalType('edit')
    setIsModalOpen(true)
  }

  const handleIssueChange = (issueId: string) => {
    const issue = issues.find(i => i.id.toString() === issueId)
    setSelectedIssue(issue || null)
    setServiceAllocation(null) // إعادة تعيين معلومات التوزيع

    setFormData(prev => {
      const newFormData = { ...prev, case_id: issueId }

      // جلب الجلسات الخاصة بهذه القضية
      if (issueId) {
        fetchHearings(parseInt(issueId))
      }

      // جلب معلومات توزيع الخدمة إذا كانت الخدمة محددة
      if (issueId && newFormData.service_id) {
        setTimeout(() => {
          fetchServiceAllocation(issueId, newFormData.service_id)
        }, 100)
      }

      return newFormData
    })
  }

  const handleServiceChange = (serviceId: string) => {
    setFormData(prev => {
      const newFormData = { ...prev, service_id: serviceId }

      // جلب معلومات توزيع الخدمة إذا كانت القضية محددة
      if (newFormData.case_id && serviceId) {
        // استخدام setTimeout لضمان تحديث state أولاً
        setTimeout(() => {
          fetchServiceAllocation(newFormData.case_id, serviceId)
        }, 100)
      }

      return newFormData
    })

    setServiceAllocation(null) // إعادة تعيين معلومات التوزيع
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // إعداد بيانات المتابعة
      const dataToSubmit = {
        ...formData,
        case_id: parseInt(formData.case_id),
        service_id: parseInt(formData.service_id),
        user_id: currentUser?.id || 1
      }

      if (modalType === 'edit' && editingFollow) {
        dataToSubmit.id = editingFollow.id
      }

      // إضافة الجلسة الجديدة إذا تم تحديد تاريخ
      let hearingId = null
      if (formData.next_hearing_date && formData.case_id && selectedIssue) {
        try {
          const hearingData = {
            issue_id: parseInt(formData.case_id),
            hearing_date: formData.next_hearing_date,
            court_name: selectedIssue.court_name || null,
            hearing_type: 'جلسة قادمة'
          }

          const hearingResponse = await fetch('/api/hearings', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(hearingData)
          })

          const hearingResult = await hearingResponse.json()
          if (hearingResult.success) {
            hearingId = hearingResult.data.id
            console.log('تم إضافة الجلسة بنجاح:', hearingResult.data)
          } else {
            console.warn('فشل في إضافة الجلسة:', hearingResult.error)
          }
        } catch (hearingError) {
          console.warn('خطأ في إضافة الجلسة:', hearingError)
        }
      }

      // إضافة معرف الجلسة إلى بيانات المتابعة
      dataToSubmit.next_hearing_id = hearingId

      // حفظ المتابعة
      const url = '/api/follows'
      const method = modalType === 'edit' ? 'PUT' : 'POST'

      console.log('إرسال المتابعة:', {
        url,
        method,
        user: currentUser?.username,
        token: currentUser?.token,
        dataToSubmit
      })

      const response = await fetch(url, {
        method,
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${currentUser?.token}`
        },
        body: JSON.stringify(dataToSubmit)
      })

      const result = await response.json()

      if (result.success) {
        setIsModalOpen(false)
        fetchFollows()
        fetchHearings() // تحديث قائمة الجلسات
        
        if (hearingId) {
          alert('تم حفظ المتابعة وإضافة الجلسة الجديدة بنجاح')
        }
      } else {
        setDbError(result.error)
      }
    } catch (error) {
      console.error('Error submitting follow:', error)
      setDbError('فشل في حفظ البيانات')
    }
  }

  const handleVoiceTranscript = (transcript: string) => {
    // إضافة النص المحول من الصوت إلى مربع التقرير
    setFormData(prev => ({
      ...prev,
      report: prev.report ? `${prev.report} ${transcript}` : transcript
    }))
  }

  const handleDelete = async (id: number) => {
    if (!canDeleteFollows) {
      alert('غير مخول لحذف المتابعات. تحتاج إلى صلاحية إدارة المتابعات.')
      return
    }

    if (confirm('هل أنت متأكد من حذف هذه المتابعة؟')) {
      try {
        const response = await fetch(`/api/follows?id=${id}&user_id=${currentUser?.id || 1}`, {
          method: 'DELETE'
        })

        const result = await response.json()
        if (result.success) {
          fetchFollows()
        } else {
          setDbError(result.error)
        }
      } catch (error) {
        console.error('Error deleting follow:', error)
        setDbError('فشل في حذف المتابعة')
      }
    }
  }

  const filteredFollows = follows.filter(follow =>
    (follow.case_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (follow.case_title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (follow.client_name || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'ready': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'معلق'
      case 'ready': return 'جاهز'
      default: return 'غير محدد'
    }
  }

  // الإحصائيات
  const stats = {
    total: filteredFollows.length,
    pending: filteredFollows.filter(f => f.status === 'pending').length,
    ready: filteredFollows.filter(f => f.status === 'ready').length
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Calendar className="h-8 w-8 mr-3 text-blue-600" />
              إدارة المتابعات
            </h1>
            <p className="text-gray-600 mt-1">متابعة المهام والمواعيد المتعلقة بالقضايا</p>
          </div>

          <div className="flex items-center gap-4">
            {canAddFollows ? (
              <Button onClick={handleAddNew} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة متابعة جديدة
              </Button>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center">
                  <div className="text-yellow-600 mr-2">🔒</div>
                  <div>
                    <div className="font-medium text-yellow-800 text-sm">صلاحية محدودة</div>
                    <div className="text-yellow-700 text-xs">تحتاج إلى صلاحية إضافة المتابعات</div>
                  </div>
                </div>
              </div>
            )}
            
            {/* عرض معلومات المستخدم والصلاحيات */}
            {currentUser && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-blue-600 mr-2" />
                  <div>
                    <div className="font-medium text-blue-800 text-sm">
                      {currentUser.name} ({currentUser.role_display_name})
                    </div>
                    <div className="text-blue-700 text-xs">
                      الصلاحيات: {currentUser?.permissions?.length > 0 ? currentUser.permissions.join(', ') : 'لا توجد صلاحيات'}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>



        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
                  <div className="text-sm text-gray-600">إجمالي المتابعات</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.pending}</div>
                  <div className="text-sm text-gray-600">معلق</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <FileText className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stats.ready}</div>
                  <div className="text-sm text-gray-600">جاهز</div>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>

        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في المتابعات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              قائمة المتابعات ({filteredFollows.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-3 font-semibold">رقم القضية</th>
                    <th className="text-right p-3 font-semibold">عنوان القضية</th>
                    <th className="text-right p-3 font-semibold">الموكل</th>
                    <th className="text-right p-3 font-semibold">نوع الخدمة</th>
                    <th className="text-right p-3 font-semibold">التقرير</th>
                    <th className="text-center p-3 font-semibold">التاريخ</th>
                    <th className="text-center p-3 font-semibold">الجلسة القادمة</th>
                    <th className="text-center p-3 font-semibold">الحالة</th>
                    <th className="text-center p-3 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredFollows.map((follow) => (
                    <tr key={follow.id} className="border-b hover:bg-gray-50">
                      <td className="p-3 font-medium text-blue-600">{follow.case_number}</td>
                      <td className="p-3">{follow.case_title}</td>
                      <td className="p-3 flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-400" />
                        {follow.client_name}
                      </td>
                      <td className="p-3">
                        <Badge variant="outline">{follow.service_name}</Badge>
                      </td>
                      <td className="p-3 max-w-xs truncate" title={follow.report}>
                        {follow.report}
                      </td>
                      <td className="text-center p-3">
                        <div className="flex items-center justify-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                          {new Date(follow.date_field).toLocaleDateString('ar-SA')}
                        </div>
                      </td>
                      <td className="text-center p-3">
                        {follow.next_hearing_date ? (
                          <div className="text-sm">
                            <div>{new Date(follow.next_hearing_date).toLocaleDateString('ar-SA')}</div>
                            {follow.next_hearing_time && <div className="text-gray-500">{follow.next_hearing_time}</div>}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="text-center p-3">
                        <Badge className={getStatusColor(follow.status)}>
                          {getStatusText(follow.status)}
                        </Badge>
                      </td>
                      <td className="text-center p-3">
                        <div className="flex justify-center space-x-2 space-x-reverse">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          {canEditFollows && (
                            <Button size="sm" variant="outline" onClick={() => handleEdit(follow)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                          {canDeleteFollows && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDelete(follow.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                          {!canEditFollows && !canDeleteFollows && (
                            <div className="text-xs text-gray-500 px-2">
                              🔒 عرض فقط
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* نافذة إضافة/تعديل المتابعة */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {modalType === 'add' ? 'إضافة متابعة جديدة' : 'تعديل المتابعة'}
              </DialogTitle>
              <DialogDescription>
                {modalType === 'add' ? 'أدخل تفاصيل المتابعة الجديدة' : 'تعديل تفاصيل المتابعة'}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {/* اختيار القضية */}
                <div>
                  <Label htmlFor="case_id">القضية *</Label>
                  <Select value={formData.case_id} onValueChange={handleIssueChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر القضية..." />
                    </SelectTrigger>
                    <SelectContent>
                      {issues.map((issue) => (
                        <SelectItem key={issue.id} value={issue.id.toString()}>
                          {issue.case_number} - {issue.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* نوع الخدمة */}
                <div>
                  <Label htmlFor="service_id">نوع الخدمة *</Label>
                  <Select
                    value={formData.service_id}
                    onValueChange={handleServiceChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الخدمة..." />
                    </SelectTrigger>
                    <SelectContent>
                      {services.map((service) => (
                        <SelectItem key={service.id} value={service.id.toString()}>
                          {service.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* عرض معلومات الموكل */}
              {selectedIssue && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm text-gray-600">
                    <strong>الموكل:</strong> {selectedIssue.client_name}
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>المحكمة:</strong> {selectedIssue.court_name}
                  </div>
                  <div className="text-sm text-gray-600">
                    <strong>نوع القضية:</strong> {selectedIssue.issue_type}
                  </div>
                </div>
              )}

              {/* عرض معلومات توزيع الخدمة */}
              {isLoadingAllocation && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    <span className="text-blue-800">جاري تحميل معلومات التوزيع...</span>
                  </div>
                </div>
              )}

              {!isLoadingAllocation && serviceAllocation && (
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-blue-800 mb-2">معلومات توزيع الخدمة</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">الخدمة:</span>
                      <span className="font-medium mr-2">{serviceAllocation.service_name}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">النسبة المخصصة:</span>
                      <span className="font-medium mr-2">{serviceAllocation.percentage}%</span>
                    </div>
                    <div>
                      <span className="text-gray-600">المبلغ المخصص:</span>
                      <span className="font-medium mr-2 text-green-600">
                        {serviceAllocation.allocated_amount.toLocaleString()} ريال
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">المبلغ المكتسب:</span>
                      <span className="font-medium mr-2 text-blue-600">
                        {serviceAllocation.total_earned.toLocaleString()} ريال
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">المبلغ المتبقي:</span>
                      <span className={`font-medium mr-2 ${serviceAllocation.remaining_amount_for_service > 0 ? 'text-orange-600' : 'text-gray-500'}`}>
                        {serviceAllocation.remaining_amount_for_service.toLocaleString()} ريال
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">نوع الخدمة:</span>
                      <span className="font-medium mr-2 text-purple-600">
                        {serviceAllocation.service_type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">حالة الاستحقاق:</span>
                      <span className={`font-medium mr-2 ${serviceAllocation.can_earn ? 'text-green-600' : 'text-red-600'}`}>
                        {serviceAllocation.can_earn ? 'يمكن الاستحقاق' : 'مكتمل الاستحقاق'}
                      </span>
                    </div>
                  </div>
                  {!serviceAllocation.can_earn && (
                    <div className="mt-2 p-2 bg-yellow-100 border border-yellow-300 rounded text-sm text-yellow-800">
                      <strong>تنبيه:</strong> {serviceAllocation.service_type === 'percentage'
                        ? 'تم تسجيل مبلغ لهذه الخدمة مسبقاً. الخدمات ذات النسبة المئوية تسمح بمتابعة واحدة فقط.'
                        : 'تم استحقاق كامل المبلغ المخصص لهذه الخدمة'}
                    </div>
                  )}

                  {serviceAllocation.can_earn && serviceAllocation.service_type && (
                    <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
                      💡 <strong>نوع الخدمة:</strong> {serviceAllocation.service_type === 'percentage'
                        ? 'نسبة مئوية - سيتم تسجيل النسبة كاملة عند إضافة المتابعة (متابعة واحدة فقط مسموحة)'
                        : 'مبلغ ثابت - يمكن إضافة متابعات متعددة، كل متابعة تحصل على المبلغ المخصص'}
                    </div>
                  )}
                </div>
              )}

              {!isLoadingAllocation && !serviceAllocation && formData.case_id && formData.service_id && allocationError && (
                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                  <div className="flex items-center">
                    <div className="text-red-600 mr-2">⚠️</div>
                    <div>
                      <div className="font-medium text-red-800">مشكلة في توزيع الخدمة</div>
                      <div className="text-sm text-red-700 mt-1">
                        {allocationError}
                      </div>
                      <div className="text-xs text-red-600 mt-2">
                        💡 يمكنك الذهاب إلى <a href="/case-distribution" className="underline font-medium">صفحة توزيع القضايا</a> لإضافة أو تعديل التوزيع
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                {/* التاريخ */}
                <div>
                  <Label htmlFor="date_field">التاريخ *</Label>
                  <Input
                    id="date_field"
                    type="date"
                    value={formData.date_field}
                    onChange={(e) => setFormData(prev => ({ ...prev, date_field: e.target.value }))}
                    required
                  />
                </div>

                {/* الحالة */}
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: 'pending' | 'ready') => setFormData(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">معلق</SelectItem>
                      <SelectItem value="ready">جاهز</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* الجلسة القادمة */}
              <div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="next_hearing_date">تاريخ الجلسة القادمة (اختياري)</Label>
                  {formData.next_hearing_date && (
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, next_hearing_date: '' }))}
                      className="text-xs text-red-600 hover:text-red-700 underline"
                    >
                      مسح التاريخ
                    </button>
                  )}
                </div>
                <Input
                  id="next_hearing_date"
                  type="date"
                  value={formData.next_hearing_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, next_hearing_date: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                  placeholder="اختر تاريخ الجلسة القادمة"
                />
                <div className="text-xs text-gray-500 mt-1">
                  💡 افتراضياً: تاريخ اليوم محدد، يمكنك تغييره أو مسحه حسب الحاجة
                </div>
                {formData.next_hearing_date && (
                  <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded mt-2">
                    📅 سيتم إضافة هذه الجلسة تلقائياً إلى جدول الجلسات القادمة للقضية
                  </div>
                )}
              </div>

              {/* التقرير */}
              <div>
                <Label htmlFor="report">التقرير *</Label>
                <div className="relative">
                  <Textarea
                    id="report"
                    value={formData.report}
                    onChange={(e) => setFormData(prev => ({ ...prev, report: e.target.value }))}
                    placeholder="اكتب تقرير المتابعة أو اضغط على الميكروفون للتسجيل الصوتي..."
                    rows={6}
                    required
                    className="pr-12"
                  />
                  <VoiceInput
                    onTranscript={handleVoiceTranscript}
                    language="ar-SA"
                    className="absolute inset-0 pointer-events-none"
                  />
                </div>
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                  إلغاء
                </Button>
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                  {modalType === 'add' ? 'إضافة' : 'تحديث'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  )
}

export default function FollowsPage() {
  // 🚨 إيقاف ProtectedRoute مؤقتاً - السماح لجميع المستخدمين بالوصول
  return <FollowsPageContent />

  // الكود الأصلي (معطل مؤقتاً):
  /*
  return (
    <ProtectedRoute
      userType="user"
      requiredPermissions={['add_follows', 'manage_follows', 'view_cases']}
    >
      <FollowsPageContent />
    </ProtectedRoute>
  )
  */
}
