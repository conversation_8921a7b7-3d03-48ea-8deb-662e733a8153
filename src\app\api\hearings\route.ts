import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع الجلسات القادمة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const issueId = searchParams.get('issue_id')
    
    let whereClause = ''
    let params: any[] = []
    
    if (issueId) {
      whereClause = 'WHERE h.issue_id = $1'
      params = [issueId]
    }
    
    const result = await query(`
      SELECT 
        h.*,
        i.case_number,
        i.title as case_title
      FROM hearings h
      LEFT JOIN issues i ON h.issue_id = i.id
      ${whereClause}
      ORDER BY h.hearing_date ASC
    `, params)
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching hearings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الجلسات' },
      { status: 500 }
    )
  }
}

// POST - إضافة جلسة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      issue_id,
      hearing_date,
      hearing_time,
      court_name,
      hearing_type,
      notes
    } = body

    if (!issue_id || !hearing_date) {
      return NextResponse.json(
        { success: false, error: 'معرف القضية وتاريخ الجلسة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO hearings (issue_id, hearing_date, hearing_time, court_name, hearing_type, notes)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [issue_id, hearing_date, hearing_time, court_name, hearing_type, notes])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الجلسة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating hearing:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الجلسة' },
      { status: 500 }
    )
  }
}
