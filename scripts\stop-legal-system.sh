#!/bin/bash

# سكريبت إيقاف النظام القانوني
# Legal System Stop Script

echo "⏹️  إيقاف النظام القانوني..."

PID_FILE="/home/<USER>/legal-system.pid"

# التحقق من وجود ملف PID
if [ ! -f "$PID_FILE" ]; then
    echo "⚠️  لا يوجد ملف PID، النظام قد يكون متوقف بالفعل"
    
    # البحث عن عمليات npm/node التي تستخدم المنفذ 7443
    PROCESSES=$(lsof -Pi :7443 -sTCP:LISTEN -t 2>/dev/null)
    
    if [ -n "$PROCESSES" ]; then
        echo "🔍 وجدت عمليات تستخدم المنفذ 7443:"
        lsof -Pi :7443 -sTCP:LISTEN
        echo "🛑 إيقاف العمليات..."
        echo $PROCESSES | xargs kill -TERM
        sleep 3
        
        # التحقق مرة أخرى
        REMAINING=$(lsof -Pi :7443 -sTCP:LISTEN -t 2>/dev/null)
        if [ -n "$REMAINING" ]; then
            echo "🔨 إيقاف قسري للعمليات المتبقية..."
            echo $REMAINING | xargs kill -KILL
        fi
        echo "✅ تم إيقاف جميع العمليات"
    else
        echo "✅ لا توجد عمليات تعمل على المنفذ 7443"
    fi
    
    exit 0
fi

# قراءة معرف العملية
SERVER_PID=$(cat "$PID_FILE")

# التحقق من أن العملية تعمل
if ps -p $SERVER_PID > /dev/null 2>&1; then
    echo "🛑 إيقاف العملية (PID: $SERVER_PID)..."
    
    # إيقاف العملية بلطف
    kill -TERM $SERVER_PID
    
    # انتظار حتى 10 ثوان للإيقاف اللطيف
    for i in {1..10}; do
        if ! ps -p $SERVER_PID > /dev/null 2>&1; then
            echo "✅ تم إيقاف النظام بنجاح"
            rm -f "$PID_FILE"
            exit 0
        fi
        echo "⏳ انتظار الإيقاف... ($i/10)"
        sleep 1
    done
    
    # إيقاف قسري إذا لم يتوقف
    echo "🔨 إيقاف قسري للعملية..."
    kill -KILL $SERVER_PID
    
    # التحقق النهائي
    if ps -p $SERVER_PID > /dev/null 2>&1; then
        echo "❌ فشل في إيقاف العملية"
        exit 1
    else
        echo "✅ تم إيقاف النظام قسرياً"
        rm -f "$PID_FILE"
    fi
else
    echo "⚠️  العملية غير موجودة، إزالة ملف PID"
    rm -f "$PID_FILE"
fi

echo "🏁 تم إيقاف النظام القانوني"
