@echo off
chcp 65001 >nul
echo.
echo ================================================
echo    نظام الإدارة القانونية - إعداد Windows
echo    Legal Management System - Windows Setup
echo ================================================
echo.

echo 📋 فحص المتطلبات الأساسية...
echo Checking basic requirements...
echo.

:: فحص Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت! يرجى تثبيت Node.js أولاً
    echo ❌ Node.js not installed! Please install Node.js first
    echo 🔗 https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js مثبت
    node --version
)

:: فحص npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح!
    echo ❌ npm not available!
    pause
    exit /b 1
) else (
    echo ✅ npm متاح
    npm --version
)

:: فحص PostgreSQL
echo 🔍 فحص PostgreSQL...
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ PostgreSQL غير مثبت أو غير متاح في PATH
    echo ⚠️ PostgreSQL not installed or not in PATH
    echo 🔗 https://www.postgresql.org/download/windows/
    echo.
    echo هل تريد المتابعة بدون فحص PostgreSQL؟ (y/n)
    echo Do you want to continue without PostgreSQL check? (y/n)
    set /p continue=
    if /i "%continue%" neq "y" (
        pause
        exit /b 1
    )
) else (
    echo ✅ PostgreSQL مثبت
    psql --version
)

echo.
echo ================================================
echo 📦 تثبيت التبعيات...
echo Installing dependencies...
echo ================================================
echo.

npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات!
    echo ❌ Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت التبعيات بنجاح!
echo ✅ Dependencies installed successfully!
echo.

echo ================================================
echo 🗄️ إعداد قاعدة البيانات...
echo Database setup...
echo ================================================
echo.

echo هل تريد إعداد قاعدة البيانات الآن؟ (y/n)
echo Do you want to setup the database now? (y/n)
set /p setupdb=

if /i "%setupdb%"=="y" (
    echo.
    echo أدخل كلمة مرور PostgreSQL:
    echo Enter PostgreSQL password:
    set /p pgpass=
    
    echo.
    echo 🔄 إنشاء قاعدة البيانات...
    echo Creating database...
    
    set PGPASSWORD=%pgpass%
    createdb -U postgres mohammi
    
    if exist "database_backups\*.sql" (
        echo.
        echo 🔄 استعادة النسخة الاحتياطية...
        echo Restoring backup...
        
        for %%f in (database_backups\*.sql) do (
            psql -U postgres -d mohammi < "%%f"
            goto :backup_restored
        )
        
        :backup_restored
        echo ✅ تم استعادة النسخة الاحتياطية!
        echo ✅ Backup restored successfully!
    ) else if exist "database_backups\*.gz" (
        echo.
        echo 🔄 فك ضغط واستعادة النسخة الاحتياطية...
        echo Extracting and restoring backup...
        
        for %%f in (database_backups\*.gz) do (
            powershell -command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%%f', 'database_backups\temp')}"
            for %%g in (database_backups\temp\*.sql) do (
                psql -U postgres -d mohammi < "%%g"
            )
            rmdir /s /q "database_backups\temp"
            goto :backup_restored2
        )
        
        :backup_restored2
        echo ✅ تم استعادة النسخة الاحتياطية!
        echo ✅ Backup restored successfully!
    ) else (
        echo ⚠️ لم يتم العثور على نسخة احتياطية
        echo ⚠️ No backup file found
    )
    
    set PGPASSWORD=
)

echo.
echo ================================================
echo 🚀 تشغيل النظام...
echo Starting the system...
echo ================================================
echo.

echo النظام سيعمل على المنفذ 7443
echo The system will run on port 7443
echo.
echo 🌐 الرابط: http://localhost:7443
echo 🌐 URL: http://localhost:7443
echo.
echo 👤 بيانات الدخول الافتراضية:
echo 👤 Default login credentials:
echo    المستخدم / Username: admin
echo    كلمة المرور / Password: admin123
echo.

echo هل تريد تشغيل النظام الآن؟ (y/n)
echo Do you want to start the system now? (y/n)
set /p startapp=

if /i "%startapp%"=="y" (
    echo.
    echo 🔄 بدء تشغيل النظام...
    echo Starting system...
    echo.
    echo لإيقاف النظام، اضغط Ctrl+C
    echo To stop the system, press Ctrl+C
    echo.
    npm run dev
) else (
    echo.
    echo ✅ الإعداد مكتمل!
    echo ✅ Setup complete!
    echo.
    echo لتشغيل النظام لاحقاً، استخدم:
    echo To start the system later, use:
    echo    npm run dev
    echo.
)

echo.
echo ================================================
echo 📋 ملخص الإعداد / Setup Summary
echo ================================================
echo ✅ تم تثبيت التبعيات
echo ✅ Dependencies installed
if /i "%setupdb%"=="y" (
    echo ✅ تم إعداد قاعدة البيانات
    echo ✅ Database configured
)
echo 🌐 الرابط: http://localhost:7443
echo 🌐 URL: http://localhost:7443
echo 📖 راجع README_TRANSFER.md للمزيد من التفاصيل
echo 📖 Check README_TRANSFER.md for more details
echo.

pause
