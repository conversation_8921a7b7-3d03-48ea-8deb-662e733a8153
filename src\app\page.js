'use client'

export default function HomePage() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f3f4f6',
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '40px',
        borderRadius: '8px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        textAlign: 'center',
        maxWidth: '500px'
      }}>
        <h1 style={{ 
          fontSize: '32px',
          fontWeight: 'bold',
          color: '#1f2937',
          marginBottom: '20px'
        }}>
          🎉 النظام القانوني يعمل!
        </h1>
        
        <p style={{ 
          fontSize: '18px',
          color: '#6b7280',
          marginBottom: '30px'
        }}>
          تم حل مشكلة 404 بنجاح! 🚀
        </p>
        
        <div style={{ marginBottom: '20px' }}>
          <a 
            href="/login" 
            style={{ 
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: '#3b82f6',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '6px',
              fontSize: '16px',
              fontWeight: '500',
              marginRight: '10px'
            }}
          >
            تسجيل الدخول
          </a>
          
          <a 
            href="/dashboard" 
            style={{ 
              display: 'inline-block',
              padding: '12px 24px',
              backgroundColor: '#10b981',
              color: 'white',
              textDecoration: 'none',
              borderRadius: '6px',
              fontSize: '16px',
              fontWeight: '500'
            }}
          >
            لوحة التحكم
          </a>
        </div>
        
        <div style={{
          backgroundColor: '#f3f4f6',
          padding: '15px',
          borderRadius: '6px',
          fontSize: '14px',
          color: '#6b7280'
        }}>
          <p style={{ margin: 0 }}>
            <strong>بيانات تجريبية:</strong><br />
            المستخدم: admin | كلمة المرور: admin123
          </p>
        </div>
      </div>
    </div>
  )
}
