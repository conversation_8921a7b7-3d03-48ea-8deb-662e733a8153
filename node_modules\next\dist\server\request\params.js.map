{"version": 3, "sources": ["../../../src/server/request/params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticParams(\n          underlyingParams,\n          workStore.route,\n          prerenderStore\n        )\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring exotic params object which will postpone\n      // if you access the fallback params\n      return makeErroringExoticParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeAbortingExoticParams(\n  underlyingParams: Params,\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = makeHangingPromise<Params>(\n    prerenderStore.renderSignal,\n    '`params`'\n  )\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const expression = describeStringPropertyAccess('params', prop)\n          const error = createParamsAccessError(route, expression)\n          abortAndThrowOnSynchronousRequestDataAccess(\n            route,\n            expression,\n            error,\n            prerenderStore\n          )\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "underlyingParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderParams", "createRenderParams", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "makeHangingPromise", "renderSignal", "Promise", "resolve", "hasSomeFallbackParams", "makeAbortingExoticParams", "route", "makeErroringExoticParams", "makeUntrackedExoticParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "cachedParams", "get", "promise", "set", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "error", "createParamsAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "newValue", "value", "writable", "enumerable", "configurable", "augmentedUnderlying", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "store", "scheduleImmediate", "proxiedProperties", "Set", "unproxiedProperties", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "syncIODev", "ReflectAdapter", "delete", "ownKeys", "Reflect", "missingProperties", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "length", "warnForIncompleteEnumeration", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": ";;;;;;;;;;;;;;;;;;IA0DgBA,sBAAsB;eAAtBA;;IA2DAC,qCAAqC;eAArCA;;IAvCHC,6BAA6B;eAA7BA;;IAGGC,0BAA0B;eAA1BA;;IAkBAC,kCAAkC;eAAlCA;;;yBAhGe;kCAMxB;8CAQA;gCACwB;8BAIxB;uCAC4B;0DACyB;2BAC1B;AAiC3B,SAASJ,uBACdK,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAIO,MAAMJ,gCAAgCE;AAGtC,SAASD,2BACdE,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASF,mCACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASL,sCACdI,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMO,iBAAiBL,kDAAoB,CAACC,QAAQ;IACpD,IAAII,kBAAkBA,eAAeH,IAAI,KAAK,aAAa;QACzD,MAAMI,iBAAiBR,UAAUS,mBAAmB;QACpD,IAAID,gBAAgB;YAClB,IAAK,IAAIE,OAAOX,iBAAkB;gBAChC,IAAIS,eAAeG,GAAG,CAACD,MAAM;oBAC3B,4EAA4E;oBAC5E,+EAA+E;oBAC/E,kDAAkD;oBAClD,OAAOE,IAAAA,yCAAkB,EAACL,eAAeM,YAAY,EAAE;gBACzD;YACF;QACF;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOC,QAAQC,OAAO,CAAChB;AACzB;AAEA,SAASM,sBACPN,gBAAwB,EACxBC,SAAoB,EACpBO,cAA8B;IAE9B,MAAMC,iBAAiBR,UAAUS,mBAAmB;IACpD,IAAID,gBAAgB;QAClB,IAAIQ,wBAAwB;QAC5B,IAAK,MAAMN,OAAOX,iBAAkB;YAClC,IAAIS,eAAeG,GAAG,CAACD,MAAM;gBAC3BM,wBAAwB;gBACxB;YACF;QACF;QAEA,IAAIA,uBAAuB;YACzB,mFAAmF;YACnF,IAAIT,eAAeH,IAAI,KAAK,aAAa;gBACvC,qDAAqD;gBACrD,OAAOa,yBACLlB,kBACAC,UAAUkB,KAAK,EACfX;YAEJ;YACA,yDAAyD;YACzD,4EAA4E;YAC5E,gFAAgF;YAChF,oCAAoC;YACpC,OAAOY,yBACLpB,kBACAS,gBACAR,WACAO;QAEJ;IACF;IAEA,qFAAqF;IACrF,OAAOa,0BAA0BrB;AACnC;AAEA,SAASO,mBACPP,gBAAwB,EACxBC,SAAoB;IAEpB,IAAIqB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,CAACvB,UAAUwB,iBAAiB,EAAE;QAC1E,OAAOC,kDACL1B,kBACAC;IAEJ,OAAO;QACL,OAAOoB,0BAA0BrB;IACnC;AACF;AAGA,MAAM2B,eAAe,IAAIC;AAEzB,SAASV,yBACPlB,gBAAwB,EACxBmB,KAAa,EACbX,cAAoC;IAEpC,MAAMqB,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,MAAME,UAAUlB,IAAAA,yCAAkB,EAChCL,eAAeM,YAAY,EAC3B;IAEFa,aAAaK,GAAG,CAAChC,kBAAkB+B;IAEnCE,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,iCAAmB,CAACzB,GAAG,CAACwB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLH,OAAOK,cAAc,CAACP,SAASK,MAAM;gBACnCN;oBACE,MAAMS,aAAaC,IAAAA,0CAA4B,EAAC,UAAUJ;oBAC1D,MAAMK,QAAQC,wBAAwBvB,OAAOoB;oBAC7CI,IAAAA,6DAA2C,EACzCxB,OACAoB,YACAE,OACAjC;gBAEJ;gBACAwB,KAAIY,QAAQ;oBACVX,OAAOK,cAAc,CAACP,SAASK,MAAM;wBACnCS,OAAOD;wBACPE,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOjB;AACT;AAEA,SAASX,yBACPpB,gBAAwB,EACxBS,cAAmC,EACnCR,SAAoB,EACpBO,cAAwD;IAExD,MAAMqB,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMoB,sBAAsB;QAAE,GAAGjD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAM+B,UAAUhB,QAAQC,OAAO,CAACiC;IAChCtB,aAAaK,GAAG,CAAChC,kBAAkB+B;IAEnCE,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,iCAAmB,CAACzB,GAAG,CAACwB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAI3B,eAAeG,GAAG,CAACwB,OAAO;gBAC5BH,OAAOK,cAAc,CAACW,qBAAqBb,MAAM;oBAC/CN;wBACE,MAAMS,aAAaC,IAAAA,0CAA4B,EAAC,UAAUJ;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI5B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/B6C,IAAAA,sCAAoB,EAClBjD,UAAUkB,KAAK,EACfoB,YACA/B,eAAe2C,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,IAAAA,kDAAgC,EAC9Bb,YACAtC,WACAO;wBAEJ;oBACF;oBACAuC,YAAY;gBACd;gBACAd,OAAOK,cAAc,CAACP,SAASK,MAAM;oBACnCN;wBACE,MAAMS,aAAaC,IAAAA,0CAA4B,EAAC,UAAUJ;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI5B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/B6C,IAAAA,sCAAoB,EAClBjD,UAAUkB,KAAK,EACfoB,YACA/B,eAAe2C,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,IAAAA,kDAAgC,EAC9Bb,YACAtC,WACAO;wBAEJ;oBACF;oBACAwB,KAAIY,QAAQ;wBACVX,OAAOK,cAAc,CAACP,SAASK,MAAM;4BACnCS,OAAOD;4BACPE,UAAU;4BACVC,YAAY;wBACd;oBACF;oBACAA,YAAY;oBACZC,cAAc;gBAChB;YACF,OAAO;;gBACHjB,OAAe,CAACK,KAAK,GAAGpC,gBAAgB,CAACoC,KAAK;YAClD;QACF;IACF;IAEA,OAAOL;AACT;AAEA,SAASV,0BAA0BrB,gBAAwB;IACzD,MAAM6B,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUhB,QAAQC,OAAO,CAAChB;IAChC2B,aAAaK,GAAG,CAAChC,kBAAkB+B;IAEnCE,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,iCAAmB,CAACzB,GAAG,CAACwB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHL,OAAe,CAACK,KAAK,GAAGpC,gBAAgB,CAACoC,KAAK;QAClD;IACF;IAEA,OAAOL;AACT;AAEA,SAASL,kDACP1B,gBAAwB,EACxBqD,KAAgB;IAEhB,MAAMxB,eAAeF,aAAaG,GAAG,CAAC9B;IACtC,IAAI6B,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAU,IAAIhB,QAAgB,CAACC,UACnCsC,IAAAA,4BAAiB,EAAC,IAAMtC,QAAQhB;IAGlC,MAAMuD,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CxB,OAAOC,IAAI,CAAClC,kBAAkBmC,OAAO,CAAC,CAACC;QACrC,IAAIC,iCAAmB,CAACzB,GAAG,CAACwB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEqB,oBAAoBC,IAAI,CAACtB;QAC3B,OAAO;YACLmB,kBAAkBI,GAAG,CAACvB;YACpBL,OAAe,CAACK,KAAK,GAAGpC,gBAAgB,CAACoC,KAAK;QAClD;IACF;IAEA,MAAMwB,iBAAiB,IAAIC,MAAM9B,SAAS;QACxCD,KAAIgC,MAAM,EAAE1B,IAAI,EAAE2B,QAAQ;YACxB,IAAI,OAAO3B,SAAS,UAAU;gBAC5B,IACE,uEAAuE;gBACvEmB,kBAAkB3C,GAAG,CAACwB,OACtB;oBACA,MAAMG,aAAaC,IAAAA,0CAA4B,EAAC,UAAUJ;oBAC1D4B,UAAUX,MAAMlC,KAAK,EAAEoB;gBACzB;YACF;YACA,OAAO0B,uBAAc,CAACnC,GAAG,CAACgC,QAAQ1B,MAAM2B;QAC1C;QACA/B,KAAI8B,MAAM,EAAE1B,IAAI,EAAES,KAAK,EAAEkB,QAAQ;YAC/B,IAAI,OAAO3B,SAAS,UAAU;gBAC5BmB,kBAAkBW,MAAM,CAAC9B;YAC3B;YACA,OAAO6B,uBAAc,CAACjC,GAAG,CAAC8B,QAAQ1B,MAAMS,OAAOkB;QACjD;QACAI,SAAQL,MAAM;YACZ,MAAMvB,aAAa;YACnByB,UAAUX,MAAMlC,KAAK,EAAEoB,YAAYkB;YACnC,OAAOW,QAAQD,OAAO,CAACL;QACzB;IACF;IAEAnC,aAAaK,GAAG,CAAChC,kBAAkB4D;IACnC,OAAOA;AACT;AAEA,SAASI,UACP7C,KAAyB,EACzBoB,UAAkB,EAClB8B,iBAAiC;IAEjC,MAAMnE,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAcoE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAerE;QACrBsE,IAAAA,wDAAsC,EAACD;IACzC;IACA,gCAAgC;IAChC,IAAIF,qBAAqBA,kBAAkBI,MAAM,GAAG,GAAG;QACrDC,6BAA6BvD,OAAOoB,YAAY8B;IAClD,OAAO;QACLM,kBAAkBxD,OAAOoB;IAC3B;AACF;AAEA,MAAMoC,oBAAoBC,IAAAA,qFAA2C,EACnElC;AAGF,MAAMgC,+BACJE,IAAAA,qFAA2C,EAACC;AAE9C,SAASnC,wBACPvB,KAAyB,EACzBoB,UAAkB;IAElB,MAAMuC,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAEvC,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASsC,iCACP1D,KAAyB,EACzBoB,UAAkB,EAClB8B,iBAAgC;IAEhC,MAAMS,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAON,CAPM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAEvC,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,gEAAgE,CAAC,GAClE,CAAC,mDAAmD,CAAC,GACrD,GAAGyC,4BAA4BX,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASW,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWR,MAAM;QACvB,KAAK;YACH,MAAM,qBAEL,CAFK,IAAIS,8BAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAED,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWR,MAAM,GAAG,GAAGW,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAEF,UAAU,CAACG,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAEF,UAAU,CAACA,WAAWR,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOU;YACT;IACF;AACF"}