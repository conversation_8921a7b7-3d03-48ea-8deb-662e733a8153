const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
});

async function addAllYemeniGovernorates() {
  const client = await pool.connect();
  
  try {
    console.log('🇾🇪 إضافة جميع المحافظات اليمنية...\n');

    // بدء المعاملة
    await client.query('BEGIN');

    // جميع المحافظات اليمنية الـ 22
    const yemeniGovernorates = [
      { name: 'صنعاء', code: 'SAN', capital: 'صنعاء', region: 'الوسط' },
      { name: 'عدن', code: 'ADE', capital: 'عدن', region: 'الجنوب' },
      { name: 'تعز', code: 'TAI', capital: 'تعز', region: 'الجنوب' },
      { name: 'الحديدة', code: 'HOD', capital: 'الحديدة', region: 'الغرب' },
      { name: 'إب', code: 'IBB', capital: 'إب', region: 'الوسط' },
      { name: 'ذمار', code: 'DHA', capital: 'ذمار', region: 'الوسط' },
      { name: 'حضرموت', code: 'HAD', capital: 'المكلا', region: 'الشرق' },
      { name: 'لحج', code: 'LAH', capital: 'الحوطة', region: 'الجنوب' },
      { name: 'أبين', code: 'ABI', capital: 'زنجبار', region: 'الجنوب' },
      { name: 'شبوة', code: 'SHA', capital: 'عتق', region: 'الشرق' },
      { name: 'المهرة', code: 'MAH', capital: 'الغيضة', region: 'الشرق' },
      { name: 'حجة', code: 'HAJ', capital: 'حجة', region: 'الشمال' },
      { name: 'صعدة', code: 'SAD', capital: 'صعدة', region: 'الشمال' },
      { name: 'عمران', code: 'AMR', capital: 'عمران', region: 'الشمال' },
      { name: 'الجوف', code: 'JOW', capital: 'الحزم', region: 'الشمال' },
      { name: 'مأرب', code: 'MAR', capital: 'مأرب', region: 'الشرق' },
      { name: 'البيضاء', code: 'BAY', capital: 'البيضاء', region: 'الوسط' },
      { name: 'الضالع', code: 'DAL', capital: 'الضالع', region: 'الجنوب' },
      { name: 'ريمة', code: 'RAY', capital: 'الجبين', region: 'الغرب' },
      { name: 'المحويت', code: 'MAH', capital: 'المحويت', region: 'الغرب' },
      { name: 'سقطرى', code: 'SOC', capital: 'حديبو', region: 'الجزر' },
      { name: 'أمانة العاصمة', code: 'CAP', capital: 'صنعاء', region: 'العاصمة' }
    ];

    console.log(`📋 سيتم إضافة ${yemeniGovernorates.length} محافظة يمنية`);

    let addedCount = 0;
    let updatedCount = 0;

    for (const gov of yemeniGovernorates) {
      try {
        // محاولة إدراج المحافظة
        const result = await client.query(`
          INSERT INTO governorates (name, code, is_active, created_date)
          VALUES ($1, $2, true, CURRENT_DATE)
          ON CONFLICT (code) DO UPDATE SET
            name = EXCLUDED.name,
            is_active = EXCLUDED.is_active
          RETURNING id, name, (xmax = 0) AS inserted
        `, [gov.name, gov.code]);

        if (result.rows[0].inserted) {
          addedCount++;
          console.log(`✅ تم إضافة: ${gov.name} (${gov.code}) - العاصمة: ${gov.capital}`);
        } else {
          updatedCount++;
          console.log(`🔄 تم تحديث: ${gov.name} (${gov.code}) - العاصمة: ${gov.capital}`);
        }

      } catch (error) {
        console.error(`❌ خطأ في إضافة ${gov.name}:`, error.message);
      }
    }

    // إتمام المعاملة
    await client.query('COMMIT');

    console.log('\n🎉 تم الانتهاء من إضافة المحافظات اليمنية!');
    console.log('================================');
    console.log(`📊 ملخص العملية:`);
    console.log(`✅ محافظات جديدة: ${addedCount}`);
    console.log(`🔄 محافظات محدثة: ${updatedCount}`);
    console.log(`📍 إجمالي المحافظات: ${yemeniGovernorates.length}`);
    console.log('================================');

    // عرض إحصائيات حسب المنطقة
    console.log('\n🗺️ توزيع المحافظات حسب المنطقة:');
    const regions = {};
    yemeniGovernorates.forEach(gov => {
      if (!regions[gov.region]) regions[gov.region] = 0;
      regions[gov.region]++;
    });

    Object.entries(regions).forEach(([region, count]) => {
      console.log(`${region}: ${count} محافظة`);
    });

    // التحقق من إجمالي المحافظات في قاعدة البيانات
    console.log('\n📊 التحقق من قاعدة البيانات:');
    const totalResult = await client.query('SELECT COUNT(*) as total FROM governorates');
    const activeResult = await client.query('SELECT COUNT(*) as active FROM governorates WHERE is_active = true');
    
    console.log(`📍 إجمالي المحافظات في قاعدة البيانات: ${totalResult.rows[0].total}`);
    console.log(`✅ المحافظات النشطة: ${activeResult.rows[0].active}`);

  } catch (error) {
    // التراجع عن المعاملة في حالة الخطأ
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إضافة المحافظات اليمنية:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addAllYemeniGovernorates().catch(console.error);
