/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/client-accounts/route";
exports.ids = ["app/api/client-accounts/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient-accounts%2Froute&page=%2Fapi%2Fclient-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient-accounts%2Froute&page=%2Fapi%2Fclient-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_mohaminew_src_app_api_client_accounts_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/client-accounts/route.ts */ \"(rsc)/./src/app/api/client-accounts/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_mohaminew_src_app_api_client_accounts_route_ts__WEBPACK_IMPORTED_MODULE_16__]);\nD_mohaminew_src_app_api_client_accounts_route_ts__WEBPACK_IMPORTED_MODULE_16__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/client-accounts/route\",\n        pathname: \"/api/client-accounts\",\n        filename: \"route\",\n        bundlePath: \"app/api/client-accounts/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"D:\\\\mohaminew\\\\src\\\\app\\\\api\\\\client-accounts\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_mohaminew_src_app_api_client_accounts_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/client-accounts/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient-accounts%2Froute&page=%2Fapi%2Fclient-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/client-accounts/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/client-accounts/route.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_database__WEBPACK_IMPORTED_MODULE_1__]);\n_lib_database__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// GET - جلب حسابات العملاء\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const search = searchParams.get('search');\n        const isActive = searchParams.get('is_active');\n        const isVerified = searchParams.get('is_verified');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const offset = (page - 1) * limit;\n        let whereConditions = [\n            '1=1'\n        ];\n        let queryParams = [];\n        let paramIndex = 1;\n        // البحث في اسم المستخدم والبريد الإلكتروني\n        if (search) {\n            whereConditions.push(`(\n        cpa.username ILIKE $${paramIndex} OR \n        cpa.email ILIKE $${paramIndex} OR \n        c.name ILIKE $${paramIndex}\n      )`);\n            queryParams.push(`%${search}%`);\n            paramIndex++;\n        }\n        // تصفية حسب الحالة النشطة\n        if (isActive !== null) {\n            whereConditions.push(`cpa.is_active = $${paramIndex}`);\n            queryParams.push(isActive === 'true');\n            paramIndex++;\n        }\n        // تصفية حسب التحقق\n        if (isVerified !== null) {\n            whereConditions.push(`cpa.is_verified = $${paramIndex}`);\n            queryParams.push(isVerified === 'true');\n            paramIndex++;\n        }\n        const whereClause = whereConditions.join(' AND ');\n        // الاستعلام الرئيسي\n        const accountsQuery = `\n      SELECT \n        cpa.*,\n        c.name as client_name,\n        c.phone as client_phone,\n        c.address as client_address,\n        COUNT(*) OVER() as total_count\n      FROM client_portal_accounts cpa\n      LEFT JOIN clients c ON cpa.client_id = c.id\n      WHERE ${whereClause}\n      ORDER BY cpa.created_date DESC\n      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}\n    `;\n        queryParams.push(limit, offset);\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(accountsQuery, queryParams);\n        // إحصائيات الحسابات\n        const statsQuery = `\n      SELECT \n        COUNT(*) as total_accounts,\n        COUNT(CASE WHEN is_active = true THEN 1 END) as active_accounts,\n        COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_accounts,\n        COUNT(CASE WHEN last_login >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as recent_logins\n      FROM client_portal_accounts cpa\n      LEFT JOIN clients c ON cpa.client_id = c.id\n      WHERE ${whereClause}\n    `;\n        const statsResult = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(statsQuery, queryParams.slice(0, -2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                accounts: result.rows,\n                totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,\n                currentPage: page,\n                totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,\n                statistics: statsResult.rows[0]\n            }\n        });\n    } catch (error) {\n        console.error('خطأ في جلب حسابات العملاء:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب حسابات العملاء'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إنشاء حساب عميل جديد\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { clientId, username, email, password, language = 'ar', timezone = 'Asia/Riyadh', sendWelcomeEmail = true } = body;\n        // التحقق من البيانات المطلوبة\n        if (!clientId || !username || !email || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع البيانات المطلوبة يجب أن تكون موجودة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود العميل\n        const clientCheck = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id, name FROM clients WHERE id = $1', [\n            clientId\n        ]);\n        if (clientCheck.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'العميل غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // التحقق من عدم وجود حساب للعميل\n        const existingAccountCheck = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM client_portal_accounts WHERE client_id = $1', [\n            clientId\n        ]);\n        if (existingAccountCheck.rows.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يوجد حساب للعميل بالفعل'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من عدم تكرار اسم المستخدم والبريد الإلكتروني\n        const duplicateCheck = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT username, email FROM client_portal_accounts WHERE username = $1 OR email = $2', [\n            username,\n            email\n        ]);\n        if (duplicateCheck.rows.length > 0) {\n            const duplicate = duplicateCheck.rows[0];\n            if (duplicate.username === username) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'اسم المستخدم موجود بالفعل'\n                }, {\n                    status: 400\n                });\n            }\n            if (duplicate.email === email) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'البريد الإلكتروني موجود بالفعل'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // تشفير كلمة المرور\n        const saltRounds = 12;\n        const passwordHash = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].hash(password, saltRounds);\n        // إنشاء رمز التحقق\n        const verificationToken = crypto__WEBPACK_IMPORTED_MODULE_3___default().randomBytes(32).toString('hex');\n        // إنشاء الحساب\n        const insertQuery = `\n      INSERT INTO client_portal_accounts (\n        client_id, username, email, password_hash, language, timezone,\n        verification_token, notification_preferences\n      ) VALUES (\n        $1, $2, $3, $4, $5, $6, $7, $8\n      ) RETURNING id, username, email, is_active, is_verified, created_date\n    `;\n        const defaultNotificationPrefs = {\n            email_notifications: true,\n            case_updates: true,\n            document_uploads: true,\n            appointment_reminders: true,\n            invoice_notifications: true\n        };\n        const values = [\n            clientId,\n            username,\n            email,\n            passwordHash,\n            language,\n            timezone,\n            verificationToken,\n            JSON.stringify(defaultNotificationPrefs)\n        ];\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(insertQuery, values);\n        const newAccount = result.rows[0];\n        // إرسال بريد ترحيبي (محاكاة)\n        if (sendWelcomeEmail) {\n            console.log(`إرسال بريد ترحيبي إلى: ${email}`);\n            console.log(`رابط التحقق: /verify-email?token=${verificationToken}`);\n        }\n        // إنشاء إشعار للعميل\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO client_notifications (client_id, title, message, type)\n      VALUES ($1, $2, $3, $4)\n    `, [\n            clientId,\n            'مرحباً بك في بوابة العملاء',\n            'تم إنشاء حسابك بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب.',\n            'success'\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newAccount,\n            message: 'تم إنشاء الحساب بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في إنشاء حساب العميل:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إنشاء حساب العميل'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث حساب عميل\nasync function PUT(request) {\n    try {\n        const body = await request.json();\n        const { id, username, email, isActive, isVerified, language, timezone, notificationPreferences, resetPassword = false, newPassword } = body;\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الحساب مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        let updateFields = [];\n        let values = [\n            id\n        ];\n        let paramIndex = 2;\n        // تحديث اسم المستخدم\n        if (username) {\n            // التحقق من عدم تكرار اسم المستخدم\n            const duplicateCheck = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM client_portal_accounts WHERE username = $1 AND id != $2', [\n                username,\n                id\n            ]);\n            if (duplicateCheck.rows.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'اسم المستخدم موجود بالفعل'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.push(`username = $${paramIndex}`);\n            values.push(username);\n            paramIndex++;\n        }\n        // تحديث البريد الإلكتروني\n        if (email) {\n            // التحقق من عدم تكرار البريد الإلكتروني\n            const duplicateCheck = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('SELECT id FROM client_portal_accounts WHERE email = $1 AND id != $2', [\n                email,\n                id\n            ]);\n            if (duplicateCheck.rows.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'البريد الإلكتروني موجود بالفعل'\n                }, {\n                    status: 400\n                });\n            }\n            updateFields.push(`email = $${paramIndex}`);\n            values.push(email);\n            paramIndex++;\n            // إعادة تعيين حالة التحقق إذا تم تغيير البريد الإلكتروني\n            updateFields.push(`is_verified = false`);\n            updateFields.push(`verification_token = $${paramIndex}`);\n            values.push(crypto__WEBPACK_IMPORTED_MODULE_3___default().randomBytes(32).toString('hex'));\n            paramIndex++;\n        }\n        // تحديث الحالة النشطة\n        if (isActive !== undefined) {\n            updateFields.push(`is_active = $${paramIndex}`);\n            values.push(isActive);\n            paramIndex++;\n        }\n        // تحديث حالة التحقق\n        if (isVerified !== undefined) {\n            updateFields.push(`is_verified = $${paramIndex}`);\n            values.push(isVerified);\n            paramIndex++;\n            if (isVerified) {\n                updateFields.push(`verification_token = NULL`);\n            }\n        }\n        // تحديث اللغة\n        if (language) {\n            updateFields.push(`language = $${paramIndex}`);\n            values.push(language);\n            paramIndex++;\n        }\n        // تحديث المنطقة الزمنية\n        if (timezone) {\n            updateFields.push(`timezone = $${paramIndex}`);\n            values.push(timezone);\n            paramIndex++;\n        }\n        // تحديث إعدادات الإشعارات\n        if (notificationPreferences) {\n            updateFields.push(`notification_preferences = $${paramIndex}`);\n            values.push(JSON.stringify(notificationPreferences));\n            paramIndex++;\n        }\n        // إعادة تعيين كلمة المرور\n        if (resetPassword && newPassword) {\n            const passwordHash = await bcryptjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].hash(newPassword, 12);\n            updateFields.push(`password_hash = $${paramIndex}`);\n            values.push(passwordHash);\n            paramIndex++;\n            updateFields.push(`reset_token = NULL`);\n            updateFields.push(`reset_token_expires = NULL`);\n        }\n        if (updateFields.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا توجد بيانات للتحديث'\n            }, {\n                status: 400\n            });\n        }\n        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);\n        const updateQuery = `\n      UPDATE client_portal_accounts \n      SET ${updateFields.join(', ')}\n      WHERE id = $1\n      RETURNING id, username, email, is_active, is_verified, language, timezone, updated_at\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(updateQuery, values);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الحساب غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result.rows[0],\n            message: 'تم تحديث الحساب بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في تحديث حساب العميل:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث حساب العميل'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف حساب عميل\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف الحساب مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // حذف الجلسات النشطة\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)('DELETE FROM client_sessions WHERE client_id = $1', [\n            parseInt(id)\n        ]);\n        // حذف الحساب\n        const deleteQuery = `\n      DELETE FROM client_portal_accounts \n      WHERE id = $1 \n      RETURNING username, email\n    `;\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.query)(deleteQuery, [\n            parseInt(id)\n        ]);\n        if (result.rows.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'الحساب غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف الحساب بنجاح'\n        });\n    } catch (error) {\n        console.error('خطأ في حذف حساب العميل:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف حساب العميل'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/client-accounts/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeTables: () => (/* binding */ initializeTables),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__]);\npg__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// Database configuration from mohammi.txt\nconst dbConfig = {\n    host: 'localhost',\n    port: 5432,\n    database: 'mohammi',\n    user: 'postgres',\n    password: 'yemen123',\n    ssl: false,\n    connectionTimeoutMillis: 5000,\n    idleTimeoutMillis: 30000,\n    max: 20\n};\n// Create connection pool\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n// Test database connection\nasync function testConnection() {\n    try {\n        const client = await pool.connect();\n        const result = await client.query('SELECT NOW()');\n        client.release();\n        console.log('✅ Database connected successfully:', result.rows[0]);\n        return true;\n    } catch (error) {\n        console.error('❌ Database connection failed:', error);\n        return false;\n    }\n}\n// Generic query function\nasync function query(text, params) {\n    let client;\n    try {\n        client = await pool.connect();\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error('Database query error:', error);\n        console.error('Query:', text);\n        console.error('Params:', params);\n        throw error;\n    } finally{\n        if (client) {\n            client.release();\n        }\n    }\n}\n// Initialize database tables\nasync function initializeTables() {\n    try {\n        // Create clients table\n        await query(`\n      CREATE TABLE IF NOT EXISTS clients (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        address TEXT,\n        id_number VARCHAR(20) UNIQUE,\n        status VARCHAR(20) DEFAULT 'active',\n        cases_count INTEGER DEFAULT 0,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create employees table\n        await query(`\n      CREATE TABLE IF NOT EXISTS employees (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        position VARCHAR(255),\n        department VARCHAR(255),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        address TEXT,\n        id_number VARCHAR(20) UNIQUE,\n        salary DECIMAL(10,2),\n        hire_date DATE,\n        status VARCHAR(20) DEFAULT 'active',\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create issue_types table\n        await query(`\n      CREATE TABLE IF NOT EXISTS issue_types (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        description TEXT,\n        color VARCHAR(50),\n        cases_count INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create issues table\n        await query(`\n      CREATE TABLE IF NOT EXISTS issues (\n        id SERIAL PRIMARY KEY,\n        case_number VARCHAR(50) UNIQUE NOT NULL,\n        title VARCHAR(255) NOT NULL,\n        description TEXT,\n        client_id INTEGER REFERENCES clients(id),\n        client_name VARCHAR(255),\n        court_name VARCHAR(255),\n        issue_type_id INTEGER REFERENCES issue_types(id),\n        issue_type VARCHAR(255),\n        status VARCHAR(50) DEFAULT 'pending',\n        amount DECIMAL(12,2),\n        next_hearing DATE,\n        notes TEXT,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create lineages table (النسب المالية)\n        await query(`\n      CREATE TABLE IF NOT EXISTS lineages (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        management_share DECIMAL(5,2) DEFAULT 0.00,\n        court_share DECIMAL(5,2) DEFAULT 0.00,\n        commission_share DECIMAL(5,2) DEFAULT 0.00,\n        other_share DECIMAL(5,2) DEFAULT 0.00,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create follows table (المتابعات)\n        await query(`\n      CREATE TABLE IF NOT EXISTS follows (\n        id SERIAL PRIMARY KEY,\n        case_id INTEGER REFERENCES issues(id),\n        case_number VARCHAR(50),\n        case_title VARCHAR(255),\n        client_name VARCHAR(255),\n        follow_type VARCHAR(50),\n        description TEXT,\n        due_date DATE,\n        status VARCHAR(50) DEFAULT 'pending',\n        priority VARCHAR(20) DEFAULT 'medium',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create movements table (الحركات المالية)\n        await query(`\n      CREATE TABLE IF NOT EXISTS movements (\n        id SERIAL PRIMARY KEY,\n        case_id INTEGER REFERENCES issues(id),\n        case_number VARCHAR(50),\n        case_title VARCHAR(255),\n        movement_type VARCHAR(20) NOT NULL, -- 'income' or 'expense'\n        category VARCHAR(255),\n        amount DECIMAL(12,2) NOT NULL,\n        description TEXT,\n        date DATE DEFAULT CURRENT_DATE,\n        reference_number VARCHAR(100),\n        status VARCHAR(50) DEFAULT 'pending',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create users table\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        username VARCHAR(100) UNIQUE NOT NULL,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        role VARCHAR(50) DEFAULT 'user',\n        status VARCHAR(20) DEFAULT 'active',\n        last_login TIMESTAMP,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create companies table\n        await query(`\n      CREATE TABLE IF NOT EXISTS companies (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        legal_name VARCHAR(255),\n        registration_number VARCHAR(100),\n        address TEXT,\n        city VARCHAR(100),\n        country VARCHAR(100),\n        phone VARCHAR(20),\n        email VARCHAR(255),\n        website VARCHAR(255),\n        tax_number VARCHAR(50),\n        commercial_register VARCHAR(50),\n        logo_url VARCHAR(500),\n        logo_right_text TEXT,\n        logo_left_text TEXT,\n        logo_image_url VARCHAR(500),\n        established_date DATE,\n        legal_form VARCHAR(100),\n        capital DECIMAL(15,2),\n        description TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create journal_entries table\n        await query(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        entry_number VARCHAR(50) UNIQUE NOT NULL,\n        description TEXT NOT NULL,\n        date DATE DEFAULT CURRENT_DATE,\n        total_debit DECIMAL(12,2) DEFAULT 0.00,\n        total_credit DECIMAL(12,2) DEFAULT 0.00,\n        status VARCHAR(50) DEFAULT 'pending',\n        created_by VARCHAR(255),\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create governorates table\n        await query(`\n      CREATE TABLE IF NOT EXISTS governorates (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        code VARCHAR(10),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create branches table\n        await query(`\n      CREATE TABLE IF NOT EXISTS branches (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        governorate_id INTEGER REFERENCES governorates(id),\n        address TEXT,\n        phone VARCHAR(20),\n        manager_name VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create courts table\n        await query(`\n      CREATE TABLE IF NOT EXISTS courts (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        type VARCHAR(100),\n        governorate_id INTEGER REFERENCES governorates(id),\n        address TEXT,\n        phone VARCHAR(20),\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create announcements table\n        await query(`\n      CREATE TABLE IF NOT EXISTS announcements (\n        id SERIAL PRIMARY KEY,\n        announcement_1 TEXT,\n        announcement_2 TEXT,\n        announcement_3 TEXT,\n        announcement_4 TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create opening_balances table\n        await query(`\n      CREATE TABLE IF NOT EXISTS opening_balances (\n        id SERIAL PRIMARY KEY,\n        account_id INTEGER NOT NULL,\n        debit_balance DECIMAL(15,2) DEFAULT 0,\n        credit_balance DECIMAL(15,2) DEFAULT 0,\n        balance_date DATE NOT NULL,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Create navigation_pages table for smart search\n        await query(`\n      CREATE TABLE IF NOT EXISTS navigation_pages (\n        id SERIAL PRIMARY KEY,\n        page_title VARCHAR(255) NOT NULL,\n        page_url VARCHAR(500) NOT NULL,\n        page_description TEXT,\n        category VARCHAR(100),\n        keywords TEXT,\n        is_active BOOLEAN DEFAULT true,\n        created_date DATE DEFAULT CURRENT_DATE,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Insert default navigation pages\n        await query(`\n      INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords) VALUES\n      ('لوحة التحكم', '/', 'الصفحة الرئيسية للنظام', 'رئيسي', 'لوحة,تحكم,رئيسي,dashboard'),\n      ('العملاء', '/clients', 'إدارة بيانات العملاء', 'إدارة', 'عملاء,clients,زبائن'),\n      ('الموظفين', '/employees', 'إدارة بيانات الموظفين', 'إدارة', 'موظفين,employees,عمال'),\n      ('أنواع القضايا', '/issue-types', 'إدارة أنواع القضايا', 'قضايا', 'أنواع,قضايا,types'),\n      ('القضايا', '/issues', 'إدارة القضايا والدعاوى', 'قضايا', 'قضايا,دعاوى,issues,cases'),\n      ('المتابعات', '/follows', 'متابعة القضايا والمهام', 'قضايا', 'متابعات,follows,مهام'),\n      ('حركة القضايا', '/movements', 'إدارة حركة القضايا', 'مالية', 'حركات,مالية,movements'),\n      ('دليل الحسابات', '/accounting/chart-of-accounts', 'دليل الحسابات المحاسبي', 'محاسبة', 'دليل,حسابات,chart,accounts'),\n      ('سندات الصرف', '/accounting/payment-vouchers', 'إدارة سندات الصرف', 'محاسبة', 'سندات,صرف,payment,vouchers'),\n      ('سندات القبض', '/accounting/receipt-vouchers', 'إدارة سندات القبض', 'محاسبة', 'سندات,قبض,receipt,vouchers'),\n      ('القيود اليومية', '/accounting/journal-entries', 'إدارة القيود اليومية', 'محاسبة', 'قيود,يومية,journal,entries'),\n      ('الأرصدة الافتتاحية', '/accounting/opening-balances', 'إدارة الأرصدة الافتتاحية', 'محاسبة', 'أرصدة,افتتاحية,opening,balances'),\n      ('التقارير المحاسبية', '/accounting/reports', 'التقارير المحاسبية', 'تقارير', 'تقارير,محاسبية,reports'),\n      ('كشف حساب', '/accounting/reports/account-statement', 'كشف حساب تفصيلي', 'تقارير', 'كشف,حساب,statement'),\n      ('تقارير القضايا', '/case-reports', 'تقارير القضايا والدعاوى', 'تقارير', 'تقارير,قضايا,cases'),\n      ('التقارير المالية', '/financial-reports', 'التقارير المالية', 'تقارير', 'تقارير,مالية,financial'),\n      ('تقارير الموظفين', '/employee-reports', 'تقارير الموظفين', 'تقارير', 'تقارير,موظفين,employees'),\n      ('بيانات الشركة', '/company', 'إدارة بيانات الشركة', 'إعدادات', 'شركة,company,بيانات'),\n      ('مراكز التكلفة', '/settings/cost-centers', 'إدارة مراكز التكلفة', 'إعدادات', 'مراكز,تكلفة,cost,centers'),\n      ('الإعلانات', '/settings/announcements', 'إدارة الإعلانات', 'إعدادات', 'إعلانات,announcements'),\n      ('المحافظات', '/governorates', 'إدارة المحافظات', 'إدارة', 'محافظات,governorates'),\n      ('الفروع', '/branches', 'إدارة الفروع', 'إدارة', 'فروع,branches'),\n      ('المحاكم', '/courts', 'إدارة المحاكم', 'إدارة', 'محاكم,courts')\n      ON CONFLICT DO NOTHING\n    `);\n        console.log('✅ All database tables initialized successfully');\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to initialize database tables:', error);\n        throw error;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclient-accounts%2Froute&page=%2Fapi%2Fclient-accounts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclient-accounts%2Froute.ts&appDir=D%3A%5Cmohaminew%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmohaminew&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();