2025/08/22 00:13:22.910 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; 22/8/2025 00:13:22 @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 00:13:22.912 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 00:13:22.918 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 00:13:22.921 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 00:13:22.987 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 00:13:22.987 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 00:13:23.071 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 00:13:23.076 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 00:13:23.167 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 00:13:23.173 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 00:13:23.205 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 00:13:23.206 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 00:13:23.207 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 00:13:23.210 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 00:13:23.250 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 00:13:23.250 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 00:13:23.252 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 00:13:23.253 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 00:13:23.260 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 00:13:23.379 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 00:13:23.379 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 00:13:23.379 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 00:13:23.514 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 00:13:24.369 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.370 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.395 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.421 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.442 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.442 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.448 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:28.380 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 00:13:28.381 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 00:13:28.384 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 00:13:31.915 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/22 00:23:56.522 ; Log ; 29 ; ::1 ; 9900:admin ; System Restart:  @Http Req#9 @Req#8 0s
2025/08/22 00:23:57.053 ; Log ; 13 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/22 00:23:57.054 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/22 00:23:57.054 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/22 00:23:57.108 ; Log ; 13 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/22 00:32:13.875 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 00:32:13.876 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; 22/8/2025 00:32:13 @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 00:32:13.884 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 00:32:13.888 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 00:32:13.969 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 00:32:13.969 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 00:32:14.048 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 00:32:14.055 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 00:32:14.155 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 00:32:14.159 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 00:32:14.188 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 00:32:14.188 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 00:32:14.189 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 00:32:14.191 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 00:32:14.234 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 00:32:14.234 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 00:32:14.238 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 00:32:14.240 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 00:32:14.246 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 00:32:14.368 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 00:32:14.368 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 00:32:14.368 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 00:32:14.369 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 00:32:14.369 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 00:32:14.370 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 00:32:14.370 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 00:32:14.479 ; Log ; 132 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 00:32:15.254 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.255 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.279 ; Log ; 134 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.306 ; Log ; 134 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.326 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.326 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.331 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:19.372 ; Log ; 140 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 00:32:19.374 ; Log ; 140 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 00:32:19.371 ; Log ; 135 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2

2025/08/22 00:38:04.291 ; Error ; 133 ; ::1 ; 9900: ; Failed login attempt @Http Req#3 @Req#2 0s
2025/08/22 00:38:04.292 ; Log ; 133 ; ::1 ; 9900: ; Failed user login attempt: admin @Http Req#3 @Req#2 0s
2025/08/22 00:38:04.292 ; Log ; 133 ; ::1 ; 9900: ; اسم المستخدم أو كلمة السر غير صحيح ( E35 )  @Http Req#3 @Req#2 0s ; #at:UserError
2025/08/22 00:38:13.181 ; Log ; 146 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#4 @Req#3 0s

2025/08/22 01:01:50.948 ; Log ; 132 ; ::1 ; 9900:admin ; System Restart:  @Http Req#8 @Req#7 0s
2025/08/22 01:01:50.962 ; Log ; 136 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#8
2025/08/22 01:01:50.962 ; Log ; 135 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#8
2025/08/22 01:01:50.962 ; Log ; 140 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#8
2025/08/22 01:01:50.970 ; Log ; 136 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#8

2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; 22/8/2025 01:01:51 @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 01:01:51.087 ; Log ; 170 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 01:01:51.091 ; Log ; 170 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 01:01:51.151 ; Log ; 170 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 01:01:51.151 ; Log ; 170 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 01:01:51.228 ; Log ; 170 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 01:01:51.233 ; Log ; 170 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 01:01:51.306 ; Log ; 170 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 01:01:51.312 ; Log ; 170 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 01:01:51.341 ; Log ; 170 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 01:01:51.342 ; Log ; 170 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 01:01:51.342 ; Log ; 170 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 01:01:51.345 ; Log ; 170 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 01:01:51.401 ; Log ; 170 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 01:01:51.401 ; Log ; 170 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 01:01:51.403 ; Log ; 170 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 01:01:51.404 ; Log ; 170 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 01:01:51.414 ; Log ; 170 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 01:01:51.536 ; Log ; 170 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 01:01:51.538 ; Log ; 170 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 01:01:51.539 ; Log ; 170 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 01:01:51.652 ; Log ; 143 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 01:01:56.541 ; Log ; 173 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/08/22 01:01:56.541 ; Log ; 172 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/08/22 01:01:56.544 ; Log ; 173 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1

2025/08/22 01:49:23.586 ; Log ; 125 ;  ; 0000: ; Initiating app shutdown, waiting for worker threads... @Http Req#1
2025/08/22 01:49:23.586 ; Log ; 172 ;  ; 0000: ; Ending threaded queue: DelayedSqlQueue @Http Req#1
2025/08/22 01:49:23.586 ; Log ; 173 ;  ; 0000: ; Ending threaded queue: ScheduledTasksQueue @Http Req#1
2025/08/22 01:49:23.643 ; Log ; 125 ;  ; 0000: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#1

2025/08/22 03:24:14.805 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 03:24:14.808 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 03:24:14.808 ; Log ; 1 ;  ; 0000: ; 22/8/2025 03:24:14 @Http Req#0
2025/08/22 03:24:14.809 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 03:24:14.809 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 03:24:14.809 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 03:24:14.820 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 03:24:14.823 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 03:24:14.920 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 03:24:14.923 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 03:24:15.007 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 03:24:15.015 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 03:24:15.134 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 03:24:15.153 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 03:24:15.249 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 03:24:15.255 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 03:24:15.270 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 03:24:15.285 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 03:24:15.389 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 03:24:15.389 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 03:24:15.390 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 03:24:15.391 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 03:24:15.397 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 03:24:15.615 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 03:24:15.615 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 03:24:15.616 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 03:24:15.616 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 03:24:15.616 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 03:24:15.617 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 03:24:15.618 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 03:24:15.740 ; Log ; 22 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 03:24:17.135 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.136 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.166 ; Log ; 14 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.187 ; Log ; 14 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.214 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.216 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.221 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:20.619 ; Log ; 32 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 03:24:20.620 ; Log ; 33 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 03:24:20.626 ; Log ; 33 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 03:24:25.978 ; Log ; 12 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/22 03:26:23.716 ; Log ; 26 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#5
2025/08/22 03:26:23.716 ; Log ; 33 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#5
2025/08/22 03:26:23.716 ; Log ; 32 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#5
2025/08/22 03:26:23.719 ; Log ; 26 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#5

2025/08/22 04:19:19.325 ; Log ; 49 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; 22/8/2025 04:19:19 @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 04:19:19.331 ; Log ; 49 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 04:19:19.336 ; Log ; 49 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 04:19:19.406 ; Log ; 49 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 04:19:19.406 ; Log ; 49 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 04:19:19.471 ; Log ; 49 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 04:19:19.475 ; Log ; 49 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 04:19:19.560 ; Log ; 49 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 04:19:19.565 ; Log ; 49 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 04:19:19.591 ; Log ; 49 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 04:19:19.591 ; Log ; 49 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 04:19:19.592 ; Log ; 49 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 04:19:19.597 ; Log ; 49 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 04:19:19.650 ; Log ; 49 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 04:19:19.651 ; Log ; 49 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 04:19:19.651 ; Log ; 49 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 04:19:19.652 ; Log ; 49 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 04:19:19.652 ; Log ; 49 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 04:19:19.767 ; Log ; 49 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 04:19:19.767 ; Log ; 49 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 04:19:19.896 ; Log ; 51 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 04:19:20.722 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.722 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.742 ; Log ; 66 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.764 ; Log ; 66 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.775 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.775 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.779 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:24.768 ; Log ; 69 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 04:19:24.769 ; Log ; 70 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 04:19:24.772 ; Log ; 70 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 04:19:28.510 ; Log ; 14 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

