import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب جميع القيود اليومية
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const includeDetails = searchParams.get('include_details') === 'true'

    let sql = `
      SELECT
        je.*,
        c.currency_code,
        c.symbol as currency_symbol,
        cc.center_name as cost_center_name,
        u.username as created_by_username
      FROM journal_entries je
      LEFT JOIN currencies c ON je.currency_id = c.id
      LEFT JOIN cost_centers cc ON je.cost_center_id = cc.id
      LEFT JOIN users u ON je.created_by_user_id = u.id
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    // تصفية حسب الحالة
    if (status && status !== 'all') {
      sql += ` AND je.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    sql += ` ORDER BY je.entry_date DESC, je.entry_number DESC`

    const result = await query(sql, params)
    let entries = result.rows

    // جلب تفاصيل القيود إذا طُلب ذلك
    if (includeDetails && entries.length > 0) {
      const entryIds = entries.map(entry => entry.id)
      const detailsResult = await query(`
        SELECT
          jed.*,
          ca.account_name,
          ca.account_code,
          c.currency_code,
          pm.method_name as payment_method_name,
          cc.center_name as cost_center_name,
          -- إضافة أسماء العملاء والموظفين والموردين
          COALESCE(
            clients.name,
            employees.name,
            suppliers.name,
            ca.account_name
          ) as enhanced_account_name
        FROM journal_entry_details jed
        LEFT JOIN chart_of_accounts ca ON jed.account_id = ca.id
        LEFT JOIN currencies c ON jed.currency_id = c.id
        LEFT JOIN payment_methods pm ON jed.payment_method_id = pm.id
        LEFT JOIN cost_centers cc ON jed.cost_center_id = cc.id
        -- ربط مباشر مع العملاء والموظفين والموردين
        LEFT JOIN clients ON clients.account_id = ca.id
        LEFT JOIN employees ON employees.account_id = ca.id
        LEFT JOIN suppliers ON suppliers.account_id = ca.id
        WHERE jed.journal_entry_id = ANY($1)
        ORDER BY jed.journal_entry_id, jed.line_number
      `, [entryIds])

      // ربط التفاصيل بالقيود
      entries = entries.map(entry => ({
        ...entry,
        details: detailsResult.rows.filter(detail => detail.journal_entry_id === entry.id)
      }))
    }

    return NextResponse.json({
      success: true,
      entries,
      total: entries.length,
      message: 'تم جلب القيود اليومية بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب القيود اليومية:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب القيود اليومية',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة قيد يومي جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      entry_date,
      description,
      total_debit,
      total_credit,
      currency_id = 1,
      cost_center_id,
      case_id,
      case_number,
      reference_number,
      created_by_user_id = 1, // TODO: الحصول على المستخدم الحالي
      created_by_name = 'النظام',
      details = []
    } = body

    // التحقق من صحة البيانات
    if (!entry_date || !description || !details || details.length < 2) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة أو تفاصيل القيد غير كافية'
      }, { status: 400 })
    }

    // التحقق من توازن القيد
    if (Math.abs(total_debit - total_credit) > 0.01) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير متوازن'
      }, { status: 400 })
    }

    // التحقق من صحة تفاصيل القيد
    for (const detail of details) {
      if (!detail.account_id || (detail.debit_amount === 0 && detail.credit_amount === 0)) {
        return NextResponse.json({
          success: false,
          error: 'كل سطر يجب أن يحتوي على حساب ومبلغ صحيح'
        }, { status: 400 })
      }

      if (detail.debit_amount > 0 && detail.credit_amount > 0) {
        return NextResponse.json({
          success: false,
          error: 'لا يمكن أن يحتوي السطر على مبلغ مدين ودائن في نفس الوقت'
        }, { status: 400 })
      }

      // التحقق من وجود الحساب
      const accountCheck = await query(
        'SELECT id, allow_transactions FROM chart_of_accounts WHERE id = $1 AND is_active = true',
        [detail.account_id]
      )

      if (accountCheck.rows.length === 0) {
        return NextResponse.json({
          success: false,
          error: `الحساب ${detail.account_id} غير موجود أو غير نشط`
        }, { status: 400 })
      }

      if (!accountCheck.rows[0].allow_transactions) {
        return NextResponse.json({
          success: false,
          error: `الحساب ${detail.account_id} لا يقبل معاملات`
        }, { status: 400 })
      }
    }

    // بدء المعاملة
    await query('BEGIN')

    try {
      // إنشاء رقم القيد
      const entryNumberResult = await query(`
        SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
        FROM journal_entries
        WHERE entry_number LIKE 'JE%'
      `)

      const nextNumber = entryNumberResult.rows[0].next_number
      const entry_number = `JE${nextNumber.toString().padStart(6, '0')}`

      // إدراج القيد الرئيسي
      const entryResult = await query(`
        INSERT INTO journal_entries (
          entry_number, entry_date, description, total_debit, total_credit,
          currency_id, cost_center_id, case_id, case_number, reference_number,
          created_by_user_id, created_by_name, status
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, 'draft')
        RETURNING *
      `, [
        entry_number, entry_date, description, total_debit, total_credit,
        currency_id, cost_center_id, case_id, case_number, reference_number,
        created_by_user_id, created_by_name
      ])

      const entryId = entryResult.rows[0].id

      // إدراج تفاصيل القيد
      for (const detail of details) {
        await query(`
          INSERT INTO journal_entry_details (
            journal_entry_id, line_number, account_id, debit_amount, credit_amount,
            currency_id, exchange_rate, cost_center_id, payment_method_id,
            description, reference_number, case_id
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        `, [
          entryId, detail.line_number, detail.account_id, detail.debit_amount || 0, detail.credit_amount || 0,
          detail.currency_id || currency_id, detail.exchange_rate || 1, detail.cost_center_id,
          detail.payment_method_id, detail.description, detail.reference_number,
          // في القيود اليومية: حفظ case_id من القيد الرئيسي أو من التفصيل
          detail.case_id || (case_id && case_id !== '0' ? parseInt(case_id) : null)
        ])
      }

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        entry: entryResult.rows[0],
        message: 'تم إنشاء القيد اليومي بنجاح'
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في إنشاء القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث قيد يومي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      entry_date,
      description,
      total_debit,
      total_credit,
      currency_id,
      cost_center_id,
      case_id,
      case_number,
      reference_number,
      status,
      details = []
    } = body

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف القيد مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود القيد
    const existingEntry = await query(
      'SELECT id, status FROM journal_entries WHERE id = $1',
      [id]
    )

    if (existingEntry.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'القيد غير موجود'
      }, { status: 404 })
    }

    // منع تعديل القيود المعتمدة
    if (existingEntry.rows[0].status === 'approved') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن تعديل القيد المعتمد'
      }, { status: 400 })
    }

    // بدء المعاملة
    await query('BEGIN')

    try {
      // تحديث القيد الرئيسي
      const result = await query(`
        UPDATE journal_entries
        SET
          entry_date = $2,
          description = $3,
          total_debit = $4,
          total_credit = $5,
          currency_id = $6,
          cost_center_id = $7,
          case_id = $8,
          case_number = $9,
          reference_number = $10,
          status = COALESCE($11, status),
          updated_date = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `, [
        id, entry_date, description, total_debit, total_credit,
        currency_id, cost_center_id, case_id, case_number, reference_number, status
      ])

      // حذف التفاصيل القديمة وإدراج الجديدة
      if (details.length > 0) {
        await query('DELETE FROM journal_entry_details WHERE journal_entry_id = $1', [id])

        for (const detail of details) {
          await query(`
            INSERT INTO journal_entry_details (
              journal_entry_id, line_number, account_id, debit_amount, credit_amount,
              currency_id, exchange_rate, cost_center_id, payment_method_id,
              description, reference_number
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          `, [
            id, detail.line_number, detail.account_id, detail.debit_amount || 0, detail.credit_amount || 0,
            detail.currency_id || currency_id, detail.exchange_rate || 1, detail.cost_center_id,
            detail.payment_method_id, detail.description, detail.reference_number
          ])
        }
      }

      // تأكيد المعاملة
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        entry: result.rows[0],
        message: 'تم تحديث القيد اليومي بنجاح'
      })

    } catch (error) {
      // إلغاء المعاملة في حالة الخطأ
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('خطأ في تحديث القيد اليومي:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث القيد اليومي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
