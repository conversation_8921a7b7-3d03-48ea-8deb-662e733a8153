import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب تسجيلات الوقت مع إمكانية التصفية
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const employeeId = searchParams.get('employee_id')
    const caseId = searchParams.get('case_id')
    const clientId = searchParams.get('client_id')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const status = searchParams.get('status')
    const billable = searchParams.get('billable')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['1=1']
    let queryParams: any[] = []
    let paramIndex = 1

    // تصفية حسب الموظف
    if (employeeId) {
      whereConditions.push(`te.employee_id = $${paramIndex}`)
      queryParams.push(parseInt(employeeId))
      paramIndex++
    }

    // تصفية حسب القضية
    if (caseId) {
      whereConditions.push(`te.case_id = $${paramIndex}`)
      queryParams.push(parseInt(caseId))
      paramIndex++
    }

    // تصفية حسب العميل
    if (clientId) {
      whereConditions.push(`te.client_id = $${paramIndex}`)
      queryParams.push(parseInt(clientId))
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (startDate) {
      whereConditions.push(`DATE(te.start_time) >= $${paramIndex}`)
      queryParams.push(startDate)
      paramIndex++
    }

    if (endDate) {
      whereConditions.push(`DATE(te.start_time) <= $${paramIndex}`)
      queryParams.push(endDate)
      paramIndex++
    }

    // تصفية حسب الحالة
    if (status) {
      whereConditions.push(`te.status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    // تصفية حسب قابلية الفوترة
    if (billable !== null && billable !== undefined) {
      whereConditions.push(`te.is_billable = $${paramIndex}`)
      queryParams.push(billable === 'true')
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    // الاستعلام الرئيسي
    const timeEntriesQuery = `
      SELECT 
        te.*,
        e.name as employee_name,
        c.name as client_name,
        i.title as case_title,
        i.case_number,
        COUNT(*) OVER() as total_count
      FROM time_entries te
      LEFT JOIN employees e ON te.employee_id = e.id
      LEFT JOIN clients c ON te.client_id = c.id
      LEFT JOIN issues i ON te.case_id = i.id
      WHERE ${whereClause}
      ORDER BY te.start_time DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(timeEntriesQuery, queryParams)

    // حساب الإحصائيات
    const statsQuery = `
      SELECT 
        COUNT(*) as total_entries,
        SUM(duration_minutes) as total_minutes,
        SUM(CASE WHEN is_billable THEN duration_minutes ELSE 0 END) as billable_minutes,
        SUM(billable_amount) as total_billable_amount,
        SUM(CASE WHEN is_billed THEN billable_amount ELSE 0 END) as billed_amount
      FROM time_entries te
      WHERE ${whereClause}
    `

    const statsResult = await query(statsQuery, queryParams.slice(0, -2)) // إزالة limit و offset

    return NextResponse.json({
      success: true,
      data: {
        timeEntries: result.rows,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        statistics: statsResult.rows[0]
      }
    })

  } catch (error) {
    console.error('خطأ في جلب تسجيلات الوقت:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب تسجيلات الوقت' },
      { status: 500 }
    )
  }
}

// POST - إضافة تسجيل وقت جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      caseId,
      clientId,
      employeeId,
      startTime,
      endTime,
      taskDescription,
      taskCategory,
      hourlyRate,
      isBillable,
      notes
    } = body

    // التحقق من البيانات المطلوبة
    if (!employeeId || !startTime || !taskDescription) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // حساب المدة والمبلغ
    let durationMinutes = 0
    let billableAmount = 0

    if (endTime) {
      const start = new Date(startTime)
      const end = new Date(endTime)
      durationMinutes = Math.round((end.getTime() - start.getTime()) / (1000 * 60))
      
      if (hourlyRate && isBillable) {
        billableAmount = (durationMinutes / 60) * hourlyRate
      }
    }

    const insertQuery = `
      INSERT INTO time_entries (
        case_id, client_id, employee_id, start_time, end_time, duration_minutes,
        task_description, task_category, hourly_rate, billable_amount, is_billable, notes
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) RETURNING *
    `

    const values = [
      caseId || null, clientId || null, employeeId, startTime, endTime || null, durationMinutes,
      taskDescription, taskCategory || 'general', hourlyRate || 0, billableAmount, isBillable || true, notes
    ]

    const result = await query(insertQuery, values)

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة تسجيل الوقت بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة تسجيل الوقت:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة تسجيل الوقت' },
      { status: 500 }
    )
  }
}

// PUT - تحديث تسجيل الوقت
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      endTime,
      taskDescription,
      taskCategory,
      hourlyRate,
      isBillable,
      notes,
      status
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف التسجيل مطلوب' },
        { status: 400 }
      )
    }

    // جلب البيانات الحالية
    const currentData = await query('SELECT * FROM time_entries WHERE id = $1', [id])
    if (currentData.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'التسجيل غير موجود' },
        { status: 404 }
      )
    }

    const current = currentData.rows[0]
    
    // حساب المدة والمبلغ الجديد
    let durationMinutes = current.duration_minutes
    let billableAmount = current.billable_amount

    if (endTime && current.start_time) {
      const start = new Date(current.start_time)
      const end = new Date(endTime)
      durationMinutes = Math.round((end.getTime() - start.getTime()) / (1000 * 60))
      
      const rate = hourlyRate !== undefined ? hourlyRate : current.hourly_rate
      const billable = isBillable !== undefined ? isBillable : current.is_billable
      
      if (rate && billable) {
        billableAmount = (durationMinutes / 60) * rate
      }
    }

    const updateQuery = `
      UPDATE time_entries 
      SET 
        end_time = COALESCE($2, end_time),
        duration_minutes = $3,
        task_description = COALESCE($4, task_description),
        task_category = COALESCE($5, task_category),
        hourly_rate = COALESCE($6, hourly_rate),
        billable_amount = $7,
        is_billable = COALESCE($8, is_billable),
        notes = COALESCE($9, notes),
        status = COALESCE($10, status),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `

    const values = [id, endTime, durationMinutes, taskDescription, taskCategory, hourlyRate, billableAmount, isBillable, notes, status]
    const result = await query(updateQuery, values)

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث تسجيل الوقت بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث تسجيل الوقت:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث تسجيل الوقت' },
      { status: 500 }
    )
  }
}

// DELETE - حذف تسجيل الوقت
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف التسجيل مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من عدم فوترة التسجيل
    const checkQuery = 'SELECT is_billed FROM time_entries WHERE id = $1'
    const checkResult = await query(checkQuery, [parseInt(id)])

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'التسجيل غير موجود' },
        { status: 404 }
      )
    }

    if (checkResult.rows[0].is_billed) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف تسجيل تم فوترته' },
        { status: 400 }
      )
    }

    const deleteQuery = 'DELETE FROM time_entries WHERE id = $1 RETURNING id'
    const result = await query(deleteQuery, [parseInt(id)])

    return NextResponse.json({
      success: true,
      message: 'تم حذف تسجيل الوقت بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف تسجيل الوقت:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف تسجيل الوقت' },
      { status: 500 }
    )
  }
}